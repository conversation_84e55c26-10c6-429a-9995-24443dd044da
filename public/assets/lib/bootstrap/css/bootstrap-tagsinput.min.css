.bootstrap-tagsinput{background-color:#fff;border:1px solid #e9e9e9;display:inline-block;padding:4px 6px;margin-bottom:10px;color:#555;vertical-align:middle;border-radius:2px;min-width:100%;max-width:100%;line-height:22px;cursor:text}.bootstrap-tagsinput input{border:0;box-shadow:none;outline:0;background-color:transparent;padding:0;margin:0;width:auto!important;max-width:inherit}.bootstrap-tagsinput input:focus{border:0;box-shadow:none}.bootstrap-tagsinput .tag{display:inline-block;font-size:12px;font-weight:normal;padding:5px;margin:0 2px 0 0;color:white}.bootstrap-tagsinput .tag [data-role="remove"]{margin-left:8px;cursor:pointer}.bootstrap-tagsinput .tag [data-role="remove"]:after{content:"x";font-size:12px;font-weight:bold;padding:0 2px}.bootstrap-tagsinput .tag [data-role="remove"]:hover{box-shadow:inset 0 1px 0 rgba(255,255,255,0.2),0 1px 2px rgba(0,0,0,0.05)}.bootstrap-tagsinput .tag [data-role="remove"]:hover:active{box-shadow:inset 0 3px 5px rgba(0,0,0,0.125)}