@font-face {
    font-family: 'nifty-demo-icons';
    src:    url('fonts/nifty-demo-icons.eot?bbef6n');
    src:    url('fonts/nifty-demo-icons.svg?bbef6n#nifty-demo-icons') format('svg'),
        url('fonts/nifty-demo-icons.eot?bbef6n#iefix') format('embedded-opentype'),
        url('fonts/nifty-demo-icons.ttf?bbef6n') format('truetype'),
        url('fonts/nifty-demo-icons.woff?bbef6n') format('woff');
    font-weight: normal;
    font-style: normal;
}

[class^="demo-pli-"]:before, [class*=" demo-pli-"]:before,
[class^="demo-psi-"]:before, [class*=" demo-psi-"]:before {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'nifty-demo-icons' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    display: inline-block;
    text-decoration: inherit;
    text-align: center;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.demo-pli-add:before {
    content: "\e900";
}
.demo-pli-add-cart:before {
    content: "\e901";
}
.demo-pli-add-user:before {
    content: "\e902";
}
.demo-pli-add-user-plus-star:before {
    content: "\e903";
}
.demo-pli-arrow-down:before {
    content: "\e904";
}
.demo-pli-arrow-left:before {
    content: "\e905";
}
.demo-pli-arrow-left-2:before {
    content: "\e906";
}
.demo-pli-arrow-out-right:before {
    content: "\e907";
}
.demo-pli-arrow-right:before {
    content: "\e908";
}
.demo-pli-arrow-right-2:before {
    content: "\e909";
}
.demo-pli-arrow-up:before {
    content: "\e90a";
}
.demo-pli-bag-coins:before {
    content: "\e90b";
}
.demo-pli-bell:before {
    content: "\e90c";
}
.demo-pli-building:before {
    content: "\e90d";
}
.demo-pli-calendar-4:before {
    content: "\e90e";
}
.demo-pli-camera-2:before {
    content: "\e90f";
}
.demo-pli-check:before {
    content: "\e910";
}
.demo-pli-check2:before {
    content: "\e911";
}
.demo-pli-checked-user:before {
    content: "\e912";
}
.demo-pli-clock:before {
    content: "\e913";
}
.demo-pli-coding:before {
    content: "\e914";
}
.demo-pli-coin:before {
    content: "\e915";
}
.demo-pli-computer-secure:before {
    content: "\e916";
}
.demo-pli-consulting:before {
    content: "\e917";
}
.demo-pli-credit-card-2:before {
    content: "\e918";
}
.demo-pli-cross:before {
    content: "\e919";
}
.demo-pli-data-settings:before {
    content: "\e91a";
}
.demo-pli-data-storage:before {
    content: "\e91b";
}
.demo-pli-download-from-cloud:before {
    content: "\e91c";
}
.demo-pli-download-window:before {
    content: "\e91d";
}
.demo-pli-exclamation:before {
    content: "\e91e";
}
.demo-pli-exclamation-circle:before {
    content: "\e91f";
}
.demo-pli-facebook:before {
    content: "\e920";
}
.demo-pli-facebook-2:before {
    content: "\e921";
}
.demo-pli-file:before {
    content: "\e922";
}
.demo-pli-file-add:before {
    content: "\e923";
}
.demo-pli-file-csv:before {
    content: "\e924";
}
.demo-pli-file-edit:before {
    content: "\e925";
}
.demo-pli-file-excel:before {
    content: "\e926";
}
.demo-pli-file-html:before {
    content: "\e927";
}
.demo-pli-file-jpg:before {
    content: "\e928";
}
.demo-pli-file-text-image:before {
    content: "\e929";
}
.demo-pli-file-txt:before {
    content: "\e92a";
}
.demo-pli-file-word:before {
    content: "\e92b";
}
.demo-pli-file-zip:before {
    content: "\e92c";
}
.demo-pli-find-user:before {
    content: "\e92d";
}
.demo-pli-fire-flame-2:before {
    content: "\e92e";
}
.demo-pli-folder-with-document:before {
    content: "\e92f";
}
.demo-pli-gear:before {
    content: "\e930";
}
.demo-pli-go-top:before {
    content: "\e931";
}
.demo-pli-google-plus:before {
    content: "\e932";
}
.demo-pli-heart-2:before {
    content: "\e933";
}
.demo-pli-home:before {
    content: "\e934";
}
.demo-pli-idea-2:before {
    content: "\e935";
}
.demo-pli-inbox-full:before {
    content: "\e936";
}
.demo-pli-inbox-into:before {
    content: "\e937";
}
.demo-pli-information:before {
    content: "\e938";
}
.demo-pli-instagram:before {
    content: "\e939";
}
.demo-pli-internet:before {
    content: "\e93a";
}
.demo-pli-laptop:before {
    content: "\e93b";
}
.demo-pli-layout-grid:before {
    content: "\e93c";
}
.demo-pli-left-4:before {
    content: "\e93d";
}
.demo-pli-like:before {
    content: "\e93e";
}
.demo-pli-like-2:before {
    content: "\e93f";
}
.demo-pli-location-2:before {
    content: "\e940";
}
.demo-pli-lock-user:before {
    content: "\e941";
}
.demo-pli-love-user:before {
    content: "\e942";
}
.demo-pli-magnifi-glass:before {
    content: "\e943";
}
.demo-pli-mail:before {
    content: "\e944";
}
.demo-pli-mail-attachment:before {
    content: "\e945";
}
.demo-pli-mail-block:before {
    content: "\e946";
}
.demo-pli-mail-favorite:before {
    content: "\e947";
}
.demo-pli-mail-remove:before {
    content: "\e948";
}
.demo-pli-mail-send:before {
    content: "\e949";
}
.demo-pli-mail-unread:before {
    content: "\e94a";
}
.demo-pli-male:before {
    content: "\e94b";
}
.demo-pli-male-female:before {
    content: "\e94c";
}
.demo-pli-map:before {
    content: "\e94d";
}
.demo-pli-map-2:before {
    content: "\e94e";
}
.demo-pli-map-marker:before {
    content: "\e94f";
}
.demo-pli-map-marker-2:before {
    content: "\e950";
}
.demo-pli-medal-2:before {
    content: "\e951";
}
.demo-pli-mine:before {
    content: "\e952";
}
.demo-pli-monitor-2:before {
    content: "\e953";
}
.demo-pli-office:before {
    content: "\e954";
}
.demo-pli-old-telephone:before {
    content: "\e955";
}
.demo-pli-paper-plane:before {
    content: "\e956";
}
.demo-pli-paperclip:before {
    content: "\e957";
}
.demo-pli-plus:before {
    content: "\e958";
}
.demo-pli-printer:before {
    content: "\e959";
}
.demo-pli-question:before {
    content: "\e95a";
}
.demo-pli-question-circle:before {
    content: "\e95b";
}
.demo-pli-recycling:before {
    content: "\e95c";
}
.demo-pli-refresh:before {
    content: "\e95d";
}
.demo-pli-reload-3:before {
    content: "\e95e";
}
.demo-pli-remove:before {
    content: "\e95f";
}
.demo-pli-remove-user:before {
    content: "\e960";
}
.demo-pli-repeat-2:before {
    content: "\e961";
}
.demo-pli-right-4:before {
    content: "\e962";
}
.demo-pli-share:before {
    content: "\e963";
}
.demo-pli-shopping-bag:before {
    content: "\e964";
}
.demo-pli-shopping-basket:before {
    content: "\e965";
}
.demo-pli-shopping-cart:before {
    content: "\e966";
}
.demo-pli-smartphone-3:before {
    content: "\e967";
}
.demo-pli-speech-bubble-2:before {
    content: "\e968";
}
.demo-pli-speech-bubble-3:before {
    content: "\e969";
}
.demo-pli-speech-bubble-4:before {
    content: "\e96a";
}
.demo-pli-speech-bubble-5:before {
    content: "\e96b";
}
.demo-pli-speech-bubble-7:before {
    content: "\e96c";
}
.demo-pli-star:before {
    content: "\e96d";
}
.demo-pli-support:before {
    content: "\e96e";
}
.demo-pli-tablet-2:before {
    content: "\e96f";
}
.demo-pli-tactic:before {
    content: "\e970";
}
.demo-pli-tag:before {
    content: "\e971";
}
.demo-pli-tag-2:before {
    content: "\e972";
}
.demo-pli-tag-3:before {
    content: "\e973";
}
.demo-pli-temperature:before {
    content: "\e974";
}
.demo-pli-twitter:before {
    content: "\e975";
}
.demo-pli-twitter-2:before {
    content: "\e976";
}
.demo-pli-unlike:before {
    content: "\e977";
}
.demo-pli-unlike-2:before {
    content: "\e978";
}
.demo-pli-unlock:before {
    content: "\e979";
}
.demo-pli-upload-to-cloud:before {
    content: "\e97a";
}
.demo-pli-video:before {
    content: "\e97b";
}
.demo-pli-view-list:before {
    content: "\e97c";
}
.demo-pli-wallet-2:before {
    content: "\e97d";
}
.demo-pli-wrench:before {
    content: "\e97e";
}
.demo-psi-add:before {
    content: "\e97f";
}
.demo-psi-arrow-left:before {
    content: "\e980";
}
.demo-psi-arrow-left-2:before {
    content: "\e981";
}
.demo-psi-arrow-right:before {
    content: "\e982";
}
.demo-psi-arrow-right-2:before {
    content: "\e983";
}
.demo-psi-bar-chart:before {
    content: "\e984";
}
.demo-psi-boot-2:before {
    content: "\e985";
}
.demo-psi-building:before {
    content: "\e986";
}
.demo-psi-car-coins:before {
    content: "\e987";
}
.demo-psi-close:before {
    content: "\e988";
}
.demo-psi-computer-secure:before {
    content: "\e989";
}
.demo-psi-consulting:before {
    content: "\e98a";
}
.demo-psi-download-from-cloud:before {
    content: "\e98b";
}
.demo-psi-facebook:before {
    content: "\e98c";
}
.demo-psi-facebook-2:before {
    content: "\e98d";
}
.demo-psi-file:before {
    content: "\e98e";
}
.demo-psi-file-csv:before {
    content: "\e98f";
}
.demo-psi-file-excel:before {
    content: "\e990";
}
.demo-psi-file-html:before {
    content: "\e991";
}
.demo-psi-file-jpg:before {
    content: "\e992";
}
.demo-psi-file-text-image:before {
    content: "\e993";
}
.demo-psi-file-txt:before {
    content: "\e994";
}
.demo-psi-file-word:before {
    content: "\e995";
}
.demo-psi-file-zip:before {
    content: "\e996";
}
.demo-psi-folder-organizing:before {
    content: "\e997";
}
.demo-psi-gear:before {
    content: "\e998";
}
.demo-psi-gear-2:before {
    content: "\e999";
}
.demo-psi-google-plus:before {
    content: "\e99a";
}
.demo-psi-happy:before {
    content: "\e99b";
}
.demo-psi-heart-2:before {
    content: "\e99c";
}
.demo-psi-home:before {
    content: "\e99d";
}
.demo-psi-idea-2:before {
    content: "\e99e";
}
.demo-psi-inbox-full:before {
    content: "\e99f";
}
.demo-psi-information:before {
    content: "\e9a0";
}
.demo-psi-instagram:before {
    content: "\e9a1";
}
.demo-psi-internet:before {
    content: "\e9a2";
}
.demo-psi-left-4:before {
    content: "\e9a3";
}
.demo-psi-like:before {
    content: "\e9a4";
}
.demo-psi-lock-2:before {
    content: "\e9a5";
}
.demo-psi-mail:before {
    content: "\e9a6";
}
.demo-psi-mail-favorite:before {
    content: "\e9a7";
}
.demo-psi-mail-send:before {
    content: "\e9a8";
}
.demo-psi-mail-unread:before {
    content: "\e9a9";
}
.demo-psi-male:before {
    content: "\e9aa";
}
.demo-psi-medal-2:before {
    content: "\e9ab";
}
.demo-psi-office:before {
    content: "\e9ac";
}
.demo-psi-paperclip:before {
    content: "\e9ad";
}
.demo-psi-pen-5:before {
    content: "\e9ae";
}
.demo-psi-printer:before {
    content: "\e9af";
}
.demo-psi-receipt-4:before {
    content: "\e9b0";
}
.demo-psi-recycling:before {
    content: "\e9b1";
}
.demo-psi-remove:before {
    content: "\e9b2";
}
.demo-psi-repair:before {
    content: "\e9b3";
}
.demo-psi-repeat-2:before {
    content: "\e9b4";
}
.demo-psi-right-4:before {
    content: "\e9b5";
}
.demo-psi-share:before {
    content: "\e9b6";
}
.demo-psi-sidebar-window:before {
    content: "\e9b7";
}
.demo-psi-speech-bubble-2:before {
    content: "\e9b8";
}
.demo-psi-speech-bubble-3:before {
    content: "\e9b9";
}
.demo-psi-speech-bubble-4:before {
    content: "\e9ba";
}
.demo-psi-speech-bubble-5:before {
    content: "\e9bb";
}
.demo-psi-speech-bubble-comic-2:before {
    content: "\e9bc";
}
.demo-psi-split-vertical-2:before {
    content: "\e9bd";
}
.demo-psi-star:before {
    content: "\e9be";
}
.demo-psi-tactic:before {
    content: "\e9bf";
}
.demo-psi-tag:before {
    content: "\e9c0";
}
.demo-psi-tag-2:before {
    content: "\e9c1";
}
.demo-psi-tag-3:before {
    content: "\e9c2";
}
.demo-psi-thunder:before {
    content: "\e9c3";
}
.demo-psi-twitter:before {
    content: "\e9c4";
}
.demo-psi-twitter-2:before {
    content: "\e9c5";
}
.demo-psi-unlike:before {
    content: "\e9c6";
}
.demo-psi-upload-to-cloud:before {
    content: "\e9c7";
}
.demo-psi-warning-window:before {
    content: "\e9c8";
}

