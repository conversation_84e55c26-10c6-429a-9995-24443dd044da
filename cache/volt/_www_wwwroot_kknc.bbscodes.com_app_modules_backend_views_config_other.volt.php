<div class="tab-base">

	<?php $this->flashSession->output(); ?>
	<!--Nav Tabs-->
	<ul class="nav nav-tabs">
		<li class="active">
			<a data-toggle="tab" href="#">其他设置</a>
		</li>
	</ul>

	<!--Tabs Content-->
	<div class="tab-content">
		<div class="tab-pane fade active in">
			<div class="panel">
				
				
				
				<div class="panel-body">
					<form class="form-horizontal form-padding" method = 'post'>
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">每天可提现</label>
							<div class="col-xs-12 col-sm-4 col-md-4 col-lg-4">
								<input type="text" name="maxWithdraw" placeholder="设置每个用户每天最大可提现金额，0表示无限" class="form-control" value="<?php if (isset($configInfo['maxWithdraw'])) { ?><?= $configInfo['maxWithdraw'] ?><?php } ?>">
							</div>
						</div>
						<?php if ($hostType == 'kk') { ?>
						<div  class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">提现教程文档</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<?php if (isset($configInfo['course'])) { ?>
								<?php echo Dhc\Component\MyTags::tpl_ueditor('course',$configInfo["course"])?>
								<?php } else { ?>
								<?php  echo Dhc\Component\MyTags::tpl_ueditor('course')?>
								<?php } ?>
							</div>
						</div>
						<div  class="form-group">
                            <label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">支付提示语</label>
                            <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                <?php if (isset($configInfo['course'])) { ?>
                                <?php echo Dhc\Component\MyTags::tpl_ueditor('payInfo',$configInfo["payInfo"])?>
                                <?php } else { ?>
                                <?php  echo Dhc\Component\MyTags::tpl_ueditor('payInfo')?>
                                <?php } ?>
                            </div>
                        </div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">提现最低房屋等级</label>
							<div class="col-xs-12 col-sm-4 col-md-4 col-lg-4">
								<input type="text" name="minWithdrawGrade" placeholder="" class="form-control" value="<?php if (isset($configInfo['minWithdrawGrade'])) { ?><?= $configInfo['minWithdrawGrade'] ?><?php } ?>">
							</div>
						</div>

						<?php } ?>
						<?php if ($hostType == 'chuangjin') { ?>
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">交易自动撤销</label>
							<div class="col-xs-12 col-sm-4 col-md-4 col-lg-4">
								<select name="isCrontab" class="form-control">
									<option value="1" <?php if ($configInfo != '') { ?><?php if ($configInfo['isCrontab'] == 1) { ?>selected<?php } ?><?php } ?>>启用</option>
									<option value="0" <?php if ($configInfo != '') { ?><?php if ($configInfo['isCrontab'] == 0) { ?>selected<?php } ?><?php } ?>>禁用</option>
								</select>
							</div>
						</div>
						<?php } ?>
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label"></label>
							<div class="col-xs-12 col-sm-4 col-md-4 col-lg-4">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
