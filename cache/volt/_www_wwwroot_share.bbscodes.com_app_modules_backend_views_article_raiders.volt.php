
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="<?php if ($show === 'list') { ?>active<?php } else { ?> <?php } ?>">
				<a data-toggle="tab" href="#demo-lft-tab-1" aria-expanded="<?php if ($show === 'list') { ?>true<?php } else { ?>false<?php } ?>">攻略列表</a>
			</li>
			<li class="<?php if ($show === 'edit') { ?>active<?php } else { ?> <?php } ?>">
				<a data-toggle="tab" href="#demo-lft-tab-2" aria-expanded="<?php if ($show === 'edit') { ?>true<?php } else { ?>false<?php } ?>">攻略审核</a>
			</li>
			<li class="<?php if ($show === 'wait') { ?>active<?php } else { ?> <?php } ?>">
				<a data-toggle="tab" href="#demo-lft-tab-3" aria-expanded="<?php if ($show === 'wait') { ?>true<?php } else { ?>false<?php } ?>">待审核</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if ($show === 'list') { ?> active in <?php } ?>">
				<div class="panel">
					<div class="panel-body">
						
							
								
									
								
							
						
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th class="text-center">id</th>
									<th>攻略标题</th>
									<th>攻略内容</th>
									<th>审核状态</th>
									<th>发表日期</th>
									<th>阅读类型</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
									<?php if (isset($rlist)) { ?>
										<?php foreach ($rlist as $rows) { ?>
											<?php if (!is_scalar($rows)) { ?>
											<?php foreach ($rows as $row) { ?>
									<tr>
										<td><i><?= $row->id ?></i></td>
										<td><?= $row->title ?></td>
										<td><span class="text-muted contentSub"><?= $row->content ?></span></td>
										<td>
											<div class="label label-table label-success"><?php if ($row->status === '1') { ?>审核通过<?php } elseif ($row->status === '-1') { ?>审核未通过<?php } else { ?>待审核<?php } ?></div>
										</td>
										<td><?php echo date('Y-m-d H:i:s',$row->createtime)?></td>
										<td><?php if ($row->type === '1') { ?>付费阅读<?php } else { ?>免费阅读<?php } ?></td>
										<td>
											<a href="<?= $apppath ?>/article/raiders?op=edit&id=<?= $row->id ?>" class="btn btn-default btn-sm" title="编辑"><i class="fa fa-edit"></i></a>
											<a href="<?= $apppath ?>/article/raiders?op=del&id=<?= $row->id ?>" class="btn btn-default btn-sm" title="删除" onclick="window.alert('确定删除？')"><i class="fa fa-trash"></i></a>
										</td>
									</tr>

										
									<?php } ?>
												<?php } ?>
									<?php } ?>
								<?php } ?>
								</tbody>
							</table>
							<div class="panel-body text-center">
								<ul class="pagination">
									<li><a href="<?= $apppath ?>/article/raiders?op=list&page=1" class="demo-pli-arrow-right">首页</a></li>
									<li><a href="<?= $apppath ?>/article/raiders?op=list&page=<?= $rlist->before ?>">上一页</a></li>
									<li><a href="<?= $apppath ?>/article/raiders?op=list&page=<?= $rlist->next ?>">下一页</a></li>
									<li><a href="<?= $apppath ?>/article/raiders?op=list&page=<?= $rlist->total_pages ?>" class="demo-pli-arrow-right">尾页</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div id="demo-lft-tab-2" class="tab-pane fade <?php if ($show === 'edit') { ?> active in <?php } ?>">
				<div class="panel">
					<form class="panel-body form-horizontal form-padding " method="post"  action="<?= $apppath ?>/article/raiders?op=list">
						<!--Static-->
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">攻略类型</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<select class="form-control" id="demo-vs-definput" name="cid">
								<option value="1" <?php if (isset($item->type)) { ?><?php if ($item->type === '1') { ?>selected<?php } ?><?php } ?>>付费攻略</option>
								<option value="0"<?php if (isset($item->type)) { ?><?php if ($item->type === '0') { ?>selected<?php } ?><?php } ?>>免费攻略</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">攻略标题</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input type="text" class="form-control" name="title" value="<?php if (isset($item->title)) { ?><?= $item->title ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label" for="demo-email-input" >文章作者</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input type="text" class="form-control" name="auth" value="<?php if (isset($item->auth)) { ?><?= $item->auth ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">审核</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<select class="form-control" id="demo-vs-definput" name="status">
									<option value="1" <?php if (isset($item->status)) { ?><?php if ($item->status === '1') { ?>selected<?php } ?><?php } ?>>通过</option>
									<option value="-1" <?php if (isset($item->status)) { ?><?php if ($item->status === '-1') { ?>selected<?php } ?><?php } ?>>不通过</option>
									<option value="0" <?php if (isset($item->status)) { ?><?php if ($item->status === '0') { ?>selected<?php } ?><?php } ?>>待审核</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label" for="demo-email-input" >审核结果</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input type="text" placeholder="通过或者不通过原因" class="form-control" name="reason" value="<?php if (isset($item->reason)) { ?><?= $item->reason ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label" for="demo-textarea-input">文章内容</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<?php if (isset($item)) { ?>
									<?php echo Dhc\Component\MyTags::tpl_ueditor('content',$item->content)?>
								<?php } else { ?>
									<?php  echo Dhc\Component\MyTags::tpl_ueditor('content')?>
								<?php } ?>
							</div>
						</div>
						<div class="panel-footer text-left">
							<input type="hidden" value="<?php if (isset($item->id)) { ?><?= $item->id ?><?php } ?>" name="id">
							<button class="btn btn-success" type="submit">确定</button>
						</div>
					</form>
				</div>
			</div>
			<div id="demo-lft-tab-3" class="tab-pane fade <?php if ($show === 'wait') { ?> active in <?php } ?>">
				<div class="panel">
					<div class="panel-body">
						
						
						
						
						
						
						
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th class="text-center">id</th>
									<th>攻略标题</th>
									<th>攻略内容</th>
									<th>审核状态</th>
									<th>发表日期</th>
									<th>阅读类型</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<?php if (isset($wlist)) { ?>
									<?php foreach ($wlist as $rs) { ?>
										<?php if (!is_scalar($rs)) { ?>
											<?php foreach ($rs as $r) { ?>
												<tr>
													<td><i><?= $r->id ?></i></td>
													<td><?= $r->title ?></td>
													<td><span class="text-muted"><i class="demo-pli-clock"></i> <?= $r->content ?></span></td>
													<td>
														<div class="label label-table label-success"><?php if ($r->status === '1') { ?>审核通过<?php } elseif ($r->status === '-1') { ?>审核未通过<?php } else { ?>待审核<?php } ?></div>
													</td>
													<td><?php echo date('Y-m-d H:i:s',$r->createtime)?></td>
													<td><?php if ($r->type === '1') { ?>付费阅读<?php } else { ?>免费阅读<?php } ?></td>
													<td>
														<a href="<?= $apppath ?>/article/raiders?op=edit&id=<?= $r->id ?>" class="btn btn-default btn-sm" title="编辑"><i class="fa fa-edit"></i></a>
														<a href="<?= $apppath ?>/article/raiders?op=del&id=<?= $r->id ?>" class="btn btn-default btn-sm" title="删除" onclick="window.alert('确定删除？')"><i class="fa fa-trash"></i></a>
													</td>
												</tr>
											<?php } ?>
										<?php } ?>
									<?php } ?>
								<?php } ?>
								</tbody>
							</table>
							<div class="panel-body text-center">
								<ul class="pagination">
									<li><a href="<?= $apppath ?>/article/raiders?op=wait&page=1" class="demo-pli-arrow-right">首页</a></li>
									<li><a href="<?= $apppath ?>/article/raiders?op=wait&page=<?= $wlist->before ?>">上一页</a></li>
									<li><a href="<?= $apppath ?>/article/raiders?op=wait&page=<?= $wlist->last ?>">下一页</a></li>
									<li><a href="<?= $apppath ?>/article/raiders?op=wait&page=<?= $wlist->total_pages ?>" class="demo-pli-arrow-right">尾页</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>

		</div>
	</div>
</div>
<!--文章模块结束--!>
<script>
window.onload=function(){
	var contentSubAll=document.getElementsByClassName("contentSub");
    	for(var i=0;i<contentSubAll.length;i++){
    		var tempText=contentSubAll[i].innerHTML;
    		if(tempText.length>=20){
    			tempText=tempText.substr(0,20);
    			contentSubAll[i].innerHTML=tempText+"……";
    		}

    	}
}




</script>

