
<!--文章模块开始-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="<?php if ($op === 'list') { ?>active<?php } ?>">
				<a  href="<?= $apppath ?>/product/plist?op=list" aria-expanded="<?php if ($op === 'list') { ?>true<?php } else { ?>false<?php } ?>">当日行情</a>
			</li>
			<li class="<?php if ($op === 'order') { ?> active <?php } ?>">
				<a  href="<?= $apppath ?>/product/plist?op=order" aria-expanded="<?php if ($op === 'order') { ?>true<?php } else { ?>false<?php } ?>">挂单列表</a>
			</li>
			<li class="<?php if ($op === 'count') { ?> active <?php } else { ?> <?php } ?>">
				<a  href="<?= $apppath ?>/product/plist?op=count" aria-expanded="<?php if ($op === 'count') { ?>true<?php } else { ?>false<?php } ?>">统计中心</a>
			</li>
			
		</ul>
		<div class="tab-content">
		<?php if ($op == 'list') { ?>
			<div id="demo-lft-tab-1" class="tab-pane fade  <?php if ($op === 'list') { ?> active in<?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th>id</th>
									<th>产品名称</th>
									<th>现价</th>
									<th>今开</th>
									<th>最高</th>
									<th>最低</th>
									<th>涨幅</th>
									<th>跌幅</th>
									<th>成交量</th>
								</tr>
								</thead>
								<tbody>
								<?php if (isset($plist)) { ?>
									<?php foreach ($plist as $row) { ?>
										<?php if (!is_scalar($row)) { ?>
												<tr>
													<td><?= $row->id ?></td>
													<td><?= $row->title ?></td>
													<td>
														<?php if (isset($row->price)) { ?>
															<?= $row->price ?>
														<?php } else { ?>
															0
														<?php } ?>
													</td>
													<td><?php if (isset($row->OpeningPrice)) { ?><?= $row->OpeningPrice ?><?php } else { ?>0<?php } ?></td>
													<td><?php if (isset($row->HighestPrice)) { ?><?= $row->HighestPrice ?><?php } else { ?>0<?php } ?></td>
													<td><?php if (isset($row->LowestPrice)) { ?><?= $row->LowestPrice ?><?php } else { ?>0<?php } ?></td>
													<td>
													<?php if (isset($row->price)) { ?>
														<?= sprintf('%.2f', ($row->price - $row->OpeningPrice) / $row->OpeningPrice * 100) ?>%
														<?php } else { ?>
														0%
													<?php } ?>
													</td>
													<td>
													<?php if (isset($row->price)) { ?>
														<?= sprintf('%.2f', ($row->price - $row->LowestPrice) / $row->OpeningPrice * 100) ?>%
														<?php } else { ?>
                                                        0%
													<?php } ?>
													</td>
													<td><?php if (isset($row->Volume)) { ?><?= $row->Volume ?><?php } else { ?>0<?php } ?></td>
												</tr>
										<?php } ?>
									<?php } ?>
								<?php } ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<?php } ?>
			<?php if ($op == 'order') { ?>
			<div id="demo-lft-tab-2" class="tab-pane fade <?php if ($op === 'order') { ?> active in<?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="get" action="<?= $apppath ?>/product/plist">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">产品名称</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input type="hidden" value="order" name="op"/>
									<input class="form-control" id="demo-vs-definput" type="text" name="title" value="<?php if (isset($title)) { ?><?= $title ?><?php } ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">产品id</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" id="demo-vs-definput" type="text" name="sid" value="<?php if (isset($sid)) { ?><?= $sid ?><?php } ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">价格</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" id="demo-vs-definput" type="text" name="price" value="<?php if (isset($price)) { ?><?= $price ?><?php } ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索条件</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6" style="line-height: 30px;font-size: 14px;">
									<input id=" demo-vs-definput" type="radio" name="status" value="1"<?php if (isset($status)) { ?><?php if ($status === '1') { ?>checked<?php } ?><?php } ?>>已完成
									<input id="demo-vs-definput" type="radio" name="status" value="0"<?php if (isset($status)) { ?><?php if ($status === '0') { ?>checked<?php } ?><?php } ?>>未完成
								</div>
							</div>
							<div class="text-lg-center">
								<button class="btn btn-info fa fa-search" type="submit">搜索</button>
							</div>
						<div class="table-responsive">
							<table class="table table-hover">
							<thead>
							<tr>
								<th>挂单用户id</th>
								<th>产品id</th>
								<th>产品名称</th>
								<th>挂单类型</th>
								<th>挂单数量</th>
								<th>挂单价格</th>
								<th>挂单时间</th>
								<th>挂单状态</th>
								<th>结束时间</th>
								<th>成交数量</th>
								<th>交易用户id</th>
							</tr>
							</thead>
							<tbody>
								<?php if (isset($orderlist)) { ?>
									<?php foreach ($orderlist['items'] as $row) { ?>

											<tr>
												<td><?= $row['uid'] ?></td>
												<td><?= $row['sid'] ?></td>
												<td><?= $row['goods'] ?></td>
												<td>
													<?php if ($row['type'] === '1') { ?>买<?php } else { ?>卖<?php } ?>
												</td>
												<td><?= $row['number'] ?></td>
												<td><?= $row['price'] ?></td>
												<td><?php echo date('Y-m-d H:i:s',$row['createtime'])?></td>
												<td>
												<span class="text-muted"><i class="demo-pli-clock"><div class="label label-table label-success"><?php if ($row['status'] === '0') { ?>正常<?php } elseif ($row['status'] === '1') { ?>结束<?php } elseif ($row['status'] === '2') { ?>撤销<?php } ?></div></i></span></td>
												<td>
													<?php if (isset($row['endtime'])) { ?><?php echo date('Y-m-d H:i:s',$row['endtime'])?><?php } else { ?> ---<?php } ?>
												</td>
												<td><?= $row['dealnum'] ?></td>
												<td><?= $row['bid'] ?></td>
											</tr>

									<?php } ?>
								<?php } ?>
							</tbody>
							</table>
							<div class="panel-body text-center">
								<ul class="pagination">
									<li><a href="<?= $apppath ?>/product/plist?page=1&op=order<?php if (isset($title)) { ?>&title=<?= $title ?><?php } ?><?php if (isset($sid)) { ?>&sid=<?= $sid ?><?php } ?><?php if (isset($status)) { ?>&status=<?= $status ?><?php } ?><?php if (isset($price)) { ?>&price=<?= $price ?><?php } ?>" class="demo-pli-arrow-right">首页</a></li>
									<li><a href="<?= $apppath ?>/product/plist?op=order<?php if (isset($title)) { ?>&title=<?= $title ?><?php } ?><?php if (isset($sid)) { ?>&sid=<?= $sid ?><?php } ?><?php if (isset($status)) { ?>&status=<?= $status ?><?php } ?><?php if (isset($price)) { ?>&price=<?= $price ?><?php } ?>&page=<?= $orderlist['before'] ?>">上一页</a></li>
									<li><a href="<?= $apppath ?>/product/plist?op=order<?php if (isset($title)) { ?>&title=<?= $title ?><?php } ?><?php if (isset($sid)) { ?>&sid=<?= $sid ?><?php } ?><?php if (isset($status)) { ?>&status=<?= $status ?><?php } ?><?php if (isset($price)) { ?>&price=<?= $price ?><?php } ?>&page=<?= $orderlist['next'] ?>">下一页</a></li>
									<li><a href="<?= $apppath ?>/product/plist?op=order<?php if (isset($title)) { ?>&title=<?= $title ?><?php } ?><?php if (isset($sid)) { ?>&sid=<?= $sid ?><?php } ?><?php if (isset($status)) { ?>&status=<?= $status ?><?php } ?><?php if (isset($price)) { ?>&price=<?= $price ?><?php } ?>&page=<?= $orderlist['total_pages'] ?>" class="demo-pli-arrow-right">尾页</a></li>
								</ul>
							</div>
						</div>
						</form>
					</div>
				</div>
			</div>
			<?php } ?>
			<?php if ($op == 'count') { ?>
			<div id="demo-lft-tab-3" class="tab-pane fade  <?php if ($op == 'count') { ?> active in<?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th>产品id</th>
									<th>产品名称</th>
									<th>累计交易</th>
									<th>累计挂单</th>
									<th>累计完成</th>
									<th>累计买入</th>
									<th>累计卖出</th>
									<th>累计手续费</th>
								</tr>
								</thead>
								<tbody>
								<?php if (isset($countData)) { ?>
									<?php foreach ($countData as $rows) { ?>
												<tr>
													<td><?= $rows['id'] ?></td>
													<td><?= $rows['title'] ?></td>
													<td><?= $rows['count'] ?></td>
													<td><?= $rows['count1'] ?></td>
													<td><?= $rows['count2'] ?></td>
													<td><?= $rows['count3'] ?></td>
													<td><?= $rows['count4'] ?></td>
													<td><?= $rows['count5'] ?></td>
												</tr>
									<?php } ?>
								<?php } ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<?php } ?>
		</div>
	</div>
</div>
<!--文章模块结束--!>
