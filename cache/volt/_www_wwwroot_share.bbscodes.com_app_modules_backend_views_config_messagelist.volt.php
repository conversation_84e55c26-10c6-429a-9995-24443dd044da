<!--用户列表-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="active">
				<a data-toggle="tab" href="#demo-lft-tab-1" aria-expanded="true">短信列表</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade active in">
				<div class="panel">
					<div class="panel-body">
					 </div>
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th>短信</th>
									<th>状态</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<?php if (isset($message)) { ?>
								<?php foreach ($message as $item) { ?>
									<tr>
									<td>
										<?php if (isset($item['type'])) { ?>
										<?php if ($item['type'] === 'message') { ?>dhc<?php } ?>
										<?php if ($item['type'] === 'jh') { ?>聚合<?php } ?>
										<?php } ?>
										</td>
										<td>
										<?php if (isset($item['status'])) { ?>
										<?php if ($item['status'] === '0') { ?>停用
										<?php } elseif ($item['status'] === '1') { ?>启用
										<?php } ?>
										<?php } ?>
										</td>
										<td>
											<a href="<?= $apppath ?>/config/sitMessage?op=edit<?php if (isset($item['type'])) { ?>&type=<?= $item['type'] ?><?php } ?>">
												<button class="btn btn-warning btn-labeled fa fa-edit">更新</button>
											</a>
										</td>
									</tr>
								<?php } ?>
								<?php } ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
	</div>
</div>

<!--文章模块结束--!>
