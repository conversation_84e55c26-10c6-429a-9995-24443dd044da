<?php $this->flashSession->output(); ?>
<!--商品信息-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'display') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/user?op=display&page=<?= $userList->current ?>" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'display') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">会员列表</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'hail') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/user?op=hail&page=1&status=1" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'hail') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">好友列表</a>
			</li>
			<?php if ($user_type == 'yunji') { ?>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'branch') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
            	<a href="<?= $apppath ?>/orchard/user?op=branch&page=1&status=1" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'branch') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">分公司列表</a>
           	</li>
            <?php } ?>
			<?php if ($op == 'admin') { ?>
			<li class="active">
				<a href="<?= $apppath ?>/orchard/user?op=admin" aria-expanded="true">管理员操作</a>
			</li>
			<?php } ?>
			<?php if ($op == 'edit') { ?>
            	<li class="active">
            		<a href="<?= $apppath ?>/orchard/user?op=edit" aria-expanded="true">管理员操作</a>
            	</li>
           	<?php } ?>
		</ul>
		<?php if ($op == 'display') { ?>
		<div class="tab-content">
			<div class="panel-body">
				<form class="form-horizontal form-padding " method="get" action="<?= $apppath ?>/orchard/user">
							<div class="panel-control">
                                 <span class="label label-info">合计<?= $all_peple ?>人 </span>
                             </div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索信息</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<input class="form-control" type="text" name="keywords" value="<?php if (isset($keywords)) { ?><?= $keywords ?><?php } ?>" placeholder="请输入会员编号昵称电话">
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<select name="status" class="form-control">
								<option value="1" <?php if (isset($status)) { ?><?php if ($status == 1) { ?>selected<?php } ?><?php } ?>>
									正常
								</option>
								<option value="9" <?php if (isset($status)) { ?><?php if ($status == 9) { ?>selected<?php } ?><?php } ?>>
									禁止
								</option>
							</select>
						</div>
					</div>
					<div class="form-group">
                    	<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">会员等级</label>
                    	<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<div  class="input-group">
								<span class="input-group-addon">等级</span>
								<input type="text" name="ulevel" value="<?php if (isset($ulevel)) { ?><?= $ulevel ?><?php } ?>"  class="form-control">
							</div>
                    	</div>
                    	</div>

					<div class="text-lg-center">
						
						<button class="btn btn-info fa fa-search" type="submit">搜索</button>
					</div>
				</form>
			</div>
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'display') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
									<tr>
										<th>记录ID</th>
										<th>会员ID</th>
										<th>昵称</th>
										<th>电话</th>

										<th>钻石</th>
										<th>木材</th>
										<th>石材</th>
										<th>钢材</th>
										<th>等级</th>

										<th>普通狗粮</th>
										<th>优质狗粮</th>
										<th>玫瑰之心</th>
										<th>铜锄头</th>
										<th>银锄头</th>
										<th>铜宝箱</th>
										<th>银宝箱</th>
										<th>金宝箱</th>
										<th>钻石宝箱</th>
										<th>化肥</th>
										<th>洒水壶</th>
										<th>除草剂</th>
										<th>除虫剂</th>
										<th>绿宝石</th>
										<th>紫宝石</th>
										<th>蓝宝石</th>
										<th>黄宝石</th>

										<th>创建时间</th>
										<th>更新时间</th>
										<th>状态</th>
										<th>操作</th>
									</tr>
								</thead>
								<tbody>
								<?php if (isset($userList)) { ?>
								<?php foreach ($userList as $list) { ?>
								<?php if (!is_scalar($list)) { ?>
								<?php foreach ($list as $rows) { ?>
								<tr>
									<td><?= $rows->id ?></td>
									<td><?= $rows->uid ?></td>
									<td><?= $rows->nickname ?></td>
									<td><?= $rows->mobile ?></td>

									<td><?= $rows->diamonds ?></td>
									<td><?= $rows->wood ?></td>
									<td><?= $rows->stone ?></td>
									<td><?= $rows->steel ?></td>
									<td><?= $rows->grade ?></td>
									<td><?= $rows->dogFood1 ?></td>
									<td><?= $rows->dogFood2 ?></td>
									<td><?= $rows->roseSeed ?></td>
									<td><?= $rows->choe ?></td>
									<td><?= $rows->shoe ?></td>
									<td><?= $rows->cchest ?></td>
									<td><?= $rows->schest ?></td>
									<td><?= $rows->gchest ?></td>
									<td><?= $rows->dchest ?></td>
									<td><?= $rows->cfert ?></td>
									<td><?= $rows->wcan ?></td>
									<td><?= $rows->hcide ?></td>
									<td><?= $rows->icide ?></td>
									<td><?= $rows->emerald ?></td>
									<td><?= $rows->purplegem ?></td>
									<td><?= $rows->sapphire ?></td>
									<td><?= $rows->topaz ?></td>

									<td><?= date('Y-m-d H:i:s', $rows->createtime) ?></td>
									<td><?= date('Y-m-d H:i:s', $rows->updatetime) ?></td>
									<td>
										<?php if ($rows->status == '1') { ?>
											正常
										<?php } elseif ($rows->status == '9') { ?>
											禁用
										<?php } else { ?>
											异常
										<?php } ?>
									</td>
									<td>
										<button class="btn btn-warning btn-labeled">
											<?php if ($rows->id > 0 && $rows->status == 1) { ?>
											<a href="<?= $apppath ?>/orchard/user?op=status&id=<?= $rows->id ?>">禁用</a>
											<?php } ?>
											<?php if ($rows->id > 0 && $rows->status == 9) { ?>
											<a href="<?= $apppath ?>/orchard/user?op=status&id=<?= $rows->id ?>">恢复</a>
											<?php } ?>
											<?php if ($user_type == 'duojin') { ?>
											<a href="?op=edit&id=<?= $rows->id ?>">
                                            	<button class="btn btn-warning btn-labeled fa fa-edit">编辑</button>
                                            </a>
                                            <?php } ?>
										</button>
										<?php if ($user_type == 'chuangjin') { ?>
										<button class="btn btn-warning btn-labeled">
											<a href="<?= $apppath ?>/orchard/user?op=downgrade&uid=<?= $rows->uid ?>">
												房屋降级
											</a>
										</button>
										<?php } ?>
									</td>

									<?php } ?>
									<?php } ?>
									<?php } ?>
									<?php } ?>
								</tbody>
							</table>
							<div class="bars pull-left">
                                  <a href="<?= $apppath ?>/Orchard/print">
                                  <button class="btn btn-info">
                                  <i class="demo-pli-cross"></i> 全部导出
                                  </button>
                                </a>
                            </div>
                            <div class="panel-body text-center">
								<ul class="pagination">
									<li ><a href="?op=display&page=1<?php if (isset($keyword)) { ?>?keyword=<?= $keyword ?><?php } ?>">首页</a></li>
									<?php if ($userList->current != 1) { ?>
										<li><a href="?op=display&page=<?= $userList->before ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">上一页</a></li>
									<?php } ?>

									<?php
									$pageStart = max(1, $userList->current - 5);
									if ($pageStart == 1) {
										$pageEnd = min($userList->current + 10, $userList->last);
									} else {
										$pageEnd = min($userList->current + 5, $userList->last);
									}
									if ($pageEnd == $userList->last) {
										$pageStart = max(1, $userList->current - 10);
									}
									for ($i = $pageStart; $i <= $pageEnd; $i++): ?>
									<li class="<?php if ($userList->current == $i) { ?>active<?php } ?>"><a href="?op=display&page=<?= $i ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>"><?= $i ?></a></li>
									<?php endfor; ?>

									<?php if ($userList->current != $userList->last) { ?>
									<li><a href="?op=display&page=<?= $userList->next ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">下一页</a></li>
									<?php } ?>
									<li><a href="?op=display&page=<?= $userList->last ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">尾页</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
			<?php } ?>
			<?php if ($op == 'hail') { ?>
			<div class="tab-content">
				<div class="panel-body">
					<form class="form-horizontal form-padding " method="get" action="<?= $apppath ?>/orchard/user?op=hail">
						<input class="form-control" type="hidden" name="op" value="hail">
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索信息</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" type="text" name="keywords" value="<?php if (isset($keywords)) { ?><?= $keywords ?><?php } ?>" placeholder="请输入会员编号昵称电话">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<select name="status" class="form-control">

									<option value="1" <?php if (isset($status)) { ?><?php if ($status == 1) { ?>selected<?php } ?><?php } ?>>
										正常
									</option>
									<option value="9" <?php if (isset($status)) { ?><?php if ($status == 9) { ?>selected<?php } ?><?php } ?>>
										拒绝
									</option>
									<option value="0" <?php if (isset($status)) { ?><?php if ($status == '0') { ?>selected<?php } ?><?php } ?>>
										申请中
									</option>
								</select>
							</div>
						</div>
						<div class="text-lg-center">
							<button class="btn btn-info fa fa-search" type="submit">搜索</button>
						</div>
					</form>
				</div>
				<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'hail') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
					<div class="panel">
						<div class="panel-body">
							<div class="table-responsive">
								<table class="table table-hover">
									<thead>
										<tr>
											<th>记录ID</th>
											<th>会员ID</th>
											<th>好友uid</th>
											<th>好友昵称</th>
											<th>好友电话</th>
											<th>创建时间</th>
											<th>更新时间</th>
											<th>状态</th>
											<th>操作</th>
										</tr>
									</thead>
									<tbody>
									<?php if (isset($hailList)) { ?>
									<?php foreach ($hailList as $list) { ?>
									<?php if (!is_scalar($list)) { ?>
									<?php foreach ($list as $rows) { ?>
									<tr>
										<td><?= $rows->id ?></td>
										<td><?= $rows->uid ?></td>
										<td><?= $rows->huid ?></td>
										<td><?= $rows->nickname ?></td>
										<td><?= $rows->mobile ?></td>
										<td><?= date('Y-m-d H:i:s', $rows->createtime) ?></td>
										<td><?= date('Y-m-d H:i:s', $rows->updatetime) ?></td>
										<td>
											<?php if ($rows->status == '1') { ?>
												正常
											<?php } elseif ($rows->status == '9') { ?>
												拒绝
											<?php } else { ?>
												申请中
											<?php } ?>
										</td>
										<td>
											<?php if ($rows->id > 0 && $rows->status == '0') { ?>
											<button class="btn btn-warning btn-labeled">
												<a href="<?= $apppath ?>/orchard/user?op=savehail&id=<?= $rows->id ?>&status=1&page=<?= $hailList->current ?>">通过</a>
												<a href="<?= $apppath ?>/orchard/user?op=savehail&id=<?= $rows->id ?>&status=9&page=<?= $hailList->current ?>">拒绝</a>
											</button>
											<?php } ?>
										</td>

										<?php } ?>
										<?php } ?>
										<?php } ?>
										<?php } ?>
									</tbody>
								</table>
								<div class="panel-body text-center">
									<ul class="pagination">
										<li ><a href="?op=hail&page=1<?php if (isset($keyword)) { ?>?keyword=<?= $keyword ?><?php } ?>">首页</a></li>
										<?php if ($hailList->current != 1) { ?>
											<li><a href="?op=hail&page=<?= $hailList->before ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">上一页</a></li>
										<?php } ?>

										<?php
										$pageStart = max(1, $hailList->current - 5);
										if ($pageStart == 1) {
											$pageEnd = min($hailList->current + 10, $hailList->last);
										} else {
											$pageEnd = min($hailList->current + 5, $hailList->last);
										}
										if ($pageEnd == $hailList->last) {
											$pageStart = max(1, $hailList->current - 10);
										}
										for ($i = $pageStart; $i <= $pageEnd; $i++): ?>
										<li class="<?php if ($hailList->current == $i) { ?>active<?php } ?>"><a href="?op=hail&page=<?= $i ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>"><?= $i ?></a></li>
										<?php endfor; ?>

										<?php if ($hailList->current != $hailList->last) { ?>
										<li><a href="?op=hail&page=<?= $hailList->next ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">下一页</a></li>
										<?php } ?>
										<li><a href="?op=hail&page=<?= $hailList->last ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">尾页</a></li>
									</ul>
								</div>
							</div>
						</div>
					</div>
				</div>
			<?php } ?>
			<?php if ($op == 'admin') { ?>
			<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'admin') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/user?op=admin">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">会员编号</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" id="demo-vs-definput" type="text" name="uid" value="">
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">类型</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<?php foreach ($getAdminType as $keys => $lists) { ?>
									<label class="radio-inline">
										<input type="radio" name="model" value="<?= $keys ?>" <?php if ($keys == 'diamonds') { ?>checked <?php } ?>> <?= $lists ?>
									</label>
									<?php } ?>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">操作</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<label class="radio-inline">
										<input type="radio" name="types" value="adminadd" checked> 添加
									</label>
									<label class="radio-inline">
										<input type="radio" name="types" value="adminded"> 扣除
									</label>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">数量</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" id="demo-vs-definput" type="text" name="nums" value="">
								</div>
							</div>
							<div class="panel-footer text-left">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
			<?php } ?>
			<?php if ($op == 'edit') { ?>
            			<div class="tab-content">
            			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'edit') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
            				<div class="panel">
            					<div class="panel-body">
            						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/user?op=edit">
            							<div class="form-group">
            								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">会员编号</label>
            								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
            									<div class="form-control"><?= $item->uid ?></div>
											</div>
            							</div>
            							<div class="form-group">
                                             <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">会员昵称</label>
                                             <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                 <input class="form-control" id="demo-vs-definput" type="text" name="nickname" value="<?php if (isset($item)) { ?><?= $item->nickname ?><?php } ?>">
                                             </div>
                                        </div>
                                        <div class="form-group">
                                             <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">电话</label>
                                             <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                   <div class="form-control"><?= $item->mobile ?></div>
                                             </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">钻石数量</label>
                                            <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                <input class="form-control" id="demo-vs-definput" type="text" name="diamonds" value="<?php if (isset($item)) { ?><?= $item->diamonds ?><?php } ?>">
                                            </div>
                                        </div>
                                         <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">木材</label>
                                              <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                 <input class="form-control" id="demo-vs-definput" type="text" name="wood" value="<?php if (isset($item)) { ?><?= $item->wood ?><?php } ?>">
                                              </div>
                                         </div>
                                         <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">石材</label>
                                              <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                  <input class="form-control" id="demo-vs-definput" type="text" name="stone" value="<?php if (isset($item)) { ?><?= $item->stone ?><?php } ?>">
                                              </div>
                                         </div>
                                         <div class="form-group">
                                             <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">钢材</label>
                                             <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                 <input class="form-control" id="demo-vs-definput" type="text" name="steel" value="<?php if (isset($item)) { ?><?= $item->steel ?><?php } ?>">
                                             </div>
                                         </div>
                                         <div class="form-group">
                                            <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">普通狗粮</label>
                                            <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                              <input class="form-control" id="demo-vs-definput" type="text" name="dogFood1" value="<?php if (isset($item)) { ?><?= $item->dogFood1 ?><?php } ?>">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">高级狗粮</label>
                                            <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                <input class="form-control" id="demo-vs-definput" type="text" name="dogFood2" value="<?php if (isset($item)) { ?><?= $item->dogFood2 ?><?php } ?>">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">优质狗粮2</label>
                                            <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                <input class="form-control" id="demo-vs-definput" type="text" name="dogFood2" value="<?php if (isset($item)) { ?><?= $item->dogFood2 ?><?php } ?>">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">玫瑰花种子</label>
                                            <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                <input class="form-control" id="demo-vs-definput" type="text" name="roseSeed" value="<?php if (isset($item)) { ?><?= $item->roseSeed ?><?php } ?>">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                             <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">铜锄头</label>
                                             <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                 <input class="form-control" id="demo-vs-definput" type="text" name="choe" value="<?php if (isset($item)) { ?><?= $item->choe ?><?php } ?>">
                                             </div>
                                        </div>
                                        <div class="form-group">
                                           <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">银锄头</label>
                                           <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                             <input class="form-control" id="demo-vs-definput" type="text" name="shoe" value="<?php if (isset($item)) { ?><?= $item->shoe ?><?php } ?>">
                                           </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">铜宝箱</label>
                                            <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                <input class="form-control" id="demo-vs-definput" type="text" name="cchest" value="<?php if (isset($item)) { ?><?= $item->cchest ?><?php } ?>">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">银宝箱</label>
                                              <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                  <input class="form-control" id="demo-vs-definput" type="text" name="schest" value="<?php if (isset($item)) { ?><?= $item->schest ?><?php } ?>">
                                             </div>
                                        </div>
                                         <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">金宝箱</label>
                                              <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                  <input class="form-control" id="demo-vs-definput" type="text" name="gchest" value="<?php if (isset($item)) { ?><?= $item->gchest ?><?php } ?>">
                                              </div>
                                         </div>
                                         <div class="form-group">
                                            <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">钻石宝箱</label>
                                            <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                <input class="form-control" id="demo-vs-definput" type="text" name="dchest" value="<?php if (isset($item)) { ?><?= $item->dchest ?><?php } ?>">
                                            </div>
                                         </div>
                                         <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">化肥</label>
                                              <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                  <input class="form-control" id="demo-vs-definput" type="text" name="cfert" value="<?php if (isset($item)) { ?><?= $item->cfert ?><?php } ?>">
                                              </div>
                                         </div>
                                         <div class="form-group">
                                             <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">洒水壶</label>
                                             <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                 <input class="form-control" id="demo-vs-definput" type="text" name="wcan" value="<?php if (isset($item)) { ?><?= $item->wcan ?><?php } ?>">
                                             </div>
                                         </div>
                                         <div class="form-group">
                                               <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">除草剂</label>
                                               <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                    <input class="form-control" id="demo-vs-definput" type="text" name="hcide" value="<?php if (isset($item)) { ?><?= $item->hcide ?><?php } ?>">
                                               </div>
                                         </div>
                                         <div class="form-group">
                                             <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">除虫剂</label>
                                             <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                 <input class="form-control" id="demo-vs-definput" type="text" name="icide" value="<?php if (isset($item)) { ?><?= $item->icide ?><?php } ?>">
                                             </div>
                                         </div>
                                         <div class="form-group">
                                               <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">绿宝石</label>
                                               <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                    <input class="form-control" id="demo-vs-definput" type="text" name="emerald" value="<?php if (isset($item)) { ?><?= $item->emerald ?><?php } ?>">
                                               </div>
                                         </div>
                                          <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">紫宝石</label>
                                              <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                   <input class="form-control" id="demo-vs-definput" type="text" name="purplegem" value="<?php if (isset($item)) { ?><?= $item->purplegem ?><?php } ?>">
                                              </div>
                                          </div>
                                          <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">蓝宝石</label>
                                              <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                  <input class="form-control" id="demo-vs-definput" type="text" name="sapphire" value="<?php if (isset($item)) { ?><?= $item->sapphire ?><?php } ?>">
                                              </div>
                                          </div>
                                          <div class="form-group">
                                                 <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">黄宝石</label>
                                                 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                      <input class="form-control" id="demo-vs-definput" type="text" name="topaz" value="<?php if (isset($item)) { ?><?= $item->topaz ?><?php } ?>">
                                                 </div>
                                          </div>
                                      <div class="panel-footer text-left">
            								<input type='hidden'  name="id" value="<?= $item->id ?>">
            								<input type='hidden'  name="uid" value="<?= $item->uid ?>">
            								<button class="btn btn-success" type="submit">提交</button>
            						</div>
            						</form>
            					</div>
            				</div>
            			</div>
            		</div>
            			<?php } ?>
		</div>
	</div>
</div>
<!--日志模块结束--!>

