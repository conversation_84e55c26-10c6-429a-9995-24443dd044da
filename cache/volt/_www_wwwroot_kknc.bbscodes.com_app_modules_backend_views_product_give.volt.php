
<!--用户赠送列表显示-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="active">
				<a data-toggle="tab" href="#demo-lft-tab-2" aria-expanded="true">赠送列表</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="demo-lft-tab-2" class="tab-pane fade active in">
				<div class="tab-base tab-stacked-left">
					<div class="tab-content">
					<form class="form-horizontal form-padding " method="get" action="<?= $apppath ?>/product/give">
                          		 <div class="form-group">
                              		<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">用户id</label>
                                     <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                        <input class="form-control" id="demo-vs-definput" type="text" name="keywords" value="<?php if (isset($keywords)) { ?><?= $keywords ?><?php } ?>">
                                     </div>
                                 </div>
                                 <div class="form-group">
                                     <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">接受用户id</label>
                                     <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                        <input class="form-control" id="demo-vs-definput" type="text" name="accept" value="<?php if (isset($accept)) { ?><?= $accept ?><?php } ?>">
                                     </div>
                                 </div>
                                 <div class="form-group">
                                        <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">产品id</label>
                                        <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                    	<input class="form-control" id="demo-vs-definput" type="text" name="productid" value="<?php if (isset($productid)) { ?><?= $productid ?><?php } ?>">
                               			</div>
								 </div>
							<div class="form-group">
                               <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
                                 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                    <select name="status" class="form-control">
                                      <option value="3" <?php if (isset($status)) { ?><?php if ($status == 3) { ?>selected<?php } ?><?php } ?>>
                                       全部
                                      </option>
                                      <option value="1" <?php if (isset($status)) { ?><?php if ($status == 1) { ?>selected<?php } ?><?php } ?>>
                                       已接收
                                     </option>
                                     <option value="0" <?php if (isset($status)) { ?><?php if ($status == 0) { ?>selected<?php } ?><?php } ?>>
                                        待接收
                                     </option>
                                     <option value="2" <?php if (isset($status)) { ?><?php if ($status == 2) { ?>selected<?php } ?><?php } ?>>
                                       退回/撤销
                                     </option>
                                   </select>
                                </div>
                             </div>
                            <div class="text-lg-center">
                             		<button class="btn btn-info fa fa-search" type="submit">搜索</button>
                        	</div>
                    </form>
						<div id="demo-stk-lft-tab-1" class="tab-pane active">
							<table class="table table-hover">
								<thead>
								<tr>
									<th>ID</th>
									<th>赠送用户id</th>
									<th>接收用户id</th>
									<th>赠送产品id</th>
									<th>赠送数量</th>
									<th>赠送状态</th>
									<th>赠送产品名称</th>
									<th>赠送时间</th>
									<th>赠送产品手续费</th>
									<th>赠送金币索取</th>
								</tr>
								</thead>
								<tbody>
								<?php if (isset($giveList)) { ?>
									<?php foreach ($giveList['items'] as $row) { ?>
												<tr>
													<td><?= $row['id'] ?></td>
													<td><?= $row['uid'] ?></td>
													<td><?= $row['accept'] ?></td>
													<td><?= $row['productid'] ?></td>
													<td><?= $row['number'] ?></td>
													<td>
														<?php if ($row['status'] == 1) { ?>接收成功<?php } ?>
														<?php if ($row['status'] == 2) { ?>退回/撤销<?php } ?>
														<?php if ($row['status'] == 0) { ?>等待接收<?php } ?>
													</td>
													<td><?= $row['title'] ?></td>
													<td><?php echo date('Y-m-d H:i:s',$row['createtime'])?></td>
													<td><?= $row['fee'] ?></td>
													<td><?= $row['giveGold'] ?></td>
												</tr>
									<?php } ?>
								<?php } ?>
								</tbody>
							</table>
							<div class="panel-body text-center">
							<?php if ($giveList['total_pages'] > 1) { ?>
								<ul class="pagination">
									<li><a href="<?= $apppath ?>/product/give?page=1<?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?><?php if (isset($productid)) { ?>&productid=<?= $productid ?><?php } ?><?php if (isset($status)) { ?>&status=<?= $status ?><?php } ?><?php if (isset($accept)) { ?>&accept=<?= $accept ?><?php } ?>" class="demo-pli-arrow-right">首页</a></li>
									<li><a href="<?= $apppath ?>/product/give?page=<?= $giveList['before'] ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?><?php if (isset($productid)) { ?>&productid=<?= $productid ?><?php } ?><?php if (isset($status)) { ?>&status=<?= $status ?><?php } ?><?php if (isset($accept)) { ?>&accept=<?= $accept ?><?php } ?>">上一页</a></li>
									<li><a href="<?= $apppath ?>/product/give?page=<?= $giveList['current'] ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?><?php if (isset($productid)) { ?>&productid=<?= $productid ?><?php } ?><?php if (isset($status)) { ?>&status=<?= $status ?><?php } ?><?php if (isset($accept)) { ?>&accept=<?= $accept ?><?php } ?>">当前第<?= $giveList['current'] ?>页</a></li>
									<li><a href="<?= $apppath ?>/product/give?page=<?= $giveList['total_pages'] ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?><?php if (isset($productid)) { ?>&productid=<?= $productid ?><?php } ?><?php if (isset($status)) { ?>&status=<?= $status ?><?php } ?><?php if (isset($accept)) { ?>&accept=<?= $accept ?><?php } ?>">共<?= $giveList['total_pages'] ?>页</a></li>
									<li><a href="<?= $apppath ?>/product/give?page=<?= $giveList['next'] ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?><?php if (isset($productid)) { ?>&productid=<?= $productid ?><?php } ?><?php if (isset($status)) { ?>&status=<?= $status ?><?php } ?><?php if (isset($accept)) { ?>&accept=<?= $accept ?><?php } ?>">下一页</a></li>
									<li><a href="<?= $apppath ?>/product/give?page=<?= $giveList['last'] ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?><?php if (isset($productid)) { ?>&productid=<?= $productid ?><?php } ?><?php if (isset($status)) { ?>&status=<?= $status ?><?php } ?><?php if (isset($accept)) { ?>&accept=<?= $accept ?><?php } ?>" class="demo-pli-arrow-right">尾页</a></li>
								</ul>
							<?php } ?>
							</div>
						</div>
					</div>
				</div>
			</div>

		</div>
	</div>
</div>
<!--文章模块结束--!>


