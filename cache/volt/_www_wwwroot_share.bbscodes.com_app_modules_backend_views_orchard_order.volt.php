<?php $this->flashSession->output(); ?>
<!--商品信息-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'display') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/order?op=display&page=<?= $orderList->current ?>&payStatus=1" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'display') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">订单记录</a>
			</li>
		</ul>
		<?php if ($op == 'display') { ?>
		<div class="tab-content">
			<div class="panel-body">
				<form class="form-horizontal form-padding " method="get" action="<?= $apppath ?>/orchard/order">
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索信息</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<input class="form-control" type="text" name="keywords" value="<?php if (isset($keywords)) { ?><?= $keywords ?><?php } ?>" placeholder="请输入会员编号 订单号">
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">支付类型</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<select name="payStatus" class="form-control">
								<option value="1" <?php if (isset($payStatus)) { ?><?php if ($payStatus == 1) { ?>selected<?php } ?><?php } ?>>
									已支付
								</option>
								<option value="0" <?php if (isset($payStatus)) { ?><?php if ($payStatus == 0) { ?>selected<?php } ?><?php } ?>>
									未支付
								</option>
							</select>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">购买类型</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<select name="types" class="form-control">
								<?php foreach ($orderType as $key => $row) { ?>
								<option value="<?= $key ?>" <?php if (isset($types)) { ?><?php if ($key == $types) { ?>selected<?php } ?><?php } ?>>
									<?= $row ?>
								</option>
								<?php } ?>
							</select>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
						</div>
					</div>
					<div class="text-lg-center">
						<button class="btn btn-info fa fa-search" type="submit">搜索</button>
					</div>
				</form>
			</div>
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'display') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
									<tr>
										<th>会员ID</th>
										<th>会员信息</th>
										<th>订单号</th>
										<th>购买类型</th>
										<th>支付状态</th>
										<th>支付数量</th>
										<th>购买数量</th>
										<th>创建时间</th>
										<th>支付时间</th>
									</tr>
								</thead>
								<tbody>
								<?php if (isset($orderList)) { ?>
								<?php foreach ($orderList as $list) { ?>
								<?php if (!is_scalar($list)) { ?>
								<?php foreach ($list as $rows) { ?>
								<tr>
									<td><?= $rows->uid ?></td>
									<td><?= $rows->nickname ?><br/><?= $rows->mobile ?></td>
									<td><?= $rows->orderId ?></td>
									<td><?php echo $orderType[$rows->types];?></td>
									<td>
										<?php if ($rows->payStatus == 1) { ?>
										已支付
										<?php } ?>
										<?php if ($rows->payStatus == 0) { ?>
										未支付
										<?php } ?>
									</td>
									<td><?= $rows->coing ?></td>
									<td><?= $rows->fruit ?></td>
									<td><?= date('Y-m-d H:i:s', $rows->createtime) ?></td>
									<td><?= date('Y-m-d H:i:s', $rows->payTime) ?></td>
									<?php } ?>
									<?php } ?>
									<?php } ?>
									<?php } ?>
								</tbody>
							</table>
							<div class="panel-body text-center">
								<?php if ($orderList->total_pages > 1) { ?>
								<ul class="pagination">
									<li><a href="<?= $apppath ?>/orchard/order?op=display&page=1<?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?><?php if (isset($starttime)) { ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php } ?><?php if (isset($payStatus)) { ?>&payStatus=<?= $payStatus ?><?php } ?>" class="demo-pli-arrow-right">首页</a></li>
									<li><a href="<?= $apppath ?>/orchard/order?op=display&page=<?= $orderList->before ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?><?php if (isset($starttime)) { ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php } ?><?php if (isset($payStatus)) { ?>&payStatus=<?= $payStatus ?><?php } ?>">上一页</a></li>
									<li><a href="#">第<?= $orderList->current ?>页</a></li>
									<li><a href="#">共<?= $orderList->total_pages ?>页</a></li>
									<li><a href="<?= $apppath ?>/orchard/order?op=display&page=<?= $orderList->next ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?><?php if (isset($starttime)) { ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php } ?><?php if (isset($payStatus)) { ?>&payStatus=<?= $payStatus ?><?php } ?>">下一页</a></li>
									<li><a href="<?= $apppath ?>/orchard/order?op=display&page=<?= $orderList->total_pages ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?><?php if (isset($starttime)) { ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php } ?><?php if (isset($payStatus)) { ?>&payStatus=<?= $payStatus ?><?php } ?>" class="demo-pli-arrow-right">尾页</a></li>
								</ul>
								<?php } ?>
							</div>
						</div>
					</div>
				</div>
			</div>
			<?php } ?>
		</div>
	</div>
</div>
<!--日志模块结束--!>

