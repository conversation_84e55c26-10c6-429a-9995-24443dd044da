
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
		    <?php if ($show === 'list') { ?>
			<li class="active">
				<a data-toggle="tab" href="#demo-lft-tab-1" aria-expanded="true">联系方式</a>
			</li>
			<?php } ?>
			<?php if ($show === 'edit') { ?>
			<li class="active">
				<a data-toggle="tab" href="#demo-lft-tab-2" aria-expanded="<?php if ($show === 'edit') { ?>true<?php } else { ?>false<?php } ?>">添加联系方式</a>
			</li>
			<?php } ?>
		</ul>
		<div class="tab-content">
		<?php if ($show === 'list') { ?>
			<div id="demo-lft-tab-1" class="tab-pane fade active in ">
				<div class="panel">
					<div class="panel-body">
						<div class="pad-btm form-inline">
							<div class="row">
								<div class="col-sm-6 table-toolbar-left">
									<button id="demo-btn-addrow" class="btn btn-purple" onclick="window.location.href='<?= $apppath ?>/config/service?op=edit&type=2'"><i class="demo-pli-add"></i> 添加</button>
								</div>
							</div>
						</div>
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th >id</th>
									<th>服务名称</th>
									<th>联系方式</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<?php if (isset($sList)) { ?>
									<?php foreach ($sList as $row) { ?>
										<tr>
											<td><?= $row->id ?></td>
											<td><?= $row->title ?></td>
											<td><?= $row->way ?></td>
											<td>
												<a href="<?= $apppath ?>/config/service?op=edit&id=<?= $row->id ?>" class="btn btn-default btn-sm" title="编辑"><i class="fa fa-edit"></i></a>
												<?php if ($row->type != 0) { ?>
												<a href="<?= $apppath ?>/config/service?op=del&id=<?= $row->id ?>" class="btn btn-default btn-sm" title="删除"><i class="fa fa-trash"></i></a>
												<?php } ?>
											</td>
										</tr>
									<?php } ?>
								<?php } ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<?php } ?>
			<?php if ($show === 'edit') { ?>
			<div id="demo-lft-tab-2" class="tab-pane fade  active in ">
				<div class="panel">
					<form class="panel-body form-horizontal form-padding " method="post"  action="<?= $apppath ?>/config/service?op=list">
						<!--Static-->
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">服务名称</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input type="text" class="form-control" name="title" value="<?php if (isset($item->title)) { ?><?= $item->title ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">联系方式</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input type="text" class="form-control" name="way" value="<?php if (isset($item->title)) { ?><?= $item->way ?><?php } ?>">
							</div>
						</div>
						<?php if ($k == 2 || $item->type != 0) { ?>
                        <div class="form-group">
                             <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">操作类型</label>
                             <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                  <select name="type" class="form-control">
                                        <option value="2" <?php if (isset($item)) { ?><?php if ($item->type == 2) { ?>selected<?php } ?><?php } ?>>
                                             客服QQ/微信
                                        </option>
                                        <option value="3" <?php if (isset($item)) { ?><?php if ($item->type == 3) { ?>selected<?php } ?><?php } ?>>
                                             服务热线
                                        </option>
                                  </select>
                             </div>
                        </div>
                        <?php } ?>
						<div class="panel-footer text-left">
						    <?php if (isset($item)) { ?>
						    <input type="hidden" value="<?php if (isset($item->type)) { ?><?= $item->type ?><?php } ?>" name="type">
							<input type="hidden" value="<?php if (isset($item->id)) { ?><?= $item->id ?><?php } ?>" name="id">
							<?php } ?>
							<button class="btn btn-success" type="submit">添加</button>
						</div>
					</form>
				</div>
			</div>
            <?php } ?>

		</div>
	</div>
</div>
<!--文章模块结束--!>
