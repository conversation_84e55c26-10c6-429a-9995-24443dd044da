<!--用户列表-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="<?php if (isset($show)) { ?><?php if ($show === 'show') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a data-toggle="tab" href="#demo-lft-tab-1" aria-expanded="<?php if (isset($show)) { ?><?php if ($show === 'show') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">支付方式列表</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($show)) { ?><?php if ($show === 'show') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
					 <div class="pad-btm form-inline">
                       <div class="panel-control">
                       	<span class="label label-info">提示：只能启用一种支付状态</span>
                       	</div>
                      <div class="col-sm-6 table-toolbar-left">
                          <button id="demo-btn-addrow" class="btn btn-purple" onclick="window.location.href='<?= $apppath ?>/recharge/list'"><i class="demo-pli-add"></i> 增加</button>
                      </div>
                     </div>
                       </div>
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th>id</th>
									<th>支付方式</th>
									<th>状态</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<?php if (isset($paylist)) { ?>
								<?php foreach ($paylist as $item) { ?>
									<tr>
										<td><?= $item->id ?></td>
										<td>
										<?php if ($item->payType === 'fuyou') { ?>富有<?php } ?>
										<?php if ($item->payType === 'wft') { ?>威富通<?php } ?>
										<?php if ($item->payType === 'YB') { ?>易宝支付<?php } ?>
										<?php if ($item->payType === 'weichet') { ?>qq钱包<?php } ?>
										<?php if ($item->payType === 'ld') { ?>零度支付<?php } ?>
										<?php if ($item->payType === 'QH') { ?>千红支付<?php } ?>
										<?php if ($item->payType === 'TK') { ?>滕坤支付<?php } ?>
										<?php if ($item->payType === 'WP') { ?>微派支付<?php } ?>
										<?php if ($item->payType === 'alipay') { ?>支付宝支付<?php } ?>
										<?php if ($item->payType === 'zny') { ?>智能云<?php } ?>
										<?php if ($item->payType === 'wxh5') { ?>微信H5<?php } ?>
										</td>
										<td>
										<?php if ($item->status === '0') { ?>停用
										<?php } elseif ($item->status === '1') { ?>启用
										<?php } ?>
										</td>
										<td>
											<a href="<?= $apppath ?>/recharge/list?id=<?= $item->id ?>">
												<button class="btn btn-warning btn-labeled fa fa-edit">更新</button>
											</a>
										</td>
									</tr>
								<?php } ?>
								<?php } ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
	</div>
</div>

<!--文章模块结束--!>
