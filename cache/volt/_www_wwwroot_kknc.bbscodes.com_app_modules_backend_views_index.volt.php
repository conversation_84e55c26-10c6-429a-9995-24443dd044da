<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?= $webinfo['pageTitle'] ?></title>
<meta name="description" content="<?= $webinfo['info'] ?>">
<link rel="icon" href="<?= $webinfo['logo'] ?>">

	<?= $this->tag->stylesheetLink('/assets/lib/bootstrap/css/bootstrap.css', false) ?>
	<?= $this->tag->stylesheetLink('/assets/lib/nifty/css/nifty.css', false) ?>
	<?= $this->tag->stylesheetLink('/assets/lib/font-awesome/css/font-awesome.min.css', false) ?>
	<script src="/assets/lib/require.js"></script>
	<script src="/assets/lib/main.js"></script>
</head>
<body>

<div id="container" class="effect aside-float aside-bright navbar-fixed footer-fixed mainnav-fixed mainnav-lg">
	<?php if (!isset($login)) { ?>
<div id="container" class="cls-container">
	<div class="cls-content">
		<div class="cls-content-sm panel">
			<div class="panel-body" style=" background-color: #9acfea ;margin-top: 100px">
				<div class="mar-ver pad-btm">
					<h3 class="h4 mar-no">后台管理系统登录</h3>
					<p class="mar-no">欢迎使用</p>
				</div>
				<form action="<?= $apppath ?>/index/auth" method="post">
					<div class="form-group">
						<input type="text" class="form-control" placeholder="帐号" name="username" value="<?php if (isset($user)) { ?><?= $user ?><?php } ?>">
					</div>
					<div class="form-group">
						<input type="password" class="form-control" placeholder="密码" name="password">
					</div>
					<div class="checkbox pad-btm text-left">
						<input id="demo-form-checkbox" name="remember" value="1" class="magic-checkbox" <?php if (isset($user)) { ?>checked<?php } ?> type="checkbox" style=" margin-left: 0">
						<label for="demo-form-checkbox">记住帐号</label>
					</div>
					<button class="btn btn-primary btn-lg btn-block" type="submit">登录</button>
				</form>
			</div>
	</div>
</div>
	</div>
<?php } ?>
<div id="container" class="effect aside-float aside-bright navbar-fixed footer-fixed mainnav-fixed mainnav-lg">
	<?php if (isset($login)) { ?>
	<?php if ($login === 'login') { ?>
<header id="navbar">
	<div id="navbar-container" class="boxed">
		<div class="navbar-header">
			<a href="#" class="navbar-brand">
				<img src="/assets/img/logo.png" alt="夺金农场" class="brand-icon">
				<div class="brand-title">
					<span class="brand-text">Nifty</span>
				</div>
			</a>
		</div>
		<div class="navbar-content clearfix">
			<ul class="nav navbar-top-links pull-right">
				<li id="dropdown-user" class="dropdown">
					<a href="#" data-toggle="dropdown" class="dropdown-toggle text-right" aria-expanded="false">
					<span class="pull-right">
						<img class="img-circle img-user media-object" src="/assets/img/avatar/1.png" alt="Profile Picture">
					</span>
						<div class="username hidden-xs"><i class="fa fa-user-circle-o"></i><strong><?php if (isset($admin)) { ?><?= $admin ?><?php } ?></strong></div>
					</a>

					<div class="dropdown-menu dropdown-menu-md dropdown-menu-right panel-default">
						<ul class="head-list">
							<li>
								<a href="#">
									<i class="demo-pli-male icon-lg icon-fw"></i> 修改资料
								</a>
							</li>
							
								
									
									
								
							
							<li>
								<a href="<?= $apppath ?>/config/index">
									<span class="label label-success pull-right">New</span>
									<i class="demo-pli-gear icon-lg icon-fw"></i> 系统设置
								</a>
							</li>
						</ul>
						<div class="pad-all text-right">
							<a href="<?= $apppath ?>/index/out" class="btn btn-primary">
								<i class="demo-pli-unlock"></i>退出
							</a>
						</div>
					</div>
				</li>
			</ul>
		</div>
	</div>
</header>
<?php } ?>
<?php } ?>

	<?php if (isset($login)) { ?>
	<?php if ($login === 'login') { ?>
<div class="boxed">
	<div id="content-container">
		<div id="page-content">
		<?= $this->getContent() ?>
		</div>
	</div>
	<nav id="mainnav-container">
		<div id="mainnav">
			<div id="mainnav-menu-wrap">
				<div class="nano">
					<div class="nano-content" style="padding-top: 0">
						<ul id="mainnav-menu" class="list-group">
							<li>
								<a href="<?= $apppath ?>/article/category?op=list">
									<i class="fa fa-home"></i>
									<span class="menu-title"><strong>控制台首页</strong></span>
								</a>
							</li>
							<li class="list-header">基础功能</li>
							<?php if (isset($article)) { ?>
							<li>
								<a href="#">
									<i class="fa fa-file-text"></i>
									<span class="menu-title"><strong>文章管理</strong></span>
									<i class="arrow"></i>
								</a>
								<ul class="collapse">
									<li><a href="<?= $apppath ?>/article/category?op=list">文章分类</a></li>
									<li><a href="<?= $apppath ?>/article/article?op=list">文章列表</a></li>
									<li><a href="<?= $apppath ?>/article/raiders?op=list">攻略心得</a></li>
								</ul>

							</li>
							<?php } ?>
							<li>
								<a href="#">
									<i class="fa fa-line-chart"></i>
									<span class="menu-title"><strong>大盘管理</strong></span>
									<i class="arrow"></i>
								</a>
								<ul class="collapse">
									<li><a href="<?= $apppath ?>/product/plist?op=list&page=1">交易大厅</a></li>
									<?php if (isset($product)) { ?>
									<li><a href="<?= $apppath ?>/product/product/page">产品列表</a></li>
									<?php } ?>
									<li><a href="<?= $apppath ?>/product/pdata/page">交易数据</a></li>
									<li><a href="<?= $apppath ?>/product/logs">日志管理</a></li>
									<li><a href="<?= $apppath ?>/product/give?status=3">赠送记录</a></li>
									<li><a href="<?= $apppath ?>/product/config">功能设置</a></li>
									<li><a href="<?= $apppath ?>/product/orderReturn">订单退回</a></li>
									<?php if ($user_type == 'chuangjin') { ?>
									<li><a href="<?= $apppath ?>/product/landfixed">土地修复</a></li>
									<?php } ?>
									<li><a href="<?= $apppath ?>/user/commission">佣金统计</a></li>
									
									<li><a href="<?= $apppath ?>/product/frozen">冻结产品比对</a></li>
									<li><a href="<?= $apppath ?>/user/idcard?page=1">身份证审核</a></li>
								</ul>
							</li>
							<?php if (isset($user)) { ?>
							<li>
								<a href="<?= $apppath ?>/user/list?op=list">
									<i class="fa fa-user-circle-o"></i>
									<span class="menu-title"><strong>用户管理</strong></span>
								</a>
							</li>
							<li>
                            	<a href="<?= $apppath ?>/user/online">
                            		<i class="fa fa-user-circle-o"></i>
                            		<span class="menu-title"><strong>在线人数统计</strong></span>
                            	</a>
                            </li>
							<?php } ?>
							<li>
								<?php if (isset($warehouse)) { ?>
								
								<a href="<?= $apppath ?>/warehouse/list?op=manage">
									<i class="fa fa-pie-chart"></i>
									<span class="menu-title"><strong>数据统计</strong></span>
								</a>
							</li>
								<?php } ?>
							<?php if (isset($userwithdraw)) { ?>
						            <li>
                                         <a href="#">
                                             <i class="fa fa-dollar"></i>
                                                <span class="menu-title"><strong>提现管理</strong></span>
                                             <i class="arrow"></i>
                                         </a>
                                         <ul class="collapse">
                                             <li><a href="<?= $apppath ?>/userwithdraw/list?op=list">提现管理</a></li>
                                             <li><a href="<?= $apppath ?>/userwithdraw/over?page=1">提现完成</a></li>
                                             <?php if ($user_type == 'shennongzhuangyuan') { ?>
                                             <li><a href="<?= $apppath ?>/userwithdraw/virtual?page=1">虚拟币提现</a></li>
                                             <?php } ?>
                                         </ul>
                                    </li>
						
								<?php } ?>

								<?php if (isset($jurisdiction)) { ?>
							<li>
								
								<a href="#">
									<i class="fa fa-balance-scale"></i>
									<span class="menu-title"><strong>权限与支付</strong></span>
									<i class="arrow"></i>
								</a>
								<ul class="collapse">
								<li>
									<a href="<?= $apppath ?>/jurisdiction/list">
									<i class="fa fa-unlock-alt"></i>权限管理</a>
								</li>
								<li><a href="<?= $apppath ?>/recharge/show">
									<i class="fa fa-unlock-alt"></i>支付设置</a>
								</li>
								<li><a href="<?= $apppath ?>/recharge/recharge">
                                	<i class="fa fa-unlock-alt"></i>支付列表</a>
                                </li>
								</ul>
							</li>
							<?php } ?>
							<?php if (isset($config)) { ?>
							<li class="list-header">系统管理</li>
							<li>
								<a href="#">
									<i class="fa fa-cog"></i>
									<span class="menu-title"><strong>系统设置</strong></span>
									<i class="arrow"></i>
								</a>
								<ul class="collapse">
									<li><a href="<?= $apppath ?>/config/index">站点设置</a></li>
									<li><a href="#">附件设置</a></li>
									<li><a href="<?= $apppath ?>/config/sitMessage">短信设置</a></li>
									<li><a href="<?= $apppath ?>/config/other">其他设置</a></li>
								</ul>
							</li>
							<li>
								<a href="#">
									<i class="fa fa-desktop"></i>
									<span class="menu-title"><strong>前台设置</strong></span>
									<i class="arrow"></i>
								</a>
								<ul class="collapse">
									<li><a href="<?= $apppath ?>/config/slide?op=list">首页幻灯</a></li>
								</ul>
								<ul class="collapse">
                                	<li><a href="<?= $apppath ?>/config/groups">图片设置</a></li>
                                </ul>
								<ul class="collapse">
									<li><a href="<?= $apppath ?>/config/service">服务中心</a></li>
								</ul>
								<ul class="collapse">
                                	<li><a href="<?= $apppath ?>/config/copyright">公司信息</a></li>
                                </ul>
                                <ul class = "collapse">
                               	 <li><a href="<?= $apppath ?>/config/gameinfo">游戏信息</a></li>
                                </ul>
                                <?php if ($user_type == 'shennongzhuangyuan') { ?>
                                <ul class = "collapse">
                                   <li><a href="<?= $apppath ?>/config/userwithdraw">提现设置</a></li>
                                </ul>
                                <?php } ?>
							</li>
							<?php if (isset($spread)) { ?>
							<li>
								<a href="<?= $apppath ?>/disconfig/index">
									<i class="fa fa-dollar"></i>
									<span class="menu-title"><strong> 推广设置</strong></span>
								</a>
							</li>
							<?php } ?>
							<?php } ?>
							<?php if (isset($orchard)) { ?>
							<li class="list-header">游戏管理</li>
							<li>
								<a href="#">
									<i class="fa fa-cog"></i>
									<span class="menu-title"><strong>基础管理</strong></span>
									<i class="arrow"></i>
								</a>
								<ul class="collapse">
									<li><a href="<?= $apppath ?>/orchard/config">参数设置</a></li>
									<li><a href="<?= $apppath ?>/orchard/goods">商品设置</a></li>
									<li><a href="<?= $apppath ?>/orchard/user">会员管理</a></li>
									<li><a href="<?= $apppath ?>/orchard/logs">日志管理</a></li>
									<li><a href="<?= $apppath ?>/orchard/order?op=display&payStatus=1">订单管理</a></li>
									<li><a href="<?= $apppath ?>/orchard/orchard">果园管理</a></li>
									<li><a href="<?= $apppath ?>/orchard/dog">宠物管理</a></li>
								</ul>
							</li>
							<?php } ?>
							<?php if (isset($core)) { ?>
							<li class="list-header">核心功能</li>
                            	<li>
                            		<a href="#">
                            			<i class="fa fa-cog"></i>
                            			<span class="menu-title"><strong>核心功能</strong></span>
                            			<i class="arrow"></i>
                            		</a>
                            		<ul class="collapse">
                            			<li><a href="<?= $apppath ?>/user/list?op=recharge">金币充值</a></li>
                            			<li><a href="<?= $apppath ?>/orchard/user?op=admin">游戏钻石</a></li>
                            			<li><a href="<?= $apppath ?>/warehouse/list?op=addp">产品充值</a></li>
                            		</ul>
                            	</li>
							<?php } ?>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</nav>
</div>
	<?php } ?>
<?php } ?>

	<footer id="footer">
	<div class="show-fixed pull-right"><?= $webinfo['pageTitle'] ?> - 控制台</div>
	<p class="pad-lft"><?= $copyright['copyright'] ?></p>
</footer>
<script>
	requirejs(['nifty'], function () {
		$(document).trigger('nifty.ready');
	});
</script>

</div>


</div>
</body>
</html>
