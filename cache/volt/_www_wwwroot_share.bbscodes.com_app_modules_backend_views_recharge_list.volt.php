<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="<?php if (isset($show)) { ?><?php if ($show === 'list') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a data-toggle="tab" href="#demo-lft-tab-1" aria-expanded="<?php if (isset($show)) { ?><?php if ($show === 'list') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">支付配置</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($show)) { ?><?php if ($show === 'list') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/recharge/show">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label"><?php if (isset($item->payType) && $item->payType == 'WP') { ?>应用编号<?php } elseif ($item->payType == 'alipay') { ?>收款账号<?php } else { ?>商户号<?php } ?>*</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" id="demo-vs-definput" type="text" name="merchant_no" value="<?php if (isset($item->merchant_no)) { ?><?= $item->merchant_no ?><?php } ?>">
								</div>
							</div>

                            <?php if(@$item->payType != 'YB'): ?>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label"><?php if (isset($item->payType) && $item->payType == 'WP') { ?>app_id<?php } elseif ($item->payType == 'alipay') { ?>合作者身份<?php } else { ?>终端号<?php } ?>*</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" id="demo-vs-definput" type="text" name="terminal_id" value="<?php if (isset($item->terminal_id)) { ?><?= $item->terminal_id ?><?php } ?>">
								</div>
							</div>
                            <?php endif; ?>

							<div class="form-group">
                            	<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">支付类型*</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<select class="form-control" id="demo-vs-definput" name="payType">
										<option value="fuyou"<?php if (isset($item->payType)) { ?><?php if ($item->payType === 'fuyou') { ?>selected<?php } ?><?php } ?>>富有支付</option>
										<option value="wft"<?php if (isset($item->payType)) { ?><?php if ($item->payType === 'wft') { ?>selected<?php } ?><?php } ?>>威富通</option>
										<option value="YB"<?php if (isset($item->payType)) { ?><?php if ($item->payType === 'YB') { ?>selected<?php } ?><?php } ?>>易宝支付</option>
										<option value="QH"<?php if (isset($item->payType)) { ?><?php if ($item->payType === 'QH') { ?>selected<?php } ?><?php } ?>>千红支付</option>
										<option value="TK"<?php if (isset($item->payType)) { ?><?php if ($item->payType === 'TK') { ?>selected<?php } ?><?php } ?>>滕坤支付</option>
										<option value="WP"<?php if (isset($item->payType)) { ?><?php if ($item->payType === 'WP') { ?>selected<?php } ?><?php } ?>>微派支付</option>
										<option value="alipay"<?php if (isset($item->payType)) { ?><?php if ($item->payType === 'alipay') { ?>selected<?php } ?><?php } ?>>支付宝</option>
										<option value="zny" <?php if (isset($item->payType)) { ?><?php if ($item->payType === 'zny') { ?>selected<?php } ?><?php } ?>>智能云</option>
										<option value="wxh5" <?php if (isset($item->payType)) { ?><?php if ($item->payType === 'wxh5') { ?>selected<?php } ?><?php } ?>>微信H5</option>
									</select>
									<span class="help-block">切换类型或新增支付需要先提交类型然后重新编辑，才会出现其他选项</span>
								</div>
                            </div>

                            <?php if(@$item->payType != 'YB'): ?>
                            <div class="form-group">
                                 <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label"><?php if (isset($item->payType) && $item->payType == 'WP') { ?>app_key<?php } elseif ($item->payType == 'alipay') { ?>校验密钥<?php } else { ?>签名<?php } ?>*</label>
								 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" id="demo-vs-definput" type="text" name="access_token" value="<?php if (isset($item->access_token)) { ?><?= $item->access_token ?><?php } ?>">
								 </div>
                            </div>
                            <?php endif; ?>

                            <?php if(@$item->payType == 'YB' || @$item->payType == 'alipay'): ?>
                            <div class="form-group">
                                 <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">商户私钥*</label>
								 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								 	<textarea rows="5" class="form-control" placeholder="商户私钥" name="other[merchantPrivateKey]"><?= @$item->other['merchantPrivateKey'] ?></textarea>
								 </div>
                            </div>
                            <div class="form-group">
                                 <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">商户公钥*</label>
								 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								 	<textarea rows="5" class="form-control" placeholder="商户公钥" name="other[merchantPublicKey]"><?= @$item->other['merchantPublicKey'] ?></textarea>
								 </div>
                            </div>
                             <?php if(@$item->payType == 'YB'): ?>
                            <div class="form-group">
                                 <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">易宝公钥*</label>
								 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								 	<textarea rows="5" class="form-control" placeholder="易宝公钥" name="other[yeepayPublicKey]"><?= @$item->other['yeepayPublicKey'] ?></textarea>
								 </div>
                            </div>
                            <?php endif; ?>
                            <?php endif; ?>

                            <div class="form-group">
                                  <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">启用状态</label>
                                     <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                         <select class="form-control" id="demo-vs-definput" name="status">
                                          <option value="1"<?php if (isset($item->status)) { ?><?php if ($item->status === '1') { ?>selected<?php } ?><?php } ?>>启用</option>
                                          <option value="0"<?php if (isset($item->status)) { ?>
                                          <?php if ($item->status === '0') { ?>selected<?php } ?><?php } ?>>停用</option>
                                      </select>
                                     </div>
                                  </div>
							<div class="panel-footer text-left">
							<input type='hidden' name='id' value ="<?php if (isset($item->id)) { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">保存</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
