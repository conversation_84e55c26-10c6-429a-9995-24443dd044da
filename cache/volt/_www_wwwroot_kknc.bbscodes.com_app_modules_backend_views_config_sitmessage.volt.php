<div class="tab-base">

	<?php $this->flashSession->output(); ?>
	<!--Nav Tabs-->
	<ul class="nav nav-tabs">
		<li class="active">
			<a data-toggle="tab" href="#">短信设置</a>
		</li>
	</ul>
	<!--Tabs Content-->
	<div class="tab-content">
		<div class="tab-pane fade active in">
			<div class="panel">
				<div class="panel-heading">
				<h3 class="panel-title">请认真填写</h3>
				</div>
				<div class="panel-body">
					<form class="form-horizontal form-padding" method = 'post'>
						<?php if ($type == 'message') { ?>
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">用户id</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input type="text" name="userid"  class="form-control" value="<?php if (isset($message['userid'])) { ?><?= $message['userid'] ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">帐号</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<input type="text" name="account"  class="form-control" value="<?php if (isset($message['account'])) { ?><?= $message['account'] ?><?php } ?>">
                           </div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">密码</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<input type="password" name="password" class="form-control"value="<?php if (isset($message['password'])) { ?><?= $message['password'] ?><?php } ?>">
                            </div>
						</div>
						<div class="form-group">
                        	<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">短信签名</label>
                        	<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                        	<input type="text" name="sign" class="form-control"value="<?php if (isset($message['sign'])) { ?><?= $message['sign'] ?><?php } ?>">
                             </div>
                        </div>
                        <div class="form-group">
                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
                               <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                               <select class="form-control" id="demo-vs-definput" name="status">
                                    <option value="1"<?php if (isset($message['status'])) { ?><?php if ($message['status'] === '1') { ?>selected<?php } ?><?php } ?>>启用</option>
                                    <option value="0"<?php if (isset($message['status'])) { ?><?php if ($message['status'] === '0') { ?>selected<?php } ?><?php } ?>>禁用</option>
                               </select>
                               </div>
                         </div>
                             <input type="hidden" name="type" class="form-control" value="message">

                         <div class="form-group">
                               <label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">短信接口地址</label>
                              <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                  <input type="text" name="url" class="form-control" value="<?php if (isset($message['url'])) { ?><?= $message['url'] ?><?php } ?>">
                             </div>
                          </div>
                         <?php } elseif ($type == 'jh') { ?>
                        <div class="form-group">
                            <label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">(聚合)短信模版id</label>
                            <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                            <input type="text" name="tpl_id" class="form-control"value="<?php if (isset($message['tpl_id'])) { ?><?= $message['tpl_id'] ?><?php } ?>">
                            </div>
                        </div>
                        <div class="form-group">
                             <label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">应用APPKEY</label>
                             <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                               <input type="text" name="key" class="form-control"value="<?php if (isset($message['key'])) { ?><?= $message['key'] ?><?php } ?>">
                             </div>
                        </div>
						<div class="form-group">
                           <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">短信类型*</label>
                           <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                             <select class="form-control" id="demo-vs-definput" name="type">
                                 <option value="message"<?php if (isset($message['type'])) { ?><?php if ($message['type'] === 'message') { ?>selected<?php } ?><?php } ?>>鼎汉</option>
                                 <option value="jh"<?php if (isset($message['type'])) { ?><?php if ($message['type'] === 'jh') { ?>selected<?php } ?><?php } ?>>聚合</option>
                             </select>
                           </div>
                       </div>
                       <div class="form-group">
                                                     <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
                                                      <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                      <select class="form-control" id="demo-vs-definput" name="status">
                                                           <option value="1"<?php if (isset($message['status'])) { ?><?php if ($message['status'] === '1') { ?>selected<?php } ?><?php } ?>>启用</option>
                                                           <option value="0"<?php if (isset($message['status'])) { ?><?php if ($message['status'] === '0') { ?>selected<?php } ?><?php } ?>>禁用</option>
                                                      </select>
                                                      </div>
                        </div>

                         <input type="hidden" name="type" class="form-control" value="jh">
                       <div class="form-group">
                            <label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">短信接口地址</label>
                            <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                               <input type="text" name="url" class="form-control" value="<?php if (isset($message['url'])) { ?><?= $message['url'] ?><?php } ?>">
                            </div>
                       </div>
                       <?php } ?>
                    	<div class="panel-footer text-right">
                        	<button class="btn btn-success" type="submit">提交</button>
                        </div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
