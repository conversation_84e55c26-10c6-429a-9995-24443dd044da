<div class="tab-base">
	<ul class="nav nav-tabs">
		<li class="active">
			<a href="./index">推广设置</a>
		</li>
	</ul>

	<div class="tab-content">
		<div class="tab-pane fade active in">
			<form class="form-horizontal" method="post">
				<div class="panel">
					<div class="panel-heading">
						<h3 class="panel-title">农场</h3>
					</div>
					<div class="panel-body">
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">推广比例</label>
							<div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
								<div class="input-group" data-level="1">
									
									<span class="input-group-addon">一级</span>
									<input type="text" name="rebate[1][common][1]" value="<?php if (isset($rebate['1']['common'][1])) { ?><?= $rebate['1']['common'][1] ?><?php } ?>" class="form-control">
									<span class="input-group-addon">%</span>
								</div>
								<div class="input-group" data-level="2">
									<span class="input-group-addon">二级</span>
									<input type="text" name="rebate[1][common][2]" value="<?php if (isset($rebate['1']['common'][2])) { ?><?= $rebate['1']['common'][2] ?><?php } ?>" class="form-control">
									<span class="input-group-addon">%</span>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div class="panel">
					<div class="panel-heading">
						<h3 class="panel-title">庄园</h3>
					</div>
					<div class="panel-body">
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">推广比例</label>
							<div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
								<div class="input-group" data-level="1">
									
									<span class="input-group-addon">一级</span>
									<input type="text" name="rebate[2][common][1]" value="<?php if (isset($rebate['2']['common'][1])) { ?><?= $rebate['2']['common'][1] ?><?php } ?>" class="form-control">
									<span class="input-group-addon">%</span>
								</div>
								<div class="input-group" data-level="2">
									<span class="input-group-addon">二级</span>
									<input type="text" name="rebate[2][common][2]" value="<?php if (isset($rebate['2']['common'][2])) { ?><?= $rebate['2']['common'][2] ?><?php } ?>" class="form-control">
									<span class="input-group-addon">%</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="panel">
                		<div class="panel-heading">
                				<h3 class="panel-title">渠道佣金</h3>
                		</div>
                		<div class="panel-body">
                			<?php if ($yansheng == 'yansheng') { ?>
                			<div class="form-group">
								<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">推广比例</label>
								<div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
									<div class="input-group" data-level="1">
										
										<span class="input-group-addon">一级</span>
										<input type="text" name="rebate[3][common][1]" value="<?php if (isset($rebate['3']['common'][1])) { ?><?= $rebate['3']['common'][1] ?><?php } ?>" class="form-control">
										<span class="input-group-addon">%</span>
									</div>
									<div class="input-group" data-level="2">
										<span class="input-group-addon">二级</span>
										<input type="text" name="rebate[3][common][0]" value="<?php if (isset($rebate['3']['common'][0])) { ?><?= $rebate['3']['common'][0] ?><?php } ?>" class="form-control">
										<span class="input-group-addon">%</span>
									</div>
								</div>
							</div>
							<?php } ?>
                			<div class="form-group">
                			<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                            	<label class="radio-inline">
                            		<input type="radio" name="types" value="0" <?php if (isset($channel_billing_type)) { ?><?php if ($channel_billing_type != 0) { ?>checked<?php } ?><?php } ?>> 次月一号领取
                            	</label>
                            	<label class="radio-inline">
                            		<input type="radio" name="types" value="1"<?php if (isset($channel_billing_type)) { ?><?php if ($channel_billing_type == 1) { ?>checked<?php } ?><?php } ?>>   立即领取
                            	</label>
                             </div>
                		</div>
                		</div>
                </div>
                </div>

				<div class="panel-footer text-right">
					<button class="btn btn-success" type="submit">提交</button>
				</div>
			</form>
		</div>
	</div>
</div>
