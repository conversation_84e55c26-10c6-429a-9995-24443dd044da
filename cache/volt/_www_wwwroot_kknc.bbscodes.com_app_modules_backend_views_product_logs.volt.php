<?php $this->flashSession->output(); ?>
<!--商品信息-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="active">
				<a href="<?= $apppath ?>/product/logs?page=<?= $logsList->current ?>" aria-expanded="true">日志列表</a>
			</li>
		</ul>
		<div id="demo-lft-tab-1" class="tab-pane fade active in">
		<div class="tab-content">
        			<div class="panel-body">
        				<form class="form-horizontal form-padding " method="get" action="<?= $apppath ?>/product/logs">
        					<div class="form-group">
        						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索信息</label>
        						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
        							<input class="form-control" type="text" name="keywords" value="<?php if (isset($keywords)) { ?><?= $keywords ?><?php } ?>" placeholder="请输入会员编号">
        						</div>
        					</div>
        					<div class="form-group">
        						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">信息类型</label>
        						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
        							<select name="type" class="form-control">
        								<option value="all" <?php if (isset($type)) { ?><?php if ($type === 'all') { ?>selected<?php } ?><?php } ?>>
                                            	全部
                                         </option>
        								<option value="addcoing" <?php if (isset($type)) { ?><?php if ($type === 'addcoing') { ?>selected<?php } ?><?php } ?>>
        									增加金币
        								</option>
        								<option value="addproduct" <?php if (isset($type)) { ?><?php if ($type === 'addproduct') { ?>selected<?php } ?><?php } ?>>
                                           增加产品
                                        </option>
                                         <option value="dedproduct" <?php if (isset($type)) { ?><?php if ($type === 'dedproduct') { ?>selected<?php } ?><?php } ?>>
                                            扣除产品
                                         </option>
                                          <option value="dedcoing" <?php if (isset($type)) { ?><?php if ($type === 'dedcoing') { ?>selected<?php } ?><?php } ?>>
                                               扣除金币
                                           </option>
                                         <option value="addfrozenproduct" <?php if (isset($type)) { ?><?php if ($type === 'addfrozenproduct') { ?>selected<?php } ?><?php } ?>>
                                              增加冻结产品
                                          </option>
                                          <option value="addfrozencoing" <?php if (isset($type)) { ?><?php if ($type === 'addfrozencoing') { ?>selected<?php } ?><?php } ?>>
                                               增加冻结金币
                                          </option>
                                          <option value="dedfrozenproduct" <?php if (isset($type)) { ?><?php if ($type === 'dedfrozenproduct') { ?>selected<?php } ?><?php } ?>>
                                              扣除冻结产品
                                          </option>
                                          <option value="dedfrozencoing" <?php if (isset($type)) { ?><?php if ($type === 'dedfrozencoing') { ?>selected<?php } ?><?php } ?>>
                                              扣除冻结金币
                                          </option>
        							</select>
        						</div>
        					</div>
        					<div class="form-group">
        						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
        						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
        									<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
        						</div>
        					</div>
        					<div class="text-lg-center">
        						<button class="btn btn-info fa fa-search" type="submit">搜索</button>
        					</div>
        				</form>
        			</div>
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
									<tr>
										<th>日志ID</th>
										<th>会员ID</th>
										<th>用户帐号</th>
										<th>数量</th>
										<th>操作内容</th>
										<th>状态</th>
										<th>时间</th>
										<th>操作类型</th>
									</tr>
								</thead>
								<tbody>
								<?php if (isset($logs)) { ?>
								<?php foreach ($logs['list'] as $row) { ?>
								<tr>
									<td><?= $row['id'] ?></td>
									<td><?= $row['uid'] ?></td>
									<td><?= $row['mobile'] ?></td>
									<td><?= $row['num'] ?></td>
									<td><?= $row['logs'] ?></td>
									<td>
									<?php if ($row['status'] == 1) { ?>正常<?php } ?>
									<?php if ($row['status'] != 1) { ?>异常<?php } ?>
									</td>
									<td>
									<?= date('Y-m-d H:i:s', $row['createtime']) ?>
									</td>
									<td>
									<?php if ($row['type'] == 'addfrozenproduct') { ?>产品冻结<?php } ?>
									<?php if ($row['type'] == 'dedfrozenproduct') { ?>扣除冻结产品<?php } ?>
									<?php if ($row['type'] == 'dedfrozencoing') { ?>扣除冻结金币<?php } ?>
									<?php if ($row['type'] == 'addfrozencoing') { ?>冻结金币<?php } ?>
									<?php if ($row['type'] == 'addcoing') { ?>增加金币<?php } ?>
									<?php if ($row['type'] == 'dedcoing') { ?>扣除金币<?php } ?>
									<?php if ($row['type'] == 'addproduct') { ?>增加产品<?php } ?>
									<?php if ($row['type'] == 'dedproduct') { ?>扣除产品<?php } ?>
									</td>
									</tr>
								<?php } ?>
								<?php } ?>
								</tbody>
							</table>
							<div class="panel-body text-center">
								<?php if ($logs['total_pages'] > 1) { ?>
                                	<ul class="pagination">
                                		<li><a href="<?= $apppath ?>/product/logs?page=1<?php if (isset($type)) { ?>&type=<?= $type ?><?php } ?><?php if (isset($starttime)) { ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php } ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?>" class="demo-pli-arrow-right">首页</a></li>
                                		<li><a href="<?= $apppath ?>/product/logs?page=<?= $logs['before'] ?><?php if (isset($type)) { ?>&type=<?= $type ?><?php } ?><?php if (isset($starttime)) { ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php } ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?>">上一页</a></li>
                                		<li><a href="#">第<?= $logs['current'] ?>页</a></li>
                                		<li><a href="#">共<?= $logs['total_pages'] ?>页</a></li>
                                		<li><a href="<?= $apppath ?>/product/logs?page=<?= $logs['next'] ?><?php if (isset($type)) { ?>&type=<?= $type ?><?php } ?><?php if (isset($starttime)) { ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php } ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?>">下一页</a></li>
                                		<li><a href="<?= $apppath ?>/product/logs?page=<?= $logs['total_pages'] ?><?php if (isset($type)) { ?>&type=<?= $type ?><?php } ?><?php if (isset($starttime)) { ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php } ?><?php if (isset($keywords)) { ?>&keywords=<?= $keywords ?><?php } ?>" class="demo-pli-arrow-right">尾页</a></li>
                                	</ul>
                                <?php } ?>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!--日志模块结束--!>

