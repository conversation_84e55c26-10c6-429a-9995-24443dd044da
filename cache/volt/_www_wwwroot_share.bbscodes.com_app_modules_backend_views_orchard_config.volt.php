<?php $this->flashSession->output(); ?>
<div class="nav-tabs-custom">
	<div class="tab-base">
		<!--Nav Tabs-->
		<ul class="nav nav-tabs">
			<li class="<?php if (isset($op)) { ?><?php if ($op === 'post') { ?>active<?php } else { ?> <?php } ?><?php } ?> ">
				<a href="<?= $apppath ?>/orchard/config?op=post" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'post') { ?>true<?php } else { ?>false <?php } ?><?php } ?>">基本设置</a>
			</li>
			<?php if ($item != '' && $item->id != 0) { ?>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'duiset') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=duiset" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'duiset') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">兑换设置</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'house') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=house" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'house') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">房屋等级</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'dogInfo') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=dogInfo" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'dogInfo') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">宠物等级</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'landInfo') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=landInfo" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'landInfo') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">土地信息</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'statueInfo') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=statueInfo" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'statueInfo') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">神像信息</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'recharge') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=recharge" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'recharge') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">充值设置</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'background') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=background" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'background') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">背景信息</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'package') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=package" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'package') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">礼包信息</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'sign') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=sign" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'sign') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">签到信息</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'steal') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=steal" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'steal') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">互偷信息</a>
			</li>
          <li class="<?php if (isset($op)) { ?><?php if ($op == 'downgrade') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=downgrade" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'downgrade') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">降级信息</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'pasture') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=pasture" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'pasture') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">牧场限制</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'authcode') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=authcode" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'authcode') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">商城同步</a>
			</li>
			<?php if ($user_type == 'huangjin') { ?>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'crystal') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
            	<a href="<?= $apppath ?>/orchard/config?op=crystal" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'crystal') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">水晶兑换</a>
            </li>
            <?php } ?>
            <?php if ($user_type == 'jinlilai') { ?>
            <li class="<?php if (isset($op)) { ?><?php if ($op == 'downgrade') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
                <a href="<?= $apppath ?>/orchard/config?op=downgrade" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'downgrade') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">降级信息</a>
            </li>
            <?php } ?>
            <?php if ($user_type == 'chuangjin') { ?>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'downgrade') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=downgrade" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'downgrade') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">降级信息</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'pasture') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=pasture" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'pasture') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">牧场限制</a>
			</li>
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'authcode') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/config?op=authcode" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'authcode') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">商城同步</a>
			</li>
			<?php } ?>
			 <?php if ($user_type == 'kk') { ?>
			 <li class="<?php if (isset($op)) { ?><?php if ($op == 'pasture') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
             				<a href="<?= $apppath ?>/orchard/config?op=pasture" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'pasture') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">牧场限制</a>
             			</li>
			 <?php } ?>
			<?php if ($user_type == 'EMG') { ?>
            			<li class="<?php if (isset($op)) { ?><?php if ($op == 'EMG') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
            				<a href="<?= $apppath ?>/orchard/config?op=EMG" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'EMG') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">	接口授权</a>
            			</li>
            			<?php } ?>
			<?php } ?>

		</ul>
		<?php if ($op == 'post') { ?>
		<!--Tabs Content-->
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'post') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=post">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">标题</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" id="demo-vs-definput" type="text" name="title" value="<?php if ($item != '') { ?><?= $item->title ?><?php } ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">原始种子库存</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" id="demo-vs-definput" type="text" name="total" value="<?php if ($item != '') { ?><?= $item->total ?><?php } ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">当前用户可升级最高等级</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" id="demo-vs-definput" type="text" name="upGrade" value="<?php if ($item != '') { ?><?= $item->upGrade ?><?php } ?>">
								</div>
							</div>
							<?php if ($hostType == 'kk') { ?>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">推荐人需钻石库存</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" id="demo-vs-definput" type="text" name="SDiamondHave" value="<?php if ($item != '') { ?><?= $item->SDiamondHave ?><?php } ?>">
								</div>
							</div>
							<?php } ?>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<select name="status" class="form-control">
										<option value="1" <?php if ($item != '') { ?><?php if ($item->status == 1) { ?>selected<?php } ?><?php } ?>>启用</option>
										<option value="0" <?php if ($item != '') { ?><?php if ($item->status == 0) { ?>selected<?php } ?><?php } ?>>禁用</option>
									</select>
								</div>
							</div>
							<div class="panel-footer text-left">
								<input class="form-control"  type="hidden" name="id" value="<?php if ($item != '') { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<?php } ?>
		<?php if ($op == 'duiset') { ?>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'duiset') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=duiset">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">兑换材料信息</label>
								<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
									<div  class="input-group">
										<span class="input-group-addon"><?php echo $duiType[1];?>/块兑换需</span>
										<select name="duiInfo[1][1][pid]" class="form-control">
										<?php foreach ($product as $keys => $lists) { ?>
											<option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[1][1]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
												<?= $lists->title ?>
											</option>
										<?php } ?>
										</select>
										<span class="input-group-addon">数量</span>
										<input type="text" name="duiInfo[1][1][num]" value="<?php if (!empty($duiInfo) && $duiInfo[1][1]['num'] >0){ echo $duiInfo[1][1]['num']; }?>"  class="form-control">
										<span class="input-group-addon">+</span>
										<select name="duiInfo[1][2][pid]" class="form-control">
										<?php foreach ($product as $keys => $lists) { ?>
											<option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[1][2]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
												<?= $lists->title ?>
											</option>
										<?php } ?>
										</select>
										<span class="input-group-addon">数量</span>
										<input type="text" name="duiInfo[1][2][num]" value="<?php if (!empty($duiInfo) && $duiInfo[1][2]['num'] >0){ echo $duiInfo[1][2]['num']; }?>"  class="form-control">
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+</span>
										<select name="duiInfo[1][3][pid]" class="form-control">
										<?php foreach ($product as $keys => $lists) { ?>
											<option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[1][3]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
												<?= $lists->title ?>
											</option>
										<?php } ?>
										</select>
										<span class="input-group-addon">数量</span>
										<input type="text" name="duiInfo[1][3][num]" value="<?php if (!empty($duiInfo) && $duiInfo[1][3]['num'] >0){ echo $duiInfo[1][3]['num']; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'jinlilai') { ?>
                                        <span class="input-group-addon">+</span>
                                        <select name="duiInfo[1][3][pid]" class="form-control">
                                        <?php foreach ($product as $keys => $lists) { ?>
                                            <option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[1][3]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
                                                <?= $lists->title ?>
                                            </option>
                                        <?php } ?>
                                        </select>
                                        <span class="input-group-addon">数量</span>
                                        <input type="text" name="duiInfo[1][3][num]" value="<?php if (!empty($duiInfo) && $duiInfo[1][3]['num'] >0){ echo $duiInfo[1][3]['num']; }?>"  class="form-control">
                                        <?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon"><?php echo $duiType[2];?>/块兑换需</span>
										<select name="duiInfo[2][1][pid]" class="form-control">
										<?php foreach ($product as $keys => $lists) { ?>
											<option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[2][1]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
												<?= $lists->title ?>
											</option>
										<?php } ?>
										</select>
										<span class="input-group-addon">数量</span>
										<input type="text" name="duiInfo[2][1][num]" value="<?php if (!empty($duiInfo) && $duiInfo[2][1]['num'] >0){ echo $duiInfo[2][1]['num']; }?>"  class="form-control">
										<span class="input-group-addon">+</span>
										<select name="duiInfo[2][2][pid]" class="form-control">
										<?php foreach ($product as $keys => $lists) { ?>
											<option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[2][2]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
												<?= $lists->title ?>
											</option>
										<?php } ?>
										</select>
										<span class="input-group-addon">数量</span>
										<input type="text" name="duiInfo[2][2][num]" value="<?php if (!empty($duiInfo) && $duiInfo[2][2]['num'] >0){ echo $duiInfo[2][2]['num']; }?>"  class="form-control">
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+</span>
										<select name="duiInfo[2][3][pid]" class="form-control">
										<?php foreach ($product as $keys => $lists) { ?>
											<option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[2][3]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
												<?= $lists->title ?>
											</option>
										<?php } ?>
										</select>
										<span class="input-group-addon">数量</span>
										<input type="text" name="duiInfo[2][3][num]" value="<?php if (!empty($duiInfo) && $duiInfo[2][3]['num'] >0){ echo $duiInfo[2][3]['num']; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'jinlilai') { ?>
                                        <span class="input-group-addon">+</span>
                                        <select name="duiInfo[2][3][pid]" class="form-control">
                                        <?php foreach ($product as $keys => $lists) { ?>
                                            <option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[2][3]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
                                                <?= $lists->title ?>
                                            </option>
                                        <?php } ?>
                                        </select>
                                        <span class="input-group-addon">数量</span>
                                        <input type="text" name="duiInfo[2][3][num]" value="<?php if (!empty($duiInfo) && $duiInfo[2][3]['num'] >0){ echo $duiInfo[2][3]['num']; }?>"  class="form-control">
                                        <?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon"><?php echo $duiType[3];?>/块兑换需</span>
										<select name="duiInfo[3][1][pid]" class="form-control">
										<?php foreach ($product as $keys => $lists) { ?>
											<option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[3][1]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
												<?= $lists->title ?>
											</option>
										<?php } ?>
										</select>
										<span class="input-group-addon">数量</span>
										<input type="text" name="duiInfo[3][1][num]" value="<?php if (!empty($duiInfo) && $duiInfo[3][1]['num'] >0){ echo $duiInfo[3][1]['num']; }?>"  class="form-control">
										<span class="input-group-addon">+</span>
										<select name="duiInfo[3][2][pid]" class="form-control">
										<?php foreach ($product as $keys => $lists) { ?>
											<option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[3][2]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
												<?= $lists->title ?>
											</option>
										<?php } ?>
										</select>
										<span class="input-group-addon">数量</span>
										<input type="text" name="duiInfo[3][2][num]" value="<?php if (!empty($duiInfo) && $duiInfo[3][2]['num'] >0){ echo $duiInfo[3][2]['num']; }?>"  class="form-control">
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+</span>
										<select name="duiInfo[3][3][pid]" class="form-control">
										<?php foreach ($product as $keys => $lists) { ?>
											<option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[3][3]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
												<?= $lists->title ?>
											</option>
										<?php } ?>
										</select>
										<span class="input-group-addon">数量</span>
										<input type="text" name="duiInfo[3][3][num]" value="<?php if (!empty($duiInfo) && $duiInfo[3][3]['num'] >0){ echo $duiInfo[3][3]['num']; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'jinlilai') { ?>
                                        <span class="input-group-addon">+</span>
                                        <select name="duiInfo[3][3][pid]" class="form-control">
                                        <?php foreach ($product as $keys => $lists) { ?>
                                            <option value="<?= $lists->id ?>" <?php if (isset($duiInfo)) { ?><?php if ($duiInfo[3][3]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
                                                <?= $lists->title ?>
                                            </option>
                                        <?php } ?>
                                        </select>
                                        <span class="input-group-addon">数量</span>
                                        <input type="text" name="duiInfo[3][3][num]" value="<?php if (!empty($duiInfo) && $duiInfo[3][3]['num'] >0){ echo $duiInfo[3][3]['num']; }?>"  class="form-control">
                                        <?php } ?>
									</div>
								</div>
							</div>
							<div class="panel-footer text-left">
								<input class="form-control"  type="hidden" name="id" value="<?php if ($item != '') { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<?php } ?>
		<?php if ($op == 'house') { ?>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'house') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=house">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">房屋升级信息</label>
								<div class="col-xs-12 col-sm-10 col-md-10 col-lg-12">
									<div  class="input-group">
										<span class="input-group-addon">1-2 升级需要数量 <?php echo $duiType[0];?></span>
										<input type="text" name="houseInfo[1][0]" value="<?php if (!empty($houseInfo) && $houseInfo[1][0] >0){ echo $houseInfo[1][0]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[1];?></span>
										<input type="text" name="houseInfo[1][1]" value="<?php if (!empty($houseInfo) && $houseInfo[1][1] >0){ echo $houseInfo[1][1]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[2];?></span>
										<input type="text" name="houseInfo[1][2]" value="<?php if (!empty($houseInfo) && $houseInfo[1][2] >0){ echo $houseInfo[1][2]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[3];?></span>
										<input type="text" name="houseInfo[1][3]" value="<?php if (!empty($houseInfo) && $houseInfo[1][3] >0){ echo $houseInfo[1][3]; }?>"  class="form-control">
										<?php if ($hostType == 'yansheng') { ?>
										<span class="input-group-addon">+苦瓜汁</span>
										<input type="text" name="houseInfo[1][17]" value="<?php if (!empty($houseInfo) && $houseInfo[1][17] >0){ echo $houseInfo[1][17]; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+直推</span>
										<input type="text" name="houseInfo[1][nums]" value="<?php if (!empty($houseInfo) && $houseInfo[1]['nums'] >0){ echo $houseInfo[1]['nums']; }?>"  class="form-control">
										<span class="input-group-addon">人均充值</span>
										<input type="text" name="houseInfo[1][price]" value="<?php if (!empty($houseInfo) && $houseInfo[1]['price'] >0){ echo $houseInfo[1]['price']; }?>"  class="form-control">
										<span class="input-group-addon">元以上</span>
										<?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">2-3 升级需要数量 <?php echo $duiType[0];?></span>
										<input type="text" name="houseInfo[2][0]" value="<?php if (!empty($houseInfo) && $houseInfo[2][0] >0){ echo $houseInfo[2][0]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[1];?></span>
										<input type="text" name="houseInfo[2][1]" value="<?php if (!empty($houseInfo) && $houseInfo[2][1] >0){ echo $houseInfo[2][1]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[2];?></span>
										<input type="text" name="houseInfo[2][2]" value="<?php if (!empty($houseInfo) && $houseInfo[2][2] >0){ echo $houseInfo[2][2]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[3];?></span>
										<input type="text" name="houseInfo[2][3]" value="<?php if (!empty($houseInfo) && $houseInfo[2][3] >0){ echo $houseInfo[2][3]; }?>"  class="form-control">
										<?php if ($hostType == 'yansheng') { ?>
										<span class="input-group-addon">+苦瓜汁</span>
										<input type="text" name="houseInfo[2][17]" value="<?php if (!empty($houseInfo) && $houseInfo[2][17] >0){ echo $houseInfo[2][17]; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+直推</span>
										<input type="text" name="houseInfo[2][nums]" value="<?php if (!empty($houseInfo) && $houseInfo[2]['nums'] >0){ echo $houseInfo[2]['nums']; }?>"  class="form-control">
										<span class="input-group-addon">人均充值</span>
										<input type="text" name="houseInfo[2][price]" value="<?php if (!empty($houseInfo) && $houseInfo[2]['price'] >0){ echo $houseInfo[2]['price']; }?>"  class="form-control">
										<span class="input-group-addon">元以上</span>
										<?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">3-4 升级需要数量 <?php echo $duiType[0];?></span>
										<input type="text" name="houseInfo[3][0]" value="<?php if (!empty($houseInfo) && $houseInfo[3][0] >0){ echo $houseInfo[3][0]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[1];?></span>
										<input type="text" name="houseInfo[3][1]" value="<?php if (!empty($houseInfo) && $houseInfo[3][1] >0){ echo $houseInfo[3][1]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[2];?></span>
										<input type="text" name="houseInfo[3][2]" value="<?php if (!empty($houseInfo) && $houseInfo[3][2] >0){ echo $houseInfo[3][2]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[3];?></span>
										<input type="text" name="houseInfo[3][3]" value="<?php if (!empty($houseInfo) && $houseInfo[3][3] >0){ echo $houseInfo[3][3]; }?>"  class="form-control">
										<?php if ($hostType == 'yansheng') { ?>
										<span class="input-group-addon">+苦瓜汁</span>
										<input type="text" name="houseInfo[3][17]" value="<?php if (!empty($houseInfo) && $houseInfo[3][17] >0){ echo $houseInfo[3][17]; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+直推</span>
										<input type="text" name="houseInfo[3][nums]" value="<?php if (!empty($houseInfo) && $houseInfo[3]['nums'] >0){ echo $houseInfo[3]['nums']; }?>"  class="form-control">
										<span class="input-group-addon">人均充值</span>
										<input type="text" name="houseInfo[3][price]" value="<?php if (!empty($houseInfo) && $houseInfo[3]['price'] >0){ echo $houseInfo[3]['price']; }?>"  class="form-control">
										<span class="input-group-addon">元以上</span>
										<?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">4-5 升级需要数量 <?php echo $duiType[0];?></span>
										<input type="text" name="houseInfo[4][0]" value="<?php if (!empty($houseInfo) && $houseInfo[4][0] >0){ echo $houseInfo[4][0]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[1];?></span>
										<input type="text" name="houseInfo[4][1]" value="<?php if (!empty($houseInfo) && $houseInfo[4][1] >0){ echo $houseInfo[4][1]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[2];?></span>
										<input type="text" name="houseInfo[4][2]" value="<?php if (!empty($houseInfo) && $houseInfo[4][2] >0){ echo $houseInfo[4][2]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[3];?></span>
										<input type="text" name="houseInfo[4][3]" value="<?php if (!empty($houseInfo) && $houseInfo[4][3] >0){ echo $houseInfo[4][3]; }?>"  class="form-control">
										<?php if ($hostType == 'yansheng') { ?>
										<span class="input-group-addon">+苦瓜汁</span>
										<input type="text" name="houseInfo[4][17]" value="<?php if (!empty($houseInfo) && $houseInfo[4][17] >0){ echo $houseInfo[4][17]; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+直推</span>
										<input type="text" name="houseInfo[4][nums]" value="<?php if (!empty($houseInfo) && $houseInfo[4]['nums'] >0){ echo $houseInfo[4]['nums']; }?>"  class="form-control">
										<span class="input-group-addon">人均充值</span>
										<input type="text" name="houseInfo[4][price]" value="<?php if (!empty($houseInfo) && $houseInfo[4]['price'] >0){ echo $houseInfo[4]['price']; }?>"  class="form-control">
										<span class="input-group-addon">元以上</span>
										<?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">5-6 升级需要数量 <?php echo $duiType[0];?></span>
										<input type="text" name="houseInfo[5][0]" value="<?php if (!empty($houseInfo) && $houseInfo[5][0] >0){ echo $houseInfo[5][0]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[1];?></span>
										<input type="text" name="houseInfo[5][1]" value="<?php if (!empty($houseInfo) && $houseInfo[5][1] >0){ echo $houseInfo[5][1]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[2];?></span>
										<input type="text" name="houseInfo[5][2]" value="<?php if (!empty($houseInfo) && $houseInfo[5][2] >0){ echo $houseInfo[5][2]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[3];?></span>
										<input type="text" name="houseInfo[5][3]" value="<?php if (!empty($houseInfo) && $houseInfo[5][3] >0){ echo $houseInfo[5][3]; }?>"  class="form-control">
										<?php if ($hostType == 'yansheng') { ?>
										<span class="input-group-addon">+苦瓜汁</span>
										<input type="text" name="houseInfo[5][17]" value="<?php if (!empty($houseInfo) && $houseInfo[5][17] >0){ echo $houseInfo[5][17]; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+直推</span>
										<input type="text" name="houseInfo[5][nums]" value="<?php if (!empty($houseInfo) && $houseInfo[5]['nums'] >0){ echo $houseInfo[5]['nums']; }?>"  class="form-control">
										<span class="input-group-addon">人均充值</span>
										<input type="text" name="houseInfo[5][price]" value="<?php if (!empty($houseInfo) && $houseInfo[5]['price'] >0){ echo $houseInfo[5]['price']; }?>"  class="form-control">
										<span class="input-group-addon">元以上</span>
										<?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">6-7 升级需要数量 <?php echo $duiType[0];?></span>
										<input type="text" name="houseInfo[6][0]" value="<?php if (!empty($houseInfo) && $houseInfo[6][0] >0){ echo $houseInfo[6][0]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[1];?></span>
										<input type="text" name="houseInfo[6][1]" value="<?php if (!empty($houseInfo) && $houseInfo[6][1] >0){ echo $houseInfo[6][1]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[2];?></span>
										<input type="text" name="houseInfo[6][2]" value="<?php if (!empty($houseInfo) && $houseInfo[6][2] >0){ echo $houseInfo[6][2]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[3];?></span>
										<input type="text" name="houseInfo[6][3]" value="<?php if (!empty($houseInfo) && $houseInfo[6][3] >0){ echo $houseInfo[6][3]; }?>"  class="form-control">
										<?php if ($hostType == 'yansheng') { ?>
										<span class="input-group-addon">+苦瓜汁</span>
										<input type="text" name="houseInfo[6][17]" value="<?php if (!empty($houseInfo) && $houseInfo[6][17] >0){ echo $houseInfo[6][17]; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+直推</span>
										<input type="text" name="houseInfo[6][nums]" value="<?php if (!empty($houseInfo) && $houseInfo[6]['nums'] >0){ echo $houseInfo[6]['nums']; }?>"  class="form-control">
										<span class="input-group-addon">人均充值</span>
										<input type="text" name="houseInfo[6][price]" value="<?php if (!empty($houseInfo) && $houseInfo[6]['price'] >0){ echo $houseInfo[6]['price']; }?>"  class="form-control">
										<span class="input-group-addon">元以上</span>
										<?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">7-8 升级需要数量 <?php echo $duiType[0];?></span>
										<input type="text" name="houseInfo[7][0]" value="<?php if (!empty($houseInfo) && $houseInfo[7][0] >0){ echo $houseInfo[7][0]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[1];?></span>
										<input type="text" name="houseInfo[7][1]" value="<?php if (!empty($houseInfo) && $houseInfo[7][1] >0){ echo $houseInfo[7][1]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[2];?></span>
										<input type="text" name="houseInfo[7][2]" value="<?php if (!empty($houseInfo) && $houseInfo[7][2] >0){ echo $houseInfo[7][2]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[3];?></span>
										<input type="text" name="houseInfo[7][3]" value="<?php if (!empty($houseInfo) && $houseInfo[7][3] >0){ echo $houseInfo[7][3]; }?>"  class="form-control">
										<?php if ($hostType == 'yansheng') { ?>
										<span class="input-group-addon">+苦瓜汁</span>
										<input type="text" name="houseInfo[7][17]" value="<?php if (!empty($houseInfo) && $houseInfo[7][17] >0){ echo $houseInfo[7][17]; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+直推</span>
										<input type="text" name="houseInfo[7][nums]" value="<?php if (!empty($houseInfo) && $houseInfo[7]['nums'] >0){ echo $houseInfo[7]['nums']; }?>"  class="form-control">
										<span class="input-group-addon">人均充值</span>
										<input type="text" name="houseInfo[7][price]" value="<?php if (!empty($houseInfo) && $houseInfo[7]['price'] >0){ echo $houseInfo[7]['price']; }?>"  class="form-control">
										<span class="input-group-addon">元以上</span>
										<?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">8-9 升级需要数量 <?php echo $duiType[0];?></span>
										<input type="text" name="houseInfo[8][0]" value="<?php if (!empty($houseInfo) && $houseInfo[8][0] >0){ echo $houseInfo[8][0]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[1];?></span>
										<input type="text" name="houseInfo[8][1]" value="<?php if (!empty($houseInfo) && $houseInfo[8][1] >0){ echo $houseInfo[8][1]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[2];?></span>
										<input type="text" name="houseInfo[8][2]" value="<?php if (!empty($houseInfo) && $houseInfo[8][2] >0){ echo $houseInfo[8][2]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[3];?></span>
										<input type="text" name="houseInfo[8][3]" value="<?php if (!empty($houseInfo) && $houseInfo[8][3] >0){ echo $houseInfo[8][3]; }?>"  class="form-control">
										<?php if ($hostType == 'yansheng') { ?>
										<span class="input-group-addon">+苦瓜汁</span>
										<input type="text" name="houseInfo[8][17]" value="<?php if (!empty($houseInfo) && $houseInfo[8][17] >0){ echo $houseInfo[8][17]; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+直推</span>
										<input type="text" name="houseInfo[8][nums]" value="<?php if (!empty($houseInfo) && $houseInfo[8]['nums'] >0){ echo $houseInfo[8]['nums']; }?>"  class="form-control">
										<span class="input-group-addon">人均充值</span>
										<input type="text" name="houseInfo[8][price]" value="<?php if (!empty($houseInfo) && $houseInfo[8]['price'] >0){ echo $houseInfo[8]['price']; }?>"  class="form-control">
										<span class="input-group-addon">元以上</span>
										<?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">9-10 升级需要数量 <?php echo $duiType[0];?></span>
										<input type="text" name="houseInfo[9][0]" value="<?php if (!empty($houseInfo) && $houseInfo[9][0] >0){ echo $houseInfo[9][0]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[1];?></span>
										<input type="text" name="houseInfo[9][1]" value="<?php if (!empty($houseInfo) && $houseInfo[9][1] >0){ echo $houseInfo[9][1]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[2];?></span>
										<input type="text" name="houseInfo[9][2]" value="<?php if (!empty($houseInfo) && $houseInfo[9][2] >0){ echo $houseInfo[9][2]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[3];?></span>
										<input type="text" name="houseInfo[9][3]" value="<?php if (!empty($houseInfo) && $houseInfo[9][3] >0){ echo $houseInfo[9][3]; }?>"  class="form-control">
										<?php if ($hostType == 'yansheng') { ?>
										<span class="input-group-addon">+苦瓜汁</span>
										<input type="text" name="houseInfo[9][17]" value="<?php if (!empty($houseInfo) && $houseInfo[9][17] >0){ echo $houseInfo[9][17]; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+直推</span>
										<input type="text" name="houseInfo[9][nums]" value="<?php if (!empty($houseInfo) && $houseInfo[9]['nums'] >0){ echo $houseInfo[9]['nums']; }?>"  class="form-control">
										<span class="input-group-addon">人均充值</span>
										<input type="text" name="houseInfo[9][price]" value="<?php if (!empty($houseInfo) && $houseInfo[9]['price'] >0){ echo $houseInfo[9]['price']; }?>"  class="form-control">
										<span class="input-group-addon">元以上</span>
										<?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">10-11 升级需要数量 <?php echo $duiType[0];?></span>
										<input type="text" name="houseInfo[10][0]" value="<?php if (!empty($houseInfo) && $houseInfo[10][0] >0){ echo $houseInfo[10][0]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[1];?></span>
										<input type="text" name="houseInfo[10][1]" value="<?php if (!empty($houseInfo) && $houseInfo[10][1] >0){ echo $houseInfo[10][1]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[2];?></span>
										<input type="text" name="houseInfo[10][2]" value="<?php if (!empty($houseInfo) && $houseInfo[10][2] >0){ echo $houseInfo[10][2]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[3];?></span>
										<input type="text" name="houseInfo[10][3]" value="<?php if (!empty($houseInfo) && $houseInfo[10][3] >0){ echo $houseInfo[10][3]; }?>"  class="form-control">
										<?php if ($hostType == 'yansheng') { ?>
										<span class="input-group-addon">+苦瓜汁</span>
										<input type="text" name="houseInfo[10][17]" value="<?php if (!empty($houseInfo) && $houseInfo[10][17] >0){ echo $houseInfo[10][17]; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+直推</span>
										<input type="text" name="houseInfo[10][nums]" value="<?php if (!empty($houseInfo) && $houseInfo[10]['nums'] >0){ echo $houseInfo[10]['nums']; }?>"  class="form-control">
										<span class="input-group-addon">人均充值</span>
										<input type="text" name="houseInfo[10][price]" value="<?php if (!empty($houseInfo) && $houseInfo[10]['price'] >0){ echo $houseInfo[10]['price']; }?>"  class="form-control">
										<span class="input-group-addon">元以上</span>
										<?php } ?>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">11-12 升级需要数量 <?php echo $duiType[0];?></span>
										<input type="text" name="houseInfo[11][0]" value="<?php if (!empty($houseInfo) && $houseInfo[11][0] >0){ echo $houseInfo[11][0]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[1];?></span>
										<input type="text" name="houseInfo[11][1]" value="<?php if (!empty($houseInfo) && $houseInfo[11][1] >0){ echo $houseInfo[11][1]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[2];?></span>
										<input type="text" name="houseInfo[11][2]" value="<?php if (!empty($houseInfo) && $houseInfo[11][2] >0){ echo $houseInfo[11][2]; }?>"  class="form-control">
										<span class="input-group-addon">+<?php echo $duiType[3];?></span>
										<input type="text" name="houseInfo[11][3]" value="<?php if (!empty($houseInfo) && $houseInfo[11][3] >0){ echo $houseInfo[11][3]; }?>"  class="form-control">
										<?php if ($hostType == 'yansheng') { ?>
										<span class="input-group-addon">+苦瓜汁</span>
										<input type="text" name="houseInfo[11][17]" value="<?php if (!empty($houseInfo) && $houseInfo[11][17] >0){ echo $houseInfo[11][17]; }?>"  class="form-control">
										<?php } ?>
										<?php if ($hostType == 'kk') { ?>
										<span class="input-group-addon">+直推</span>
										<input type="text" name="houseInfo[11][nums]" value="<?php if (!empty($houseInfo) && $houseInfo[11]['nums'] >0){ echo $houseInfo[11]['nums']; }?>"  class="form-control">
										<span class="input-group-addon">人均充值</span>
										<input type="text" name="houseInfo[11][price]" value="<?php if (!empty($houseInfo) && $houseInfo[11]['price'] >0){ echo $houseInfo[11]['price']; }?>"  class="form-control">
										<span class="input-group-addon">元以上</span>
										<?php } ?>
									</div>
								</div>
							</div>
							<div class="panel-footer text-left">
								<input class="form-control"  type="hidden" name="id" value="<?php if ($item != '') { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<?php } ?>
		<?php if ($op == 'dogInfo') { ?>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'dogInfo') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=dogInfo">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">宠物升级</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<div  class="input-group">
										<span class="input-group-addon">1-2 升级</span>
										<input type="text" name="dogInfo[experience][1]" value="<?php if (!empty($dogInfo) && $dogInfo["experience"][1] >0){ echo $dogInfo["experience"][1]; }?>"  class="form-control">
										<span class="input-group-addon">经验</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">2-3 升级</span>
										<input type="text" name="dogInfo[experience][2]" value="<?php if (!empty($dogInfo) && $dogInfo["experience"][2] >0){ echo $dogInfo["experience"][2]; }?>"  class="form-control">
										<span class="input-group-addon">经验</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">3-4 升级</span>
										<input type="text" name="dogInfo[experience][3]" value="<?php if (!empty($dogInfo) && $dogInfo["experience"][3] >0){ echo $dogInfo["experience"][3]; }?>"  class="form-control">
										<span class="input-group-addon">经验</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">4-5 升级</span>
										<input type="text" name="dogInfo[experience][4]" value="<?php if (!empty($dogInfo) && $dogInfo["experience"][4] >0){ echo $dogInfo["experience"][4]; }?>"  class="form-control">
										<span class="input-group-addon">经验</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">5-6 升级</span>
										<input type="text" name="dogInfo[experience][5]" value="<?php if (!empty($dogInfo) && $dogInfo["experience"][5] >0){ echo $dogInfo["experience"][5]; }?>"  class="form-control">
										<span class="input-group-addon">经验</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">6-7 升级</span>
										<input type="text" name="dogInfo[experience][6]" value="<?php if (!empty($dogInfo) && $dogInfo["experience"][6] >0){ echo $dogInfo["experience"][6]; }?>"  class="form-control">
										<span class="input-group-addon">经验</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">7-8 升级</span>
										<input type="text" name="dogInfo[experience][7]" value="<?php if (!empty($dogInfo) && $dogInfo["experience"][7] >0){ echo $dogInfo["experience"][7]; }?>"  class="form-control">
										<span class="input-group-addon">经验</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">8-9 升级</span>
										<input type="text" name="dogInfo[experience][8]" value="<?php if (!empty($dogInfo) && $dogInfo["experience"][8] >0){ echo $dogInfo["experience"][8]; }?>"  class="form-control">
										<span class="input-group-addon">经验</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">9-10 升级</span>
										<input type="text" name="dogInfo[experience][9]" value="<?php if (!empty($dogInfo) && $dogInfo["experience"][9] >0){ echo $dogInfo["experience"][9]; }?>"  class="form-control">
										<span class="input-group-addon">经验</span>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">宠物信息</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<div  class="input-group">
										<span class="input-group-addon">拥有上限</span>
										<input type="text" name="dogInfo[uplimit]" value="<?php if (!empty($dogInfo) && $dogInfo['uplimit'] >0){ echo $dogInfo['uplimit']; }?>"  class="form-control">
										<span class="input-group-addon">只宠物</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">经验值1 = </span>
										<input type="text" name="dogInfo[num]" value="<?php if (!empty($dogInfo) && $dogInfo['num'] >0){ echo $dogInfo['num']; }?>"  class="form-control">
										<span class="input-group-addon">袋狗粮</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">普通狗粮</span>
										<select name="dogInfo[info][1][pid]" class="form-control">
										<?php foreach ($product as $keys => $lists) { ?>
											<option value="<?= $lists->id ?>" <?php if (isset($dogInfo)) { ?><?php if ($dogInfo["info"][1]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
												<?= $lists->title ?>
											</option>
										<?php } ?>
										</select>
										<span class="input-group-addon">数量</span>
										<input type="text" name="dogInfo[info][1][num]" value="<?php if (!empty($dogInfo) && $dogInfo["info"][1]['num'] >0){ echo $dogInfo["info"][1]['num']; }?>"  class="form-control">

										<span class="input-group-addon">个名称</span>
										<input type="text" name="dogInfo[info][1][tName]" value="<?php if (!empty($dogInfo) && !empty($dogInfo["info"][1]['tName'])){ echo $dogInfo["info"][1]['tName']; }?>"  class="form-control">
										<span class="input-group-addon">简介</span>
										<input type="text" name="dogInfo[info][1][depict]" value="<?php if (!empty($dogInfo) &&  !empty($dogInfo["info"][1]['depict'])){ echo $dogInfo["info"][1]['depict']; }?>"  class="form-control">
									</div>
									<div  class="input-group">
										<span class="input-group-addon">优质狗粮</span>
										<select name="dogInfo[info][2][pid]" class="form-control">
										<?php foreach ($product as $keys => $lists) { ?>
											<option value="<?= $lists->id ?>" <?php if (isset($dogInfo)) { ?><?php if ($dogInfo["info"][2]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
												<?= $lists->title ?>
											</option>
										<?php } ?>
										</select>
										<span class="input-group-addon">数量</span>
										<input type="text" name="dogInfo[info][2][num]" value="<?php if (!empty($dogInfo) && $dogInfo["info"][2]['num'] >0){ echo $dogInfo["info"][2]['num']; }?>"  class="form-control">
										<span class="input-group-addon">个名称</span>
										<input type="text" name="dogInfo[info][2][tName]" value="<?php if (!empty($dogInfo) && !empty($dogInfo["info"][2]['tName'])){ echo $dogInfo["info"][2]['tName']; }?>"  class="form-control">
										<span class="input-group-addon">简介</span>
										<input type="text" name="dogInfo[info][2][depict]" value="<?php if (!empty($dogInfo) && !empty($dogInfo["info"][2]['depict'])){ echo $dogInfo["info"][2]['depict']; }?>"  class="form-control">
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">宠物属性</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<div  class="input-group">
										<span class="input-group-addon">体力初始</span>
										<input type="text" name="dogInfo[power][1]" value="<?php if (!empty($dogInfo) && $dogInfo["power"][1] >0){ echo $dogInfo["power"][1]; }?>"  class="form-control">
										<span class="input-group-addon">点</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">喂食概率增加</span>
										<input type="text" name="dogInfo[power][0]" value="<?php if (!empty($dogInfo) && $dogInfo["power"][0] >0){ echo $dogInfo["power"][0]; }?>"  class="form-control">
										<span class="input-group-addon">点</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">体力升级增加</span>
										<input type="text" name="dogInfo[power][2]" value="<?php if (!empty($dogInfo) && $dogInfo["power"][2] >0){ echo $dogInfo["power"][2]; }?>"  class="form-control">
										<span class="input-group-addon">点</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">体力训练扣除</span>
										<input type="text" name="dogInfo[power][3]" value="<?php if (!empty($dogInfo) && $dogInfo["power"][3] >0){ echo $dogInfo["power"][3]; }?>"  class="form-control">
										<span class="input-group-addon">点</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">幸运值初始</span>
										<input type="text" name="dogInfo[lucky][1]" value="<?php if (!empty($dogInfo) && $dogInfo["lucky"][1] >0){ echo $dogInfo["lucky"][1]; }?>"  class="form-control">
										<span class="input-group-addon">点</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">喂食或升级概率增加</span>
										<input type="text" name="dogInfo[lucky][2]" value="<?php if (!empty($dogInfo) && $dogInfo["lucky"][2] >0){ echo $dogInfo["lucky"][2]; }?>"  class="form-control">
										<span class="input-group-addon">点</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">攻击值初始最小</span>
										<input type="text" name="dogInfo[attack][1][min]" value="<?php if (!empty($dogInfo) && $dogInfo["attack"][1]['min'] >0){ echo $dogInfo["attack"][1]['min']; }?>"  class="form-control">
										<span class="input-group-addon">点,最大</span>
										<input type="text" name="dogInfo[attack][1][max]" value="<?php if (!empty($dogInfo) && $dogInfo["attack"][1]['max'] >0){ echo $dogInfo["attack"][1]['max']; }?>"  class="form-control">
										<span class="input-group-addon">点</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">攻击升级最小增加</span>
										<input type="text" name="dogInfo[attack][2][min]" value="<?php if (!empty($dogInfo) && $dogInfo["attack"][2]['min'] >0){ echo $dogInfo["attack"][2]['min']; }?>"  class="form-control">
										<span class="input-group-addon">点,最大增加</span>
										<input type="text" name="dogInfo[attack][2][max]" value="<?php if (!empty($dogInfo) && $dogInfo["attack"][2]['max'] >0){ echo $dogInfo["attack"][2]['max']; }?>"  class="form-control">
										<span class="input-group-addon">点 升级成功，训练概率成功</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">防御值初始最小</span>
										<input type="text" name="dogInfo[defense][1][min]" value="<?php if (!empty($dogInfo) && $dogInfo["defense"][1]['min'] >0){ echo $dogInfo["defense"][1]['min']; }?>"  class="form-control">
										<span class="input-group-addon">点,最大</span>
										<input type="text" name="dogInfo[defense][1][max]" value="<?php if (!empty($dogInfo) && $dogInfo["defense"][1]['max'] >0){ echo $dogInfo["defense"][1]['max']; }?>"  class="form-control">
										<span class="input-group-addon">点</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">防御升级最小增加</span>
										<input type="text" name="dogInfo[defense][2][min]" value="<?php if (!empty($dogInfo) && $dogInfo["defense"][2]['min'] >0){ echo $dogInfo["defense"][2]['min']; }?>"  class="form-control">
										<span class="input-group-addon">点,最大增加</span>
										<input type="text" name="dogInfo[defense][2][max]" value="<?php if (!empty($dogInfo) && $dogInfo["defense"][2]['max'] >0){ echo $dogInfo["defense"][2]['max']; }?>"  class="form-control">
										<span class="input-group-addon">点 升级成功，训练概率成功</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">速度值初始最小</span>
										<input type="text" name="dogInfo[speed][1][min]" value="<?php if (!empty($dogInfo) && $dogInfo["speed"][1]['min'] >0){ echo $dogInfo["speed"][1]['min']; }?>"  class="form-control">
										<span class="input-group-addon">点,最大</span>
										<input type="text" name="dogInfo[speed][1][max]" value="<?php if (!empty($dogInfo) && $dogInfo["speed"][1]['max'] >0){ echo $dogInfo["speed"][1]['max']; }?>"  class="form-control">
										<span class="input-group-addon">点</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">速度升级最小增加</span>
										<input type="text" name="dogInfo[speed][2][min]" value="<?php if (!empty($dogInfo) && $dogInfo["speed"][2]['min'] >0){ echo $dogInfo["speed"][2]['min']; }?>"  class="form-control">
										<span class="input-group-addon">点,最大增加</span>
										<input type="text" name="dogInfo[speed][2][max]" value="<?php if (!empty($dogInfo) && $dogInfo["speed"][2]['max'] >0){ echo $dogInfo["speed"][2]['max']; }?>"  class="form-control">
										<span class="input-group-addon">点 升级成功，训练概率成功</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">生命升级最小增加</span>
										<input type="text" name="dogInfo[powerUlimit][2][min]" value="<?php if (!empty($dogInfo) && $dogInfo["powerUlimit"][2]['min'] >0){ echo $dogInfo["powerUlimit"][2]['min']; }?>"  class="form-control">
										<span class="input-group-addon">点,最大增加</span>
										<input type="text" name="dogInfo[powerUlimit][2][max]" value="<?php if (!empty($dogInfo) && $dogInfo["powerUlimit"][2]['max'] >0){ echo $dogInfo["powerUlimit"][2]['max']; }?>"  class="form-control">
										<span class="input-group-addon">点 升级成功，训练概率成功</span>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">宠物技能</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<div  class="input-group">
										<span class="input-group-addon">自动收获时效</span>
										<input type="text" name="dogInfo[skill][1]" value="<?php if (!empty($dogInfo) && $dogInfo["skill"][1] >0){ echo $dogInfo["skill"][1]; }?>"  class="form-control">
										<span class="input-group-addon">小时,自动播种时效</span>
										<input type="text" name="dogInfo[skill][2]" value="<?php if (!empty($dogInfo) && $dogInfo["skill"][2] >0){ echo $dogInfo["skill"][2]; }?>"  class="form-control">
										<span class="input-group-addon">小时,升级增加</span>
										<input type="text" name="dogInfo[skill][3]" value="<?php if (!empty($dogInfo) && $dogInfo["skill"][3] >0){ echo $dogInfo["skill"][3]; }?>"  class="form-control">
										<span class="input-group-addon">小时</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">玫瑰值上限</span>
										<input type="text" name="dogInfo[skill][0]" value="<?php if (!empty($dogInfo) && $dogInfo["skill"][0] >0){ echo $dogInfo["skill"][0]; }?>"  class="form-control">
										<span class="input-group-addon">随机增加最小</span>
										<input type="text" name="dogInfo[skill][4]" value="<?php if (!empty($dogInfo) && $dogInfo["skill"][4] >0){ echo $dogInfo["skill"][4]; }?>"  class="form-control">
										<span class="input-group-addon">随机增加最大</span>
										<input type="text" name="dogInfo[skill][5]" value="<?php if (!empty($dogInfo) && $dogInfo["skill"][5] >0){ echo $dogInfo["skill"][5]; }?>"  class="form-control">
									</div>
								</div>
							</div>
							<div class="panel-footer text-left">
								<input class="form-control"  type="hidden" name="id" value="<?php if ($item != '') { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<?php } ?>
		<?php if ($op == 'landInfo') { ?>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'landInfo') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=landInfo">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">土地升级</label>
								<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
									<div  class="input-group">
										<?php foreach ($landType as $key => $row) { ?>
										<?php if ($key > 1) { ?>
										<div  class="input-group">
											<span class="input-group-addon">升级 为<?= $row ?></span>

											<span class="input-group-addon">钻石需</span>
											<input type="text" name="landUpInfo[<?= $key ?>][0][num]" value="<?php if (!empty($landUpInfo) && !empty($landUpInfo[$key][0]['num'])){ echo $landUpInfo[$key][0]['num']; } ?>"  class="form-control">
											<span class="input-group-addon">颗</span>
										</div>
										<div  class="input-group">
											<select name="landUpInfo[<?= $key ?>][1][pid]" class="form-control">>
												<?php foreach ($product as $keys => $lists) { ?>
												<option value="<?= $lists->id ?>" <?php if (isset($landUpInfo)) { ?><?php if ($landUpInfo[$key][1]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
													<?= $lists->title ?>
												</option>
												<?php } ?>
											</select>
											<span class="input-group-addon">需</span>
											<input type="text" name="landUpInfo[<?= $key ?>][1][num]" value="<?php if (!empty($landUpInfo) && !empty($landUpInfo[$key][1]['num'])){ echo $landUpInfo[$key][1]['num']; } ?>"  class="form-control">
											<span class="input-group-addon">颗</span>
											<select name="landUpInfo[<?= $key ?>][2][pid]" class="form-control">>
												<?php foreach ($product as $keys => $lists) { ?>
												<option value="<?= $lists->id ?>" <?php if (isset($landUpInfo)) { ?><?php if ($landUpInfo[$key][2]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
													<?= $lists->title ?>
												</option>
												<?php } ?>
											</select>
											<span class="input-group-addon">需</span>
											<input type="text" name="landUpInfo[<?= $key ?>][2][num]" value="<?php if (!empty($landUpInfo) && !empty($landUpInfo[$key][2]['num'])){ echo $landUpInfo[$key][2]['num']; } ?>"  class="form-control">
											<span class="input-group-addon">颗</span>
											<?php if ($hostType == 'kk') { ?>
											<select name="landUpInfo[<?= $key ?>][3][pid]" class="form-control">>
												<?php foreach ($product as $keys => $lists) { ?>
												<option value="<?= $lists->id ?>" <?php if (isset($landUpInfo)) { ?><?php if ($landUpInfo[$key][3]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
													<?= $lists->title ?>
												</option>
												<?php } ?>
											</select>
											<span class="input-group-addon">需</span>
											<input type="text" name="landUpInfo[<?= $key ?>][3][num]" value="<?php if (!empty($landUpInfo) && !empty($landUpInfo[$key][3]['num'])){ echo $landUpInfo[$key][3]['num']; } ?>"  class="form-control">
											<span class="input-group-addon">颗</span>
											<?php } ?>
											<?php if ($hostType == 'jinlilai') { ?>
                                            <select name="landUpInfo[<?= $key ?>][3][pid]" class="form-control">>
                                                <?php foreach ($product as $keys => $lists) { ?>
                                                <option value="<?= $lists->id ?>" <?php if (isset($landUpInfo)) { ?><?php if ($landUpInfo[$key][3]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
                                                    <?= $lists->title ?>
                                                </option>
                                                <?php } ?>
                                            </select>
                                            <span class="input-group-addon">需</span>
                                            <input type="text" name="landUpInfo[<?= $key ?>][3][num]" value="<?php if (!empty($landUpInfo) && !empty($landUpInfo[$key][3]['num'])){ echo $landUpInfo[$key][3]['num']; } ?>"  class="form-control">
                                            <span class="input-group-addon">颗</span>
                                            <?php } ?>
										</div>
										<?php } ?>
										<?php } ?>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">种子产生</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<div  class="input-group">
										<div  class="input-group">
											<span class="input-group-addon">每次种植</span>
											<input type="text" name="landFruit[seed]" value="<?php if (!empty($landFruit) && !empty($landFruit["seed"])){ echo $landFruit["seed"]; } ?>"  class="form-control">
											<span class="input-group-addon">种子 产生最少</span>
											<input type="text" name="landFruit[min]" value="<?php if (!empty($landFruit) && !empty($landFruit["min"])){ echo $landFruit["min"]; } ?>"  class="form-control">
											<span class="input-group-addon">无不良 产生最少</span>
											<input type="text" name="landFruit[med]" value="<?php if (!empty($landFruit) && !empty($landFruit["med"])){ echo $landFruit["med"]; } ?>"  class="form-control">
											<span class="input-group-addon">颗 产生最多</span>
											<input type="text" name="landFruit[max]" value="<?php if (!empty($landFruit) && !empty($landFruit["max"])){ echo $landFruit["max"]; } ?>"  class="form-control">
											<span class="input-group-addon">颗</span>
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">土地种子概率信息</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<?php foreach ($landType as $key => $row) { ?>
										<?php foreach ($product as $keys => $lists) { ?>
										<div  class="input-group">
											<span class="input-group-addon"><?= $row ?>-<?= $lists->title ?></span>
											<input type="hidden" name="landInfo[<?= $key ?>][<?= $keys ?>][id]" value="<?= $lists->id ?>"  class="form-control">
											<input type="text" name="landInfo[<?= $key ?>][<?= $keys ?>][chance]" value="<?php if (!empty($landInfo) && !empty($landInfo[$key][$keys]['chance'])){ echo $landInfo[$key][$keys]['chance']; } ?>"  class="form-control">
											<span class="input-group-addon">%</span>
										</div>
										<?php } ?>
									<?php } ?>
								</div>
							</div>
							<?php foreach ($landSuper as $r) { ?>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label"><?= $r ?>级种子概率</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<?php foreach ($landType as $key => $row) { ?>
										<?php if ($key > 2) { ?>
										<?php foreach ($product as $keys => $lists) { ?>
										<div  class="input-group">
											<span class="input-group-addon"><?= $row ?>-<?= $lists->title ?></span>
											<input type="hidden" name="landInfo[<?= $r ?>][<?= $key ?>][<?= $keys ?>][id]" value="<?= $lists->id ?>"  class="form-control">
											<input type="text" name="landInfo[<?= $r ?>][<?= $key ?>][<?= $keys ?>][chance]" value="<?php if (!empty($landInfo) && !empty($landInfo[$r][$key][$keys]['chance'])){ echo $landInfo[$r][$key][$keys]['chance']; } ?>"  class="form-control">
											<span class="input-group-addon">%</span>
										</div>
										<?php } ?>
										<?php } ?>
									<?php } ?>
								</div>
							</div>
							<?php } ?>
							<div class="panel-footer text-left">
								<input class="form-control"  type="hidden" name="id" value="<?php if ($item != '') { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<?php } ?>
		<?php if ($op == 'statueInfo') { ?>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'statueInfo') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=statueInfo">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">神像信息</label>
								<div class="col-xs-12 col-sm-10 col-md-12 col-lg-10">
									<?php foreach ($statueType as $key => $row) { ?>
										<div  class="input-group">
											<span class="input-group-addon"><?= $row ?></span>
											<input type="text" name="statueInfo[<?= $key ?>][tName]" value="<?php if (!empty($statueInfo) && !empty($statueInfo[$key]["tName"])){ echo $statueInfo[$key]["tName"]; } ?>"  class="form-control">
											<span class="input-group-addon">简介</span>
											<input type="text" name="statueInfo[<?= $key ?>][depict]" value="<?php if (!empty($statueInfo) && !empty($statueInfo[$key]["depict"])){ echo $statueInfo[$key]["depict"]; } ?>"  class="form-control">
										</div>
										<div  class="input-group">
										<?php if ($user_type == 'yunji1') { ?>
                                         	<span class="input-group-addon">兑换需</span>
                                         		<select name="statueInfo[<?= $key ?>][fid]" class="form-control">
                                         			<?php foreach ($product as $keys => $lists) { ?>
                                         				<option value="<?= $lists->id ?>" <?php if(!empty($statueInfo) && $statueInfo[$key]["fid2"] ==$lists->id ){ echo "selected";}?>>
                                         				<?= $lists->title ?>
                                         				</option>
                                                    <?php } ?>
                                                </select>
                                         			<span class="input-group-addon">数量</span>
                                         				<input type="text" name="statueInfo[<?= $key ?>][fprice]" value="<?php if (!empty($statueInfo) && $statueInfo[$key]['fprice'] >0){ echo $statueInfo[$key]['fprice']; }?>"  class="form-control">
                                         				<span class="input-group-addon">+</span>
                                         				<select name="statueInfo[<?= $key ?>][fid2]" class="form-control">
                                         				<?php foreach ($product as $keys => $lists) { ?>
                                         				<option value="<?= $lists->id ?>" <?php if(!empty($statueInfo) && $statueInfo[$key]["fid2"] ==$lists->id ){ echo "selected";}?>>
                                         				<?= $lists->title ?>
                                         				</option>
                                         				<?php } ?>
                                         				</select>
                                         				<span class="input-group-addon">数量</span>
                                         				<input type="text" name="statueInfo[<?= $key ?>][fprice2]" value="<?php if (!empty($statueInfo) && $statueInfo[$key]['fprice2'] >0){ echo $statueInfo[$key]['fprice2']; }?>"  class="form-control">
                                         			    <span class="input-group-addon">效果时间</span>
                                                        <input type="text" name="statueInfo[<?= $key ?>][time]" value="<?php if (!empty($statueInfo) && !empty($statueInfo[$key]["time"])){ echo $statueInfo[$key]["time"]; } ?>"  class="form-control">
                                        <?php } else { ?>
											<span class="input-group-addon">宝石</span>
											<select name="statueInfo[<?= $key ?>][tId]" class="form-control">
												<?php foreach ($goods as $keys => $lists) { ?>
												<option value="<?= $lists->tId ?>" <?php if(!empty($statueInfo) && $statueInfo[$key]["tId"] ==$lists->tId ){ echo "selected";}?>>
														<?= $lists->tName ?>
													</option>
												<?php } ?>
											</select>
											<span class="input-group-addon">需数量</span>
                                            <input type="text" name="statueInfo[<?= $key ?>][price]" value="<?php if (!empty($statueInfo) && !empty($statueInfo[$key]["price"])){ echo $statueInfo[$key]["price"]; } ?>"  class="form-control">
											<span class="input-group-addon">效果时间</span>
											<input type="text" name="statueInfo[<?= $key ?>][time]" value="<?php if (!empty($statueInfo) && !empty($statueInfo[$key]["time"])){ echo $statueInfo[$key]["time"]; } ?>"  class="form-control">
										<?php } ?>
										</div>
									<?php } ?>
								</div>
							</div>
							<div class="panel-footer text-left">
								<input class="form-control"  type="hidden" name="id" value="<?php if ($item != '') { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<?php } ?>
		<?php if ($op == 'recharge') { ?>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'recharge') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=recharge">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">充值信息</label>
								<div class="col-xs-12 col-sm-10 col-md-12 col-lg-10">
									<div  class="input-group">
										<span class="input-group-addon">1 充值</span>
										<input type="hidden" name="recharge[1][id]" value="1"  class="form-control">
										<input type="text" name="recharge[1][diamonds]" value="<?php if (!empty($recharge) && !empty($recharge[1]["diamonds"])){ echo $recharge[1]["diamonds"]; } ?>"  class="form-control">
										<span class="input-group-addon">钻石，需要</span>
										<input type="text" name="recharge[1][money]" value="<?php if (!empty($recharge) && !empty($recharge[1]["money"])){ echo $recharge[1]["money"]; } ?>"  class="form-control">
										<span class="input-group-addon">金币,赠送</span>
										<input type="text" name="recharge[1][give]" value="<?php if (!empty($recharge) && !empty($recharge[1]["give"])){ echo $recharge[1]["give"]; } ?>"  class="form-control">
										<span class="input-group-addon">钻石</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">2 充值</span>
										<input type="hidden" name="recharge[2][id]" value="2"  class="form-control">
										<input type="text" name="recharge[2][diamonds]" value="<?php if (!empty($recharge) && !empty($recharge[2]["diamonds"])){ echo $recharge[2]["diamonds"]; } ?>"  class="form-control">
										<span class="input-group-addon">钻石，需要</span>
										<input type="text" name="recharge[2][money]" value="<?php if (!empty($recharge) && !empty($recharge[2]["money"])){ echo $recharge[2]["money"]; } ?>"  class="form-control">
										<span class="input-group-addon">金币,赠送</span>
										<input type="text" name="recharge[2][give]" value="<?php if (!empty($recharge) && !empty($recharge[2]["give"])){ echo $recharge[2]["give"]; } ?>"  class="form-control">
										<span class="input-group-addon">钻石</span>
									</div>
									<div  class="input-group">
										<span class="input-group-addon">3 充值</span>
										<input type="hidden" name="recharge[3][id]" value="3"  class="form-control">
										<input type="text" name="recharge[3][diamonds]" value="<?php if (!empty($recharge) && !empty($recharge[3]["diamonds"])){ echo $recharge[3]["diamonds"]; } ?>"  class="form-control">
										<span class="input-group-addon">钻石，需要</span>
										<input type="text" name="recharge[3][money]" value="<?php if (!empty($recharge) && !empty($recharge[3]["money"])){ echo $recharge[3]["money"]; } ?>"  class="form-control">
										<span class="input-group-addon">金币,赠送</span>
										<input type="text" name="recharge[3][give]" value="<?php if (!empty($recharge) && !empty($recharge[3]["give"])){ echo $recharge[3]["give"]; } ?>"  class="form-control">
										<span class="input-group-addon">钻石</span>
									</div>
								</div>
							</div>
<!--							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">返佣设置</label>
								<div class="col-xs-12 col-sm-10 col-md-12 col-lg-10" id="rebate">
									<?php if(empty($rebate)){?>
									<div  class="input-group" >
										<span class="input-group-addon">第1代</span>
										<input type="text" name="rebate[1][num]" value="1"  class="form-control">
										<span class="input-group-addon">%</span>
										<span class="input-group-addon" onclick="addLevel()"><i class="glyphicon glyphicon-plus"></i></span>
									</div>
									<?php }else{?>
									<?php foreach ($rebate as $key => $list) { ?>
									<div  class="input-group" >
										<span class="input-group-addon">第<?= $key ?>代</span>
										<input type="text" name="rebate[<?= $key ?>][num]" value="<?php if (!empty($list) && !empty($list["num"])){ echo $list["num"]; } ?>"  class="form-control">
										<span class="input-group-addon">%</span>
										<span class="input-group-addon" onclick="addLevel()"><i class="glyphicon glyphicon-plus"></i></span>
									</div>
									<?php } ?>
									<?php }?>
								</div>
							</div>-->
							<div class="panel-footer text-left">
								<input class="form-control"  type="hidden" name="id" value="<?php if ($item != '') { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<script>
			var i = 1;
			var attrHtml = '<div class="input-group" data-item="##">\
								<span class="input-group-addon">第##代</span>\
								<input type="text" name="rebate[##][num]" value="0" id="num" class="form-control">\
								<span class="input-group-addon">%</span>';
			attrHtml +='<span class="input-group-addon" onclick="delLevel(this)"><i class="glyphicon glyphicon-minus"></i></span>\
						</div>';
			$(function () {
				i = $("#rebate div").last().attr("data-item");
			});
			function addLevel() {
				++i;
				if(i>10){
					return false;
				}
				$("#rebate").append(attrHtml.replace(/##/g, i));
			}
			function delLevel(obj) {
				var num = $(obj).parent().index() + 1;
				var all = $("#rebate div").length;
				if (num < all) {
					alert("请按顺序删除，不能断层");
					return;
				} else {
					i--;
					$(obj).parent().remove();
				}
			}
			</script>
		<?php } ?>
		<?php if ($op == 'background') { ?>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'background') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=background">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">背景信息</label>
								<div class="col-xs-12 col-sm-10 col-md-12 col-lg-10">
									<div class="col-xs-12 col-sm-10 col-md-10 col-lg-10">
										<div  class="input-group">
											<span class="input-group-addon"><?php echo $backgroundType[2];?>需</span>
											<select name="background[2][1][pid]" class="form-control">
											<?php foreach ($product as $keys => $lists) { ?>
												<option value="<?= $lists->id ?>" <?php if (isset($background)) { ?><?php if ($background[2][1]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
													<?= $lists->title ?>
												</option>
											<?php } ?>
											</select>
											<span class="input-group-addon">数量</span>
											<input type="text" name="background[2][1][num]" value="<?php if (!empty($background) && $background[2][1]['num'] >0){ echo $background[2][1]['num']; }?>"  class="form-control">
											<span class="input-group-addon">+</span>
											<select name="background[2][2][pid]" class="form-control">
											<?php foreach ($product as $keys => $lists) { ?>
												<option value="<?= $lists->id ?>" <?php if (isset($background)) { ?><?php if ($background[2][2]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
													<?= $lists->title ?>
												</option>
											<?php } ?>
											</select>
											<span class="input-group-addon">数量</span>
											<input type="text" name="background[2][2][num]" value="<?php if (!empty($background) && $background[2][2]['num'] >0){ echo $background[2][2]['num']; }?>"  class="form-control">
											<span class="input-group-addon">+钻石</span>
											<input type="text" name="background[2][3][num]" value="<?php if (!empty($background) && $background[2][3]['num'] >0){ echo $background[2][3]['num']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon"><?php echo $backgroundType[3];?>需</span>
											<select name="background[3][1][pid]" class="form-control">
											<?php foreach ($product as $keys => $lists) { ?>
												<option value="<?= $lists->id ?>" <?php if (isset($background)) { ?><?php if ($background[3][1]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
													<?= $lists->title ?>
												</option>
											<?php } ?>
											</select>
											<span class="input-group-addon">数量</span>
											<input type="text" name="background[3][1][num]" value="<?php if (!empty($background) && $background[3][1]['num'] >0){ echo $background[3][1]['num']; }?>"  class="form-control">
											<span class="input-group-addon">+</span>
											<select name="background[3][2][pid]" class="form-control">
											<?php foreach ($product as $keys => $lists) { ?>
												<option value="<?= $lists->id ?>" <?php if (isset($background)) { ?><?php if ($background[3][2]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
													<?= $lists->title ?>
												</option>
											<?php } ?>
											</select>
											<span class="input-group-addon">数量</span>
											<input type="text" name="background[3][2][num]" value="<?php if (!empty($background) && $background[3][2]['num'] >0){ echo $background[3][2]['num']; }?>"  class="form-control">
											<span class="input-group-addon">+钻石</span>
											<input type="text" name="background[3][3][num]" value="<?php if (!empty($background) && $background[3][3]['num'] >0){ echo $background[3][3]['num']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon"><?php echo $backgroundType[4];?>需</span>
											<select name="background[4][1][pid]" class="form-control">
											<?php foreach ($product as $keys => $lists) { ?>
												<option value="<?= $lists->id ?>" <?php if (isset($background)) { ?><?php if ($background[4][1]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
													<?= $lists->title ?>
												</option>
											<?php } ?>
											</select>
											<span class="input-group-addon">数量</span>
											<input type="text" name="background[4][1][num]" value="<?php if (!empty($background) && $background[4][1]['num'] >0){ echo $background[4][1]['num']; }?>"  class="form-control">
											<span class="input-group-addon">+</span>
											<select name="background[4][2][pid]" class="form-control">
											<?php foreach ($product as $keys => $lists) { ?>
												<option value="<?= $lists->id ?>" <?php if (isset($background)) { ?><?php if ($background[4][2]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
													<?= $lists->title ?>
												</option>
											<?php } ?>
											</select>
											<span class="input-group-addon">数量</span>
											<input type="text" name="background[4][2][num]" value="<?php if (!empty($background) && $background[4][2]['num'] >0){ echo $background[4][2]['num']; }?>"  class="form-control">
											<span class="input-group-addon">+钻石</span>
											<input type="text" name="background[4][3][num]" value="<?php if (!empty($background) && $background[4][3]['num'] >0){ echo $background[4][3]['num']; }?>"  class="form-control">
										</div>
									</div>
								</div>
							</div>
							<div class="panel-footer text-left">
								<input class="form-control"  type="hidden" name="id" value="<?php if ($item != '') { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<?php } ?>
		<?php if ($op == 'package') { ?>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'package') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=package">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">推荐注册礼包信息</label>
								<div class="col-xs-12 col-sm-10 col-md-12 col-lg-10">
									<div class="col-xs-12 col-sm-10 col-md-10 col-lg-10">
										<div  class="input-group">
											<span class="input-group-addon">种子</span>
											<input type="text" name="package[seed]" value="<?php if (!empty($package) && $package['seed'] >0){ echo $package['seed']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['diamonds']?></span>
											<input type="text" name="package[diamonds]" value="<?php if (!empty($package) && $package["diamonds"] >0){ echo $package['diamonds']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['cfert']?></span>
											<input type="text" name="package[cfert]" value="<?php if (!empty($package) && $package["cfert"] >0){ echo $package['cfert']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['hcide']?></span>
											<input type="text" name="package[hcide]" value="<?php if (!empty($package) && $package["hcide"] >0){ echo $package['hcide']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['icide']?></span>
											<input type="text" name="package[icide]" value="<?php if (!empty($package) && $package["icide"] >0){ echo $package['icide']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['wcan']?></span>
											<input type="text" name="package[wcan]" value="<?php if (!empty($package) && $package["wcan"] >0){ echo $package['wcan']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon"><?php echo $tableType['emerald']?></span>
											<input type="text" name="package[emerald]" value="<?php if (!empty($package) && $package["emerald"] >0){ echo $package['emerald']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['purplegem']?></span>
											<input type="text" name="package[purplegem]" value="<?php if (!empty($package) && $package["purplegem"] >0){ echo $package['purplegem']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['sapphire']?></span>
											<input type="text" name="package[sapphire]" value="<?php if (!empty($package) && $package["sapphire"] >0){ echo $package['sapphire']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['topaz']?></span>
											<input type="text" name="package[topaz]" value="<?php if (!empty($package) && $package["topaz"] >0){ echo $package['topaz']; }?>"  class="form-control">
											<?php if ($user_type == 'kk') { ?>
                                            <span class="input-group-addon">+<?php echo $tableType['feed']?></span>
                                            <input type="text" name="package[feed]" value="<?php if (!empty($package) && $package["feed"] >0){ echo $package['feed']; }?>"  class="form-control">
                                            <?php } ?>
										</div>
										<div  class="input-group">
											<span class="input-group-addon">礼包说明</span>
											<input type="text" name="package[info]" value="<?php if (!empty($package) && $package["info"] >0){ echo $package['info']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon">是否需有推荐人</span>
											<select name="package[status]" class="form-control">
												<option value="1" <?php if (!empty($package) && $package["status"] ==1){ echo "selected"; }?>>需要</option>
												<option value="0" <?php if (!empty($package) && $package["status"] !=1){ echo "selected"; }?>>不需要</option>
											</select>
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">补偿礼包信息</label>
								<div class="col-xs-12 col-sm-10 col-md-12 col-lg-10">
									<div class="col-xs-12 col-sm-10 col-md-10 col-lg-10">
										<div  class="input-group">
											<span class="input-group-addon">种子</span>
											<input type="text" name="indemnify[seed]" value="<?php if (!empty($indemnify) && $indemnify['seed'] >0){ echo $indemnify['seed']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['diamonds']?></span>
											<input type="text" name="indemnify[diamonds]" value="<?php if (!empty($indemnify) && $indemnify["diamonds"] >0){ echo $indemnify['diamonds']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['cfert']?></span>
											<input type="text" name="indemnify[cfert]" value="<?php if (!empty($indemnify) && $indemnify["cfert"] >0){ echo $indemnify['cfert']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['hcide']?></span>
											<input type="text" name="indemnify[hcide]" value="<?php if (!empty($indemnify) && $indemnify["hcide"] >0){ echo $indemnify['hcide']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['icide']?></span>
											<input type="text" name="indemnify[icide]" value="<?php if (!empty($indemnify) && $indemnify["icide"] >0){ echo $indemnify['icide']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['wcan']?></span>
											<input type="text" name="indemnify[wcan]" value="<?php if (!empty($indemnify) && $indemnify["wcan"] >0){ echo $indemnify['wcan']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon"><?php echo $tableType['emerald']?></span>
											<input type="text" name="indemnify[emerald]" value="<?php if (!empty($indemnify) && $indemnify["emerald"] >0){ echo $indemnify['emerald']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['purplegem']?></span>
											<input type="text" name="indemnify[purplegem]" value="<?php if (!empty($indemnify) && $indemnify["purplegem"] >0){ echo $indemnify['purplegem']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['sapphire']?></span>
											<input type="text" name="indemnify[sapphire]" value="<?php if (!empty($indemnify) && $indemnify["sapphire"] >0){ echo $indemnify['sapphire']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['topaz']?></span>
											<input type="text" name="indemnify[topaz]" value="<?php if (!empty($indemnify) && $indemnify["topaz"] >0){ echo $indemnify['topaz']; }?>"  class="form-control">
											<?php if ($user_type == 'kk') { ?>
                                            <span class="input-group-addon">+<?php echo $tableType['feed']?></span>
                                            <input type="text" name="indemnify[feed]" value="<?php if (!empty($indemnify) && $indemnify["feed"] >0){ echo $indemnify['feed']; }?>"  class="form-control">
                                            <?php } ?>
										</div>
										<div  class="input-group">
											<span class="input-group-addon">礼包说明</span>
											<input type="text" name="indemnify[info]" value="<?php if (!empty($indemnify) && $indemnify["info"] >0){ echo $indemnify['info']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon">状态</span>
											<select name="indemnify[status]" class="form-control">
												<option value="1" <?php if (!empty($indemnify) && $indemnify["status"] ==1){ echo "selected"; }?>>启用</option>
												<option value="0" <?php if (!empty($indemnify) && $indemnify["status"] !=1){ echo "selected"; }?>>关闭</option>
											</select>
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">新人礼包信息</label>
								<div class="col-xs-12 col-sm-10 col-md-12 col-lg-10">
									<div class="col-xs-12 col-sm-10 col-md-10 col-lg-10">
										<div  class="input-group">
											<span class="input-group-addon">种子</span>
											<input type="text" name="newGiftPack[seed]" value="<?php if (!empty($newGiftPack) && $newGiftPack['seed'] >0){ echo $newGiftPack['seed']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['diamonds']?></span>
											<input type="text" name="newGiftPack[diamonds]" value="<?php if (!empty($newGiftPack) && $newGiftPack["diamonds"] >0){ echo $newGiftPack['diamonds']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['cfert']?></span>
											<input type="text" name="newGiftPack[cfert]" value="<?php if (!empty($newGiftPack) && $newGiftPack["cfert"] >0){ echo $newGiftPack['cfert']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['hcide']?></span>
											<input type="text" name="newGiftPack[hcide]" value="<?php if (!empty($newGiftPack) && $newGiftPack["hcide"] >0){ echo $newGiftPack['hcide']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['icide']?></span>
											<input type="text" name="newGiftPack[icide]" value="<?php if (!empty($newGiftPack) && $newGiftPack["icide"] >0){ echo $newGiftPack['icide']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['wcan']?></span>
											<input type="text" name="newGiftPack[wcan]" value="<?php if (!empty($newGiftPack) && $newGiftPack["wcan"] >0){ echo $newGiftPack['wcan']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon"><?php echo $tableType['emerald']?></span>
											<input type="text" name="newGiftPack[emerald]" value="<?php if (!empty($newGiftPack) && $newGiftPack["emerald"] >0){ echo $newGiftPack['emerald']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['purplegem']?></span>
											<input type="text" name="newGiftPack[purplegem]" value="<?php if (!empty($newGiftPack) && $newGiftPack["purplegem"] >0){ echo $newGiftPack['purplegem']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['sapphire']?></span>
											<input type="text" name="newGiftPack[sapphire]" value="<?php if (!empty($newGiftPack) && $newGiftPack["sapphire"] >0){ echo $newGiftPack['sapphire']; }?>"  class="form-control">
											<span class="input-group-addon">+<?php echo $tableType['topaz']?></span>
											<input type="text" name="newGiftPack[topaz]" value="<?php if (!empty($newGiftPack) && $newGiftPack["topaz"] >0){ echo $newGiftPack['topaz']; }?>"  class="form-control">
											<?php if ($user_type == 'kk') { ?>
                                            <span class="input-group-addon">+<?php echo $tableType['feed']?></span>
                                            <input type="text" name="newGiftPack[feed]" value="<?php if (!empty($newGiftPack) && $newGiftPack["feed"] >0){ echo $newGiftPack['feed']; }?>"  class="form-control">
                                            <?php } ?>
										</div>
										<div  class="input-group">
											<span class="input-group-addon">礼包说明</span>
											<input type="text" name="newGiftPack[info]" value="<?php if (!empty($newGiftPack) && $newGiftPack["info"] >0){ echo $newGiftPack['info']; }?>"  class="form-control">
											<input type="hidden" name="newGiftPack[starttime]" value="<?= time() ?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon">状态</span>
											<select name="newGiftPack[status]" class="form-control">
												<option value="1" <?php if (!empty($newGiftPack) && $newGiftPack["status"] ==1){ echo "selected"; }?>>启用</option>
												<option value="0" <?php if (!empty($newGiftPack) && $newGiftPack["status"] !=1){ echo "selected"; }?>>关闭</option>
											</select>
										</div>
									</div>
								</div>
							</div>
							<div class="panel-footer text-left">
								<input class="form-control"  type="hidden" name="id" value="<?php if ($item != '') { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<?php } ?>
		<?php if ($op == 'sign') { ?>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'sign') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=sign">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">签到信息</label>
								<div class="col-xs-12 col-sm-10 col-md-12 col-lg-10">
									<div class="col-xs-12 col-sm-10 col-md-10 col-lg-10">
										<div  class="input-group">
											<span class="input-group-addon">每天签到次数</span>
											<input type="text" name="sign[daySize]" value="<?php if (!empty($sign) && $sign['daySize'] >0){ echo $sign['daySize']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon">种子</span>
											<input type="text" name="sign[seed]" value="<?php if (!empty($sign) && $sign['seed'] >0){ echo $sign['seed']; }?>"  class="form-control">
											<span class="input-group-addon"><?php echo $tableType['diamonds']?></span>
											<input type="text" name="sign[diamonds]" value="<?php if (!empty($sign) && $sign["diamonds"] >0){ echo $sign['diamonds']; }?>"  class="form-control">
											<span class="input-group-addon"><?php echo $tableType['cfert']?></span>
											<input type="text" name="sign[cfert]" value="<?php if (!empty($sign) && $sign["cfert"] >0){ echo $sign['cfert']; }?>"  class="form-control">
											<span class="input-group-addon"><?php echo $tableType['hcide']?></span>
											<input type="text" name="sign[hcide]" value="<?php if (!empty($sign) && $sign["hcide"] >0){ echo $sign['hcide']; }?>"  class="form-control">
											<span class="input-group-addon"><?php echo $tableType['icide']?></span>
											<input type="text" name="sign[icide]" value="<?php if (!empty($sign) && $sign["icide"] >0){ echo $sign['icide']; }?>"  class="form-control">
											<span class="input-group-addon"><?php echo $tableType['wcan']?></span>
											<input type="text" name="sign[wcan]" value="<?php if (!empty($sign) && $sign["wcan"] >0){ echo $sign['wcan']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon"><?php echo $tableType['emerald']?></span>
											<input type="text" name="sign[emerald]" value="<?php if (!empty($sign) && $sign["emerald"] >0){ echo $sign['emerald']; }?>"  class="form-control">
											<span class="input-group-addon"><?php echo $tableType['purplegem']?></span>
											<input type="text" name="sign[purplegem]" value="<?php if (!empty($sign) && $sign["purplegem"] >0){ echo $sign['purplegem']; }?>"  class="form-control">
											<span class="input-group-addon"><?php echo $tableType['sapphire']?></span>
											<input type="text" name="sign[sapphire]" value="<?php if (!empty($sign) && $sign["sapphire"] >0){ echo $sign['sapphire']; }?>"  class="form-control">
											<span class="input-group-addon"><?php echo $tableType['topaz']?></span>
											<input type="text" name="sign[topaz]" value="<?php if (!empty($sign) && $sign["topaz"] >0){ echo $sign['topaz']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon">奖励类型</span>
											<select name="sign[types]" class="form-control">
<!--												<option value="1" <?php if (!empty($sign) && $sign["types"] ==1){ echo "selected"; }?>>全部奖励</option>-->
												<option value="2" <?php if (!empty($sign) && $sign["types"] ==2){ echo "selected"; }?>>随机奖励</option>
											</select>
										</div>
										<div  class="input-group">
											<span class="input-group-addon">状态</span>
											<select name="sign[status]" class="form-control">
												<option value="1" <?php if (!empty($sign) && $sign["status"] ==1){ echo "selected"; }?>>启用</option>
												<option value="0" <?php if (!empty($sign) && $sign["status"] !=1){ echo "selected"; }?>>关闭</option>
											</select>
										</div>
									</div>
								</div>
							</div>
							<div class="panel-footer text-left">
								<input class="form-control"  type="hidden" name="id" value="<?php if ($item != '') { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<?php } ?>
		<?php if ($op == 'steal') { ?>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'steal') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=steal">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">好友互偷</label>
								<div class="col-xs-12 col-sm-10 col-md-12 col-lg-10">
									<div class="col-xs-12 col-sm-10 col-md-10 col-lg-10">
										<?php foreach ($userType as $l => $v) { ?>
										<div  class="input-group">
											<span class="input-group-addon">会员<?= $v ?>级可偷取</span>
											<select name="steal[goods][<?= $v ?>][]" class="form-control" multiple="true" style="height:210px;">
												<?php foreach ($product as $keys => $lists) { ?>
													<option value="<?= $lists->id ?>" <?php if (isset($steal)) { ?><?php if(@in_array($lists->id,$steal['goods'][$v])){?>selected<?php }?><?php } ?>>
														<?= $lists->title ?>
													</option>
												<?php } ?>
											</select>
										</div>
										<?php } ?>
										<div  class="input-group">
											<span class="input-group-addon">偷取成功最少</span>
											<input type="text" name="steal[success][min]" value="<?php if (!empty($steal) && $steal["success"]['min'] >0){ echo $steal['success']['min']; }?>"  class="form-control">
											<span class="input-group-addon">最多</span>
											<input type="text" name="steal[success][max]" value="<?php if (!empty($steal) && $steal["success"]['max'] >0){ echo $steal['success']['max']; }?>"  class="form-control">
											<span class="input-group-addon">扣除宠物体力</span>
											<input type="text" name="steal[success][power]" value="<?php if (!empty($steal) && $steal["success"]['power'] >0){ echo $steal['success']['power']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon">偷取失败扣除宠物体力</span>
											<input type="text" name="steal[error][power]" value="<?php if (!empty($steal) && $steal["error"]['power'] >0){ echo $steal['error']['power']; }?>"  class="form-control">
											<span class="input-group-addon">增加被偷方宠物经验</span>
											<input type="text" name="steal[error][experience]" value="<?php if (!empty($steal) && $steal["error"]['experience'] >0){ echo $steal['error']['experience']; }?>"  class="form-control">
										</div>
										<div  class="input-group">
											<span class="input-group-addon">偷取概率</span>
											<input type="text" name="steal[chance][0]" value="<?php if (!empty($steal) && $steal["chance"][0] >0){ echo $steal['chance'][0]; }?>"  class="form-control">
											<span class="input-group-addon">%宠物提高概率</span>
											<input type="text" name="steal[chance][1]" value="<?php if (!empty($steal) && $steal["chance"][1] >0){ echo $steal['chance'][1]; }?>"  class="form-control">
											<span class="input-group-addon">%降低概率</span>
											<input type="text" name="steal[chance][2]" value="<?php if (!empty($steal) && $steal["chance"][2] >0){ echo $steal['chance'][2]; }?>"  class="form-control">
											<span class="input-group-addon">%</span>
										</div>
										<div  class="input-group">
											<span class="input-group-addon">每天偷取</span>
											<input type="text" name="steal[dayInfo][1]" value="<?php if (!empty($steal) && $steal["dayInfo"][1] >0){ echo $steal['dayInfo'][1]; }?>"  class="form-control">
											<span class="input-group-addon">次单个好友被偷上限</span>
											<input type="text" name="steal[dayInfo][2]" value="<?php if (!empty($steal) && $steal["dayInfo"][2] >0){ echo $steal['dayInfo'][2]; }?>"  class="form-control">
											<span class="input-group-addon">次数</span>
										</div>
										<div  class="input-group">
											<span class="input-group-addon">状态</span>
											<select name="steal[status]" class="form-control">
												<option value="1" <?php if (!empty($sign) && $sign["status"] ==1){ echo "selected"; }?>>启用</option>
												<option value="0" <?php if (!empty($sign) && $sign["status"] !=1){ echo "selected"; }?>>关闭</option>
											</select>
										</div>
									</div>
								</div>
							</div>
							<div class="panel-footer text-left">
								<input class="form-control"  type="hidden" name="id" value="<?php if ($item != '') { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<?php } ?>
		<?php if ($op == 'crystal') { ?>
        		<div class="tab-content">
        			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'crystal') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
        				<div class="panel">
        					<div class="panel-body">
        						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=crystal">
        							<div class="form-group">
        								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">兑换信息</label>
        								<div class="col-xs-12 col-sm-10 col-md-12 col-lg-10">
        									<div class="col-xs-12 col-sm-10 col-md-10 col-lg-10">
        										<div  class="input-group">
                                                	<span class="input-group-addon">需</span>
                                                	<select name="crystal[pid1]" class="form-control">
                                                	<?php foreach ($product as $keys => $lists) { ?>
                                                	<option value="<?= $lists->id ?>" <?php if (isset($crystal)) { ?><?php if ($lists->id == $crystal['pid1']) { ?>selected<?php } ?><?php } ?> >
                                                		<?= $lists->title ?>
                                                		</option>
                                                	<?php } ?>
                                                	</select>
                                                	<span class="input-group-addon">数量</span>
                                                	<input type="text" name="crystal[num1]" value=" <?php if (isset($crystal)) { ?><?= $crystal['num1'] ?><?php } ?>"  class="form-control">
                                                	<span class="input-group-addon">+</span>
                                                	<select name="crystal[pid2]" class="form-control">
                                                    <?php foreach ($product as $keys => $lists) { ?>
                                                         <option value="<?= $lists->id ?>" <?php if (isset($crystal)) { ?><?php if ($lists->id == $crystal['pid2']) { ?>selected<?php } ?><?php } ?> >
                                                         <?= $lists->title ?>
                                                         </option>
                                                    <?php } ?>
                                                    </select>
                                                    <span class="input-group-addon">数量</span>
                                                    <input type="text" name="crystal[num2]" value="<?php if (isset($crystal)) { ?><?= $crystal['num2'] ?><?php } ?>"  class="form-control">
                                                </div>

                                                <div class="input-group">
													<select name="crystal[pid3]" class="form-control">
													   <?php foreach ($product as $keys => $lists) { ?>
														  <option value="<?= $lists->id ?>" <?php if (isset($crystal)) { ?><?php if ($lists->id == $crystal['pid3']) { ?>selected<?php } ?><?php } ?>>
														   <?= $lists->title ?>
														  </option>
													   <?php } ?>
													   </select>
													   <span class="input-group-addon">数量</span>
													   <input type="text" name="crystal[num3]" value="<?php if (isset($crystal)) { ?><?= $crystal['num3'] ?><?php } ?>"  class="form-control">
													   <span class="input-group-addon">获得水晶</span>
													   <input type="text" name="crystal[crystal]" value="<?php if (isset($crystal)) { ?><?= $crystal['crystal'] ?><?php } ?>"  class="form-control">
                                                </div>
                                        	</div>

        								</div>
        							</div>
        							 <div class="form-group">
                                           <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
                                           <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                              <select name="crystal[status]" class="form-control">
                                                   <option value="1" <?php if (isset($crystal)) { ?><?php if ($crystal['status'] == 1) { ?>selected<?php } ?><?php } ?>>启用</option>
                                                   <option value="9" <?php if (isset($crystal)) { ?><?php if ($crystal['status'] == 9) { ?>selected<?php } ?><?php } ?>>禁用</option>
                                              </select>
                                           </div>
                                     </div>
        							<div class="panel-footer text-left">
        								<button class="btn btn-success" type="submit">提交</button>
        							</div>
        						</form>
        					</div>
        				</div>
        			</div>
        		</div>
        		<?php } ?>
        		<?php if ($op == 'downgrade') { ?>
				<div class="tab-content">
        			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'downgrade') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
        				<div class="panel">
        					<div class="panel-body">
        						<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=downgrade">
                                    <?php if ($user_type != 'jinlilai') { ?>
        							<div class="form-group">
										<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">限定房屋等级启用</label>
										 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
											<div class="input-group">
												<input type="text" name="downgrade[grade]" value="<?php if (isset($downgrade)) { ?><?= $downgrade['grade'] ?><?php } ?>"  class="form-control">
											 </div>
										</div>
									</div>
									<?php foreach ($downgrade['land'] as $key => $row) { ?>
									<div class="form-group">
										<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label"><?php if ($key > 0) { ?><?= $key ?><?php } ?>级房屋</label>
										 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
											<div class="input-group">
												<span class="input-group-addon">升级</span>
												<input type="text" name="downgrade[land][<?= $key ?>][day]" value="<?php if (isset($row['day'])) { ?><?= $row['day'] ?><?php } ?>"  class="form-control">
												<span class="input-group-addon">天后不升级掉到</span>
												<input type="text" name="downgrade[land][<?= $key ?>][grade]" value="<?php if (isset($row['grade'])) { ?><?= $row['grade'] ?><?php } ?>"  class="form-control">
												<span class="input-group-addon">级</span>
											 </div>
										</div>
									</div>
									<?php } ?>
        							 <div class="form-group">
										   <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
										   <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
											  <select name="downgrade[status]" class="form-control">
												   <option value="1" <?php if (isset($downgrade)) { ?><?php if ($downgrade['status'] == 1) { ?>selected<?php } ?><?php } ?>>启用</option>
												   <option value="0" <?php if (isset($downgrade)) { ?><?php if ($downgrade['status'] == 0) { ?>selected<?php } ?><?php } ?>>关闭</option>
											  </select>
										   </div>
									 </div>
									 <?php } ?>
									 <?php foreach ($downgrade['landInfo'] as $key => $row) { ?>
									<div class="form-group">
										<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label"><?php if ($key > 0) { ?><?= $key ?><?php } ?>级房屋的土地</label>
										 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
											<div class="input-group">
												<span class="input-group-addon">升级</span>
												<input type="text" name="downgrade[landInfo][<?= $key ?>][day]" value="<?php if (isset($row['day'])) { ?><?= $row['day'] ?><?php } ?>"  class="form-control">
												<span class="input-group-addon">天后土地降级</span>
											 </div>
											<?php foreach ($row['landLevel'] as $keys => $rows) { ?>
												<div class="input-group">
													<span class="input-group-addon"><?php if (isset($rows['title'])) { ?><?= $rows['title'] ?><?php } ?>周期</span>
													<input type="text" name="downgrade[landInfo][<?= $key ?>][landLevel][<?= $keys ?>][day]" value="<?php if (isset($rows['day'])) { ?><?= $rows['day'] ?><?php } ?>"  class="form-control">
													<span class="input-group-addon">天</span>
													<input type="hidden" name="downgrade[landInfo][<?= $key ?>][landLevel][<?= $keys ?>][title]" value="<?php if (isset($rows['title'])) { ?><?= $rows['title'] ?><?php } ?>"  class="form-control">
												 </div>
											<?php } ?>
										</div>
									</div>
									<?php } ?>

									  <div class="form-group">
										   <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
										   <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
											  <select name="downgrade[landStatus]" class="form-control">
												   <option value="1" <?php if (isset($downgrade)) { ?><?php if ($downgrade['landStatus'] == 1) { ?>selected<?php } ?><?php } ?>>启用</option>
												   <option value="0" <?php if (isset($downgrade)) { ?><?php if ($downgrade['landStatus'] == 0) { ?>selected<?php } ?><?php } ?>>关闭</option>
											  </select>
										   </div>
									 </div>
        							<div class="panel-footer text-left">
										<button class="btn btn-success" type="submit">提交</button>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
        		<?php } ?>
        		<?php if ($op == 'EMG') { ?>
        		<div class="tab-content">
					<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'EMG') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
						<div class="panel">
							<div class="panel-body">
								<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=EMG">
									<div class="form-group">
										<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">appkey</label>
										 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
												<input type="text" name="EMG[appkey]" value="<?php if (isset($EMG)) { ?><?= $EMG['appkey'] ?><?php } ?>"  class="form-control">
										</div>
									</div>
									<div class="form-group">
										<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">appSecret</label>
										 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
												<input type="text" name="EMG[appSecret]" value="<?php if (isset($EMG)) { ?><?= $EMG['appSecret'] ?><?php } ?>"  class="form-control">
										</div>
									</div>
									<div class="form-group">
										<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">域名</label>
										 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
												<input type="text" name="EMG[httpUrl]" value="<?php if (isset($EMG)) { ?><?= $EMG['httpUrl'] ?><?php } ?>"  class="form-control">
										</div>
									</div>
									<div class="panel-footer text-left">
										<button class="btn btn-success" type="submit">提交</button>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
        		<?php } ?>
        		<?php if ($op == 'pasture') { ?>
        		<div class="tab-content">
					<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'pasture') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
						<div class="panel">
							<div class="panel-body">
								<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=pasture">
									<div class="form-group">
										<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">牧场名称</label>
										 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
												<input type="text" name="pasture[title]" value="<?php if (isset($pasture)) { ?><?= $pasture['title'] ?><?php } ?>"  class="form-control">
										</div>
									</div>
									<div class="form-group">
										<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">牧场开启等级限制</label>
										 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
												<input type="text" name="pasture[grade]" value="<?php if (isset($pasture)) { ?><?= $pasture['grade'] ?><?php } ?>"  class="form-control">
										</div>
									</div>
									<div class="form-group">
                                    	<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">牧场升级信息</label>
                                    	 <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
											<?php foreach ($pastureLevel as $key => $row) { ?>
											<div  class="input-group">
												<span class="input-group-addon"><?php echo $key;?>升级</span>
												<span class="input-group-addon">钻石</span>
												<input type="text" name="pasture[upgrade][<?= $key ?>][diamonds]" value="<?php if (isset($pasture)) { ?><?= $row['diamonds'] ?><?php } ?>"  class="form-control">
												<span class="input-group-addon">个</span>
												<?php if ($user_type == 'kk') { ?>
												<span class="input-group-addon">农场需要</span>
												<input type="text" name="pasture[upgrade][<?= $key ?>][grade]" value="<?php if (isset($pasture)) { ?><?= $row['grade'] ?><?php } ?>"  class="form-control">
                                                <span class="input-group-addon">级</span>
												<?php } ?>
												<select name="pasture[upgrade][<?= $key ?>][goods][1][pid]" class="form-control">
												<?php foreach ($product2 as $keys => $lists) { ?>
													<option value="<?= $lists->id ?>" <?php if (isset($row)) { ?><?php if ($row['goods'][1]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
														<?= $lists->title ?>
													</option>
												<?php } ?>
												</select>
												<span class="input-group-addon">数量</span>
												<input type="text" name="pasture[upgrade][<?= $key ?>][goods][1][num]" value="<?php if (!empty($row) && $row['goods'][1]['num'] >0){ echo $row['goods'][1]['num']; }?>"  class="form-control">
												<span class="input-group-addon">+</span>
												<select name="pasture[upgrade][<?= $key ?>][goods][2][pid]" class="form-control">
												<?php foreach ($product2 as $keys => $lists) { ?>
													<option value="<?= $lists->id ?>" <?php if (isset($row)) { ?><?php if ($row['goods'][2]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
														<?= $lists->title ?>
													</option>
												<?php } ?>
												</select>
												<span class="input-group-addon">数量</span>
												<input type="text" name="pasture[upgrade][<?= $key ?>][goods][2][num]" value="<?php if (!empty($row) && $row['goods'][2]['num'] >0){ echo $row['goods'][2]['num']; }?>"  class="form-control">
												<span class="input-group-addon">动物</span>
												<select name="pasture[upgrade][<?= $key ?>][animal][aid]" class="form-control">
												<?php foreach ($animalInfo as $keys => $list) { ?>
													<option value="<?= $keys ?>" <?php if (isset($row['animal'])) { ?><?php if ($row['animal']['aid'] == $keys){?>selected<?php }?><?php } ?>>
														<?= $list['title'] ?>
													</option>
												<?php } ?>
												</select>
											</div>
											<?php } ?>
										</div>
									</div>
									<div class="form-group">
                                    	<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">牧场升级信息</label>
                                    	 <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                                    	 <?php foreach ($animalInfo as $key => $row) { ?>
                                    	 <div  class="input-group">
                                    		 <span class="input-group-addon"><?php echo $row['title'];?>描述</span>
                                    		 <input type="text" name="pasture[animal][<?= $key ?>][depict]" value="<?php echo $row['depict'];?>"  class="form-control">
                                    		 <span class="input-group-addon">钻石</span>
												<input type="text" name="pasture[animal][<?= $key ?>][diamonds]" value="<?php echo $row['diamonds'];?>"  class="form-control">
												<span class="input-group-addon">个</span>
												<select name="pasture[animal][<?= $key ?>][goods][1][pid]" class="form-control">
												<?php foreach ($product as $keys => $lists) { ?>
													<option value="<?= $lists->id ?>" <?php if (isset($row)) { ?><?php if ($row['goods'][1]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
														<?= $lists->title ?>
													</option>
												<?php } ?>
												</select>
												<span class="input-group-addon">数量</span>
												<input type="text" name="pasture[animal][<?= $key ?>][goods][1][num]" value="<?php if (!empty($row['goods']) && $row['goods'][1]['num'] >0){ echo $row['goods'][1]['num']; }?>"  class="form-control">
												<span class="input-group-addon">+</span>
												<select name="pasture[animal][<?= $key ?>][goods][2][pid]" class="form-control">
												<?php foreach ($product2 as $keys => $lists) { ?>
													<option value="<?= $lists->id ?>" <?php if (isset($row)) { ?><?php if ($row['goods'][2]['pid'] == $lists->id){?>selected<?php }?><?php } ?>>
														<?= $lists->title ?>
													</option>
												<?php } ?>
												</select>
												<span class="input-group-addon">数量</span>
												<input type="text" name="pasture[animal][<?= $key ?>][goods][2][num]" value="<?php if (!empty($row['goods']) && $row['goods'][2]['num'] >0){ echo $row['goods'][2]['num']; }?>"  class="form-control">
												<input type="hidden" name="pasture[animal][<?= $key ?>][title]" value="<?php echo $row['title'];?>"  class="form-control">
												<span class="input-group-addon">每组</span>
												<input type="text" name="pasture[animal][<?= $key ?>][group]" value="<?php if (!empty($row['group']) && $row['group'] >0){ echo $row['group']; }?>"  class="form-control">
												<span class="input-group-addon">个周期</span>
												<input type="text" name="pasture[animal][<?= $key ?>][days]" value="<?php if (!empty($row['days']) && $row['days'] >0){ echo $row['days']; }?>"  class="form-control">
												<span class="input-group-addon">天每次喂养</span>
												<input type="text" name="pasture[animal][<?= $key ?>][feed]" value="<?php if (!empty($row['feed']) && $row['feed'] >0){ echo $row['feed']; }?>"  class="form-control">
												<span class="input-group-addon">袋饲料</span>
												<?php if ($user_type == 'kk') { ?>
                                                <span class="input-group-addon">最多喂养</span>
                                                <input type="text" name="pasture[animal][<?= $key ?>][limit]" value="<?php if (!empty($row['limit']) && $row['limit'] >0){ echo $row['limit']; }?>"  class="form-control">
                                                <span class="input-group-addon">组</span>
                                                <?php } ?>
                                    	 </div>
                                    	 <?php } ?>
                                    	</div>
                                    </div>
									<div class="form-group">
										<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">牧场状态</label>
										 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
											<select name="pasture[status]" class="form-control">
												<option value="1" <?php if ($pasture != '') { ?><?php if ($pasture['status'] == 1) { ?>selected<?php } ?><?php } ?>>启用</option>
												<option value="0" <?php if ($pasture != '') { ?><?php if ($pasture['status'] == 0) { ?>selected<?php } ?><?php } ?>>禁用</option>
											</select>
										</div>
									</div>
									<div class="panel-footer text-left">
										<button class="btn btn-success" type="submit">提交</button>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
        		<?php } ?>
        		<?php if ($op == 'authcode') { ?>
					<div class="tab-content">
						<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'authcode') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
							<div class="panel">
								<div class="panel-body">
									<form class="form-horizontal form-padding " method="post" action="<?= $apppath ?>/orchard/config?op=authcode">
										<div class="form-group">
											<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">对接公众号ID</label>
											 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
													<input type="text" name="authCodeInfo[uniacid]" value="<?php if (isset($authCodeInfo)) { ?><?= $authCodeInfo['uniacid'] ?><?php } ?>"  class="form-control">
											</div>
										</div>
										<div class="form-group">
											<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">验证密匙（客服获取）</label>
											 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
													<input type="text" name="authCodeInfo[authkey]" value="<?php if (isset($authCodeInfo)) { ?><?= $authCodeInfo['authkey'] ?><?php } ?>"  class="form-control">
											</div>
										</div>
										<div class="form-group">
											<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">域名(http全)</label>
											 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
													<input type="text" name="authCodeInfo[httpUrl]" value="<?php if (isset($authCodeInfo)) { ?><?= $authCodeInfo['httpUrl'] ?><?php } ?>"  class="form-control">
													<input type="hidden" name="authCodeInfo[size]" value="<?php if (isset($authCodeInfo)) { ?><?php if ($authCodeInfo['size'] > 0) { ?><?= $authCodeInfo['size'] ?><?php } ?><?php if ($authCodeInfo['size'] <= 0) { ?>5<?php } ?><?php } ?>"  class="form-control">
											</div>
										</div>
										<div class="form-group">
											<div class="panel-footer text-left">
												<button class="btn btn-success" type="submit">提交</button>
											</div>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
				<?php } ?>
	</div>
