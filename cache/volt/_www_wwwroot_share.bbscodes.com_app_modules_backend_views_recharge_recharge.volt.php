
<!--文章模块开始-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="active">
				<a data-toggle="tab" href="#demo-lft-tab-1" aria-expanded="true">用户支付记录</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade  active in">
				<div class="panel">
				<div class="panel-heading">
                	<div class="panel-control">
                		<span class="label label-info">合计成功充值<?= $total_money ?>元</span>
                	</div>
                </div>
				<div class="panel-body">
					<form class="form-horizontal form-padding " method="get" action="<?= $apppath ?>/recharge/recharge">
                              <div class="form-group">
                                     <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">用户id</label>
                                     <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                    	 <input class="form-control" id="demo-vs-definput" type="text" name="uid" value="<?php if (isset($itemUser)) { ?><?= $itemUser ?><?php } ?>">
                                     </div>
                              </div>
                              <div class="form-group">
                                     <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">订单号</label>
                                     <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                         <input class="form-control" id="demo-vs-definput" type="text" name="orderNumber" <?php if (isset($itemOrder)) { ?><?= $itemOrder ?><?php } ?> >
                                     </div>
                              </div>
                              <div class="form-group">
                                     <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">支付状态</label>
                                         <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                              				<select class="form-control" id="demo-vs-definput" name="payStatus">
                              					<option value="">全部</option>
                              					<option value="1"<?php if (isset($payStatus)) { ?><?php if ($payStatus === '1') { ?>selected<?php } ?><?php } ?>>支付成功</option>
												<option value="2"<?php if (isset($payStatus)) { ?><?php if ($payStatus === '2') { ?>selected<?php } ?><?php } ?>>待支付</option>
											</select>
                                          </div>
                              </div>
                               <div class="form-group">
                                    <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">支付类型</label>
                                         <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                   			 <select class="form-control" id="demo-vs-definput" name="payType">
                                   			  <option value="">全部</option>
											  <option value="wechet"<?php if (isset($payType)) { ?><?php if ($payType === 'wechet' || $payType === 'WeChat') { ?>selected<?php } ?><?php } ?>>微信</option>
											  <option value="alipay"<?php if (isset($payType)) { ?><?php if ($payType === 'alipay') { ?>selected<?php } ?><?php } ?>>支付宝</option>
											  <option value="qq"<?php if (isset($payType)) { ?><?php if ($payType === 'qq') { ?>selected<?php } ?><?php } ?>>qq钱包</option>
											  <option value="back"<?php if (isset($payType)) { ?><?php if ($payType === 'back') { ?>selected<?php } ?><?php } ?>>后台充值</option>
											  <option value="YB"<?php if (isset($payType)) { ?><?php if ($payType === 'YB') { ?>selected<?php } ?><?php } ?>>易宝支付</option>
											</select>
                                          </div>
                               </div>
                               <div class="form-group">
									<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
									<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
												<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
									</div>
								</div>
                                 <div class="text-lg-center">
                                        <button class="btn btn-info fa fa-search" type="submit">搜索</button>

                                 </div>
                               </form>
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th>id</th>
									<th>用户</th>
									<th>订单</th>
									<th>充值数量</th>
									<th>支付类型</th>
									<th>支付时间</th>
									<th>支付状态</th>
								</tr>
								</thead>
								<tbody>
								<?php if (isset($prolist)) { ?>
									<?php foreach ($prolist['items'] as $row) { ?>
										<tr>
													<td><?= $row['id'] ?></td>
													<td><?= $row['uid'] ?></td>
													<td><?= $row['orderNumber'] ?></td>
													<td><?= $row['number'] ?></td>
													<td>
													<?php if ($row['payType'] === 'wechet') { ?>微信
													<?php } elseif ($row['payType'] === 'WeChat') { ?>微信
													<?php } elseif ($row['payType'] === 'alipay') { ?>支付宝
													<?php } elseif ($row['payType'] === 'qq') { ?>qq钱包
													<?php } elseif ($row['payType'] === 'back') { ?>后台充值
													<?php } elseif ($row['payType'] === 'YB') { ?>易宝支付
													<?php } elseif ($row['payType'] === 'alipay') { ?>支付宝支付
													<?php } ?>
													</td>
													<td><?= date('Y-m-d H:i:s', $row['createTime']) ?></td>
													<td>
													<?php if ($row['payStatus'] === '1') { ?>支付成功
													<?php } elseif ($row['payStatus'] === '2') { ?>待支付
													<?php } elseif ($row['payStatus'] === '2') { ?>支付失败
													<?php } ?>
													</td>
												</tr>
									<?php } ?>
								<?php } ?>
								</tbody>
							</table>
							<div class="bars pull-left">
                            	<a href="<?= $apppath ?>/recharge/print">
                            	<button class="btn btn-info">
                            	<i class="demo-pli-cross"></i> 全部导出
                            	</button>
                            	</a>
                            	<a href="<?= $apppath ?>/recharge/print?uid=<?= $itemUser ?>&orderNumber=<?= $itemOrder ?>&payStatus=<?= $payStatus ?>&payType=<?= $payType ?>&starttime=<?= $starttime ?>&endtime=<?= $endtime ?>">
									<button class="btn btn-info">
									<i class="demo-pli-cross"></i> 导出
									</button>
								</a>
                           </div>
                         <div class="panel-body text-center">
                                <ul class="pagination">
                                      <li><a href="<?= $apppath ?>/recharge/recharge?page=1&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php if (isset($itemUser)) { ?>&uid=<?= $itemUser ?><?php } ?><?php if (isset($itemOrder)) { ?>&itemOrder=<?= $itemOrder ?><?php } ?><?php if (isset($payType)) { ?>&payType=<?= $payType ?><?php } ?><?php if (isset($payType)) { ?>&payStatus=<?= $payStatus ?><?php } ?>" class="demo-pli-arrow-right">首页</a></li>
                                      <li><a href="<?= $apppath ?>/recharge/recharge?page=<?= $prolist['before'] ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?>&uid=<?= $itemUser ?> <?php if (isset($itemUser)) { ?>&starttime=<?= $starttime ?><?php } ?><?php if (isset($itemOrder)) { ?>&itemOrder=<?= $itemOrder ?><?php } ?><?php if (isset($payType)) { ?>&payType=<?= $payType ?><?php } ?><?php if (isset($payType)) { ?>&payStatus=<?= $payStatus ?><?php } ?>">上一页</a></li>
                                      <li><a href="<?= $apppath ?>/recharge/recharge?page=<?= $prolist['next'] ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?>&uid=<?= $itemUser ?><?php if (isset($itemUser)) { ?>&uid=<?= $itemUser ?><?php } ?><?php if (isset($itemOrder)) { ?>&itemOrder=<?= $itemOrder ?><?php } ?><?php if (isset($payType)) { ?>&payType=<?= $payType ?><?php } ?><?php if (isset($payType)) { ?>&payStatus=<?= $payStatus ?><?php } ?>">下一页</a></li>
                                      <li><a href="<?= $apppath ?>/recharge/recharge?page=<?= $prolist['total_pages'] ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?>&uid=<?= $itemUser ?><?php if (isset($itemUser)) { ?>&uid=<?= $itemUser ?><?php } ?><?php if (isset($itemOrder)) { ?>&itemOrder=<?= $itemOrder ?><?php } ?><?php if (isset($payType)) { ?>&payType=<?= $payType ?><?php } ?><?php if (isset($payType)) { ?>&payStatus=<?= $payStatus ?><?php } ?>" class="demo-pli-arrow-right">尾页</a></li>
                                </ul>
                             </div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!--文章模块结束--!>
