<!--用户列表-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="<?php if (isset($show)) { ?><?php if ($show === 'list') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a data-toggle="tab" href="#demo-lft-tab-1" aria-expanded="<?php if (isset($show)) { ?><?php if ($show === 'list') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">后台管理员列表</a>
			</li>
		</ul>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($show)) { ?><?php if ($show === 'list') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<div class="pad-btm form-inline">
							<div class="row">
								<div class="col-sm-6 table-toolbar-left">
									<button id="demo-btn-addrow" class="btn btn-purple" onclick="window.location.href='<?= $apppath ?>/jurisdiction/add'"><i class="demo-pli-add"></i> 添加用户</button>
								</div>
							</div>
						</div>
						<div class="table-responsive">
							<form method="post">
							<table class="table table-hover">
								<thead>
								<tr>
									<th>id</th>
									<th>用户名</th>
									<th>状态</th>
									<th>用户组</th>
									<th>添加时间</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<?php if (isset($userList)) { ?>
								<?php foreach ($userList as $row) { ?>
								<tr>
									<td><?= $row->id ?></td>
									<td><?= $row->user ?></td>
									<td>

										<?php if ($row->status === '1') { ?>启用<?php } ?>
										<?php if ($row->status === '0') { ?>禁用<?php } ?>

									</td>
									<td>
										 <?php if ($row->role === '1') { ?>管理员<?php } ?>
										 <?php if ($row->role === '2') { ?>操作员<?php } ?>
										 <?php if ($row->role === '3') { ?>普通会员<?php } ?>
									</td>
									<td><?php echo date('Y-m-d H:i:s',$row->createtime)?></td>
									<td>
									<button class="btn btn-success btn-labeled fa fa-refresh">
									<a href="<?= $apppath ?>/jurisdiction/update?id=<?= $row->id ?>">
									更新
									</a>
									</button>
									<button class="btn btn-warning btn-labeled fa fa-close">
									<a href="<?= $apppath ?>/jurisdiction/delete?id=<?= $row->id ?>">
									删除
									</a>
									</button>
									</td>
									</tr>
									<?php } ?>
									<?php } ?>
								</tbody>
							</table>
							</form>
							<div class="panel-body text-center">
								<ul class="pagination">
									
										
									
									
									
									
										
									
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
