
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="<?php if ($show === 'list') { ?>active<?php } else { ?> <?php } ?>">
				<a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'op', 'list') ?>">提现列表</a>
			</li>
			<li class="<?php if ($show === 'edit') { ?>active<?php } else { ?> <?php } ?>">
				<a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'op', 'edit') ?>">提现审核</a>
			</li>
			<?php if ($show === 'create') { ?>
			<li class="active">
            	<a href="#demo-lft-tab-3" aria-expanded="true">提现操作</a>
            </li>
			<?php } ?>
		</ul>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if ($show === 'list') { ?> active in <?php } ?>">
				<div class="panel">
					<div class="panel-heading">
						<h3 class="panel-title">提现列表</h3>
						<div class="panel-control">
							<span class="label label-info">合计<?= $total_money ?>金币</span>
						</div>
					</div>
					<div class="panel-body">
						<div class="bootstrap-table">
							<div class="fixed-table-toolbar"></div>
							<div class="fixed-table-container" style="padding-bottom: 0px;">
								<div class="fixed-table-body">
								<form class="form-horizontal" method='get' action="<?= $apppath ?>/userwithdraw/list?op=list">
                                       <div class="form-group">
                                        <label class="col-xs-12 col-sm-3 col-md-1 control-label">关键字</label>
                                            <div class="col-xs-12 col-sm-9 col-md-6">
                                               <input type="text" name="keywords" value="<?= $keywords ?>" placeholder="请输入UID、用户姓名，查找用户" class="form-control">
                                            </div>
                                        </div>
                                        <div class="form-group">
											<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
											<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
														<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
											</div>
										</div>
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-3 col-md-1 control-label"></label>
                                         <div class="col-xs-12 col-sm-9 col-md-6">
                                            <input type="hidden" name="op" value="list"/>
                                            <button class="btn btn-info fa fa-search" type="submit">搜索</button>
                                         </div>
                                      </div>
                                   </form>
									<form method="post" >
										<input type="hidden" id="urlPath" value="<?= $apppath ?>/userwithdraw/edit?op=edit">
									<table class="demo-add-niftycheck table table-hover" data-toggle="table" data-url="data/bs-table-simple.json" data-page-size="10" data-pagination="true">
										<thead>
										<tr>
											<th class="bs-checkbox " style="width: 36px; " data-field="state" tabindex="0">
												<div class="th-inner ">
													<input name="btSelectAll" type="checkbox" onclick="checkedAll(this)"></div>
												<div class="fht-cell"></div>
											</th>
											<th style="" data-field="id" tabindex="0">
												<div class="th-inner ">ID</div>
												<div class="fht-cell"></div>
											</th>
											<th style="" data-field="id" tabindex="0">
                                            	<div class="th-inner ">UID</div>
                                            	<div class="fht-cell"></div>
                                            </th>
											<th style="" data-field="name" tabindex="0">
												<div class="th-inner ">金额</div><div class="fht-cell"></div>
											</th>
											<th style="" data-field="date" tabindex="0">
												<div class="th-inner ">收款人帐号</div>
												<div class="fht-cell"></div>
											</th>
											<th style="" data-field="amount" tabindex="0">
												<div class="th-inner ">收款人名称</div>
												<div class="fht-cell"></div>
											</th>
											<th style="" data-field="amount" tabindex="0">
												<div class="th-inner ">收款人开户行</div>
												<div class="fht-cell"></div>
											</th>
											<th style="" data-field="amount" tabindex="0">
												<div class="th-inner ">收款人所在省</div>
												<div class="fht-cell"></div>
											</th>
											<th style="" data-field="amount" tabindex="0">
												<div class="th-inner ">收款人所在市县</div>
												<div class="fht-cell"></div>
											</th>
											<th style="" data-field="amount" tabindex="0">
												<div class="th-inner ">转账类型</div>
												<div class="fht-cell"></div>
											</th>
											<th style="" data-field="amount" tabindex="0">
												<div class="th-inner ">汇款用途</div>
												<div class="fht-cell"></div>
											</th>
											<th style="" data-field="amount" tabindex="0">
                                            	<div class="th-inner ">审核状态</div>
                                            	<div class="fht-cell"></div>
                                            </th>
                                            <th style="" data-field="amount" tabindex="0">
                                               <div class="th-inner ">提现时间</div>
                                               <div class="fht-cell"></div>
                                            </th>
                                            <?php if ($user_type == 'ceshi') { ?>
                                            <th style="" data-field="amount" tabindex="0">
											   <div class="th-inner ">凭证</div>
											   <div class="fht-cell"></div>
											</th>
											<?php } ?>
										</tr>
										</thead>
										<tbody id="childInput">
										<?php if (isset($withdraw)) { ?>
											<?php foreach ($withdraw as $rows) { ?>
												<?php if (!is_scalar($rows)) { ?>
												<?php foreach ($rows as $row) { ?>
													<?php if ($row->status === '0') { ?>
														<tr data-index="<?= $row->id ?>">
															<td class="bs-checkbox ">
																<input data-index="0" name="btSelectItem[]" type="checkbox" value="<?= $row->id ?>">
															</td>
															<td style=""><?= $row->id ?></td>
															<td style=""><?= $row->uid ?></td>
															<td style=""><?= $row->goldnumber ?></td>
															<td style=""><?= $row->accountnumber ?></td>
															<td style="">
															<?= $row->realname ?>
															</td>
															<td style=""><?= $row->bankaccount ?></td>
															<td style=""><?= $row->province ?></td>
															<td style=""><?= $row->city ?></td>
															<td style="">
																<?php if ($row->withdrawtype === '1') { ?>
																	行内转账
																<?php } elseif ($row->withdrawtype === '2') { ?>
																	同城跨行
																<?php } elseif ($row->withdrawtype === '3') { ?>
																	异地跨行
																<?php } ?>
															</td>
															<td style=""><?= $row->costname ?></td>
															<td style="">
																<?php if ($row->status == 1) { ?>已通过<?php } ?>
                                                            	<?php if ($row->status == 2) { ?>已返回<?php } ?>
                                                            	<?php if ($row->status == 3) { ?>信息有误<?php } ?>
                                                            	<?php if ($row->status == 0) { ?>审核中<?php } ?>
															</td>
															<td><?php echo date('Y-m-d H:i:s',$row->createtime)?></td>
																<?php if ($user_type == 'ceshi') { ?>
																<td>
																<?php if ($row->voucher != '') { ?><img src='<?= $row->voucher ?>' style="width:200px;height:200px;" /><?php } ?>
																</td>
																<?php } ?>
														</tr>
														<?php } ?>
													<?php } ?>
												<?php } ?>
											<?php } ?>
										<?php } ?>
										</tbody>
									</table>
										<div class="bars pull-left">
												<button class="btn btn-info">
												<a href="<?= $apppath ?>/userwithdraw/print?op=list" style="color:#fff">
                                        			<i class="demo-pli-cross"></i> 批量导出
                                        		</a></button>
                                        		<button class="btn btn-info">
												<a href="<?= $apppath ?>/userwithdraw/print?op=list&keywords=<?= $keywords ?>&starttime=<?= $starttime ?>&endtime=<?= $endtime ?>" style="color:#fff">
													<i class="demo-pli-cross"></i> 导出
												</a></button>
											<button id="demo-delete-row"  type="button"  onclick="updateData()" class="btn btn-info"><i class="demo-pli-cross"></i>批量审核</button>
										</div>
									</form>
								</div>
								<div class="panel-body text-center">
									<ul class="pagination">
										<li ><a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'page', 1) ?>">首页</a></li>
										<?php if ($withdraw->current != 1) { ?>
											<li><a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'page', $withdraw->before) ?>">上一页</a></li>
										<?php } ?>

										<?php
										$pageStart = max(1, $withdraw->current - 5);
										if ($pageStart == 1) {
											$pageEnd = min($withdraw->current + 10, $withdraw->last);
										} else {
											$pageEnd = min($withdraw->current + 5, $withdraw->last);
										}
										if ($pageEnd == $withdraw->last) {
											$pageStart = max(1, $withdraw->current - 10);
										}
										for ($i = $pageStart; $i <= $pageEnd; $i++): ?>
										<li class="<?php if ($withdraw->current == $i) { ?>active<?php } ?>"><a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'page', $i) ?>"><?= $i ?></a></li>
										<?php endfor; ?>

										<?php if ($withdraw->current != $withdraw->last) { ?>
										<li><a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'page', $withdraw->next) ?>">下一页</a></li>
										<?php } ?>
										<li><a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'page', $withdraw->last) ?>">尾页</a></li>
									</ul>
								</div>
							</div>
							<?php
								function addQueryStringVar($url, $key, $value)
								{
									$url = preg_replace('/(.*)(?|&)' . $key . '=[^&]+?(&)(.*)/i', '$1$2$4', $url . '&');
									$url = substr($url, 0, -1);
									if (strpos($url, '?') === false) {
										return ($url . '?' . $key . '=' . $value);
									} else {
										return ($url . '&' . $key . '=' . $value);
									}
								}
                            ?>
						</div>
						<div class="clearfix"></div>
					</div>	</div>
			</div>
			<?php if ($show == 'create') { ?>
			<div id="demo-lft-tab-3" class="tab-pane fade active in">
			 <div id="demo-lft-tab-2" class="tab-pane fade <?php if ($show === 'edit') { ?> active in <?php } ?>">
                                <div class="panel">
                                    <form class="panel-body form-horizontal form-padding " method="post"  action="<?= $apppath ?>/article/article/list">
                                        <!--Static-->
                                        <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">用户id*</label>
                                              <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                  <input type="text" class="form-control" name="uid" value="<?php if (isset($item->uid)) { ?><?= $item->uid ?><?php } ?>">
                                              </div>
                                         </div>
                                          <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">收款开户行</label>
                                              <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                  <input type="text" class="form-control" name="bankaccount" value="<?php if (isset($item->bankaccount)) { ?><?= $item->bankaccount ?><?php } ?>">
                                              </div>
                                          </div>
                                        <div class="form-group">
                                            <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">银行帐号*</label>
            								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
            									<input type="text" class="form-control" name="accountnumber" value="<?php if (isset($item->accountnumber)) { ?><?= $item->accountnumber ?><?php } ?>">
            								</div>
                                        </div>
            							<div class="form-group">
                                           <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">提现金币数量</label>
                                           <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                               <input type="text" class="form-control" name="goldnumber" value="<?php if (isset($item->goldnumber)) { ?><?= $item->goldnumber ?><?php } ?>">
                                           </div>
                                        </div>
                                        <div class="form-group">
                                           <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">手续费</label>
                                           <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                               <input type="text" class="form-control" name="fee" value="<?php if (isset($item->fee)) { ?><?= $item->fee ?><?php } ?>">
                                           </div>
                                        </div>
                                        <div class="form-group">
                                                 <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">省份/直辖市</label>
                                                <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                <input type="text" class="form-control" name="province" value="<?php if (isset($item->province)) { ?><?= $item->province ?><?php } ?>">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                             <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">市/县</label>
                                             <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                              <input type="text" class="form-control" name="city" value="<?php if (isset($item->city)) { ?><?= $item->city ?><?php } ?>">
                                             </div>
                                        </div>
                                         <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">消费类型</label>
                                              <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                 <input type="text" class="form-control" name="costname" value="<?php if (isset($item->costname)) { ?><?= $item->costname ?><?php } ?>">
                                              </div>
                                         </div>
                                         <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">提现用户</label>
                                                  <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                  <input type="text" class="form-control" name="realname" value="<?php if (isset($item->realname)) { ?><?= $item->realname ?><?php } ?>">
                                             </div>
                                         </div>
                                        <div class="form-group">
                                                 <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">提现类型</label>
                                                   <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                   <select class="form-control" id="demo-vs-definput" name="withdrawtype">
                                                    <option value="3" class="<?php if ($item->withdrawtype === 1) { ?>selected<?php } ?> ">行内转账</option>
                                                    <option value="4"class="<?php if ($item->withdrawtype === 2) { ?>selected<?php } ?> ">同城跨行</option>
                                                    <option value="4"class="<?php if ($item->withdrawtype === 3) { ?>selected<?php } ?> ">异地跨行</option>
                                                   </select>
                                              </div>
                                        </div>
                                          <div class="form-group">
                                              <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
                                               <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                                 <select class="form-control" id="demo-vs-definput" name="status">
                                                    <option value="3" class="<?php if ($item->id === 1) { ?>selected<?php } ?> ">审核通过</option>
                                                    <option value="4"class="<?php if ($item->id === 0) { ?>selected<?php } ?> ">审核中</option>
                                                    <option value="5"class="<?php if ($item->id === 2) { ?>selected<?php } ?> ">已完成</option>
                                                    <option value="5"class="<?php if ($item->id === 3) { ?>selected<?php } ?> ">信息有误</option>
                                                 </select>
                                              </div>
                                          </div>

                                    </form>
                                </div>
                            </div>
			</div>
			<?php } ?>
			<div id="demo-lft-tab-2" class="tab-pane fade <?php if ($show === 'edit') { ?> active in <?php } ?>">
				<div class="panel">
					<div class="panel-heading">
						<h3 class="panel-title">提现列表</h3>
						<div class="panel-control">
							<span class="label label-info">合计<?= $total_money3 ?>金币</span>
						</div>
					</div>
					<div class="panel-body">
					<form class="form-horizontal" action="<?= $apppath ?>/userwithdraw/list?op=edit">
                           <div class="form-group">
                            <label class="col-xs-12 col-sm-3 col-md-1 control-label">关键字</label>
								<div class="col-xs-12 col-sm-9 col-md-6">
									<input type="text" name="keywords" value="<?= $keywords ?>" placeholder="请输入UID、用户姓名，查找用户" class="form-control">
								</div>
                            </div>
                            <div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
											<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
								</div>
							</div>
                            <div class="form-group">
                              <label class="col-xs-12 col-sm-3 col-md-1 control-label"></label>
                           <div class="col-xs-12 col-sm-9 col-md-6">
                             <input type="hidden" name="op" value="edit"/>
                             <button class="btn btn-info fa fa-search" type="submit">搜索</button>
                           </div>
                      </div>
                   </form>
						<div class="bootstrap-table">
							<div class="fixed-table-toolbar"></div>
							<div class="fixed-table-container" style="padding-bottom: 0px;">
								<div class="fixed-table-body">

										<input type="hidden" value="<?= $apppath ?>/userwithdraw/updata" id="urlPathPass">
										<table class="demo-add-niftycheck table table-hover" data-toggle="table" data-url="data/bs-table-simple.json" data-page-size="10" data-pagination="true">
											<thead>
											<tr>
												<th class="bs-checkbox " style="width: 36px; " data-field="state" tabindex="0">
													<div class="th-inner ">
														<input name="btSelectAll" type="checkbox" onclick="checkedAllPass(this)"></div>
													<div class="fht-cell"></div>
												</th>
												<th style="" data-field="id"  tabindex="0">
													<div class="th-inner ">ID</div>
													<div class="fht-cell"></div>
												</th>
												<th style="" data-field="id"  tabindex="0">
                                                	<div class="th-inner ">UID</div>
                                                	<div class="fht-cell"></div>
                                                </th>
												<th style="" data-field="name" tabindex="0">
													<div class="th-inner ">金额</div><div class="fht-cell"></div>
												</th>
												<th style="" data-field="date" tabindex="0">
													<div class="th-inner ">收款人帐号</div>
													<div class="fht-cell"></div>
												</th>
												<th style="" data-field="amount" tabindex="0">
													<div class="th-inner ">收款人名称</div>
													<div class="fht-cell"></div>
												</th>
												<th style="" data-field="amount" tabindex="0">
													<div class="th-inner ">收款人开户行</div>
													<div class="fht-cell"></div>
												</th>
												<th style="" data-field="amount" tabindex="0">
													<div class="th-inner ">收款人所在省</div>
													<div class="fht-cell"></div>
												</th>
												<th style="" data-field="amount" tabindex="0">
													<div class="th-inner ">收款人所在市县</div>
													<div class="fht-cell"></div>
												</th>
												<th style="" data-field="amount" tabindex="0">
													<div class="th-inner ">转账类型</div>
													<div class="fht-cell"></div>
												</th>
												<th style="" data-field="amount" tabindex="0">
													<div class="th-inner ">汇款用途</div>
													<div class="fht-cell"></div>
												</th>
												<th style="" data-field="amount" tabindex="0">
                                                	<div class="th-inner ">审核状态</div>
                                                	<div class="fht-cell"></div>
                                                </th>
                                                <th style="" data-field="amount" tabindex="0">
                                                    <div class="th-inner ">提现时间</div>
                                                    <div class="fht-cell"></div>
                                                </th>
                                                 <?php if ($user_type == 'ceshi') { ?>
												<th style="" data-field="amount" tabindex="0">
												   <div class="th-inner ">凭证</div>
												   <div class="fht-cell"></div>
												</th>
												<?php } ?>
                                                <th style="" data-field="amount" tabindex="0">
                                                    <div class="th-inner ">操作</div>
                                                    <div class="fht-cell"></div>
                                                </th>

											</tr>
											</thead>
											<tbody id="inputCheckPass">
											<?php if (isset($withdraws)) { ?>
												<?php foreach ($withdraws as $items) { ?>
													<?php if (!is_scalar($items)) { ?>
													<?php foreach ($items as $item) { ?>
													<tr data-index="<?= $row->id ?>">
														<td class="bs-checkbox ">
															<input data-index="0" name="btSelectItem" type="checkbox" value="<?= $item->id ?>">
															
														</td>
														<td style=""><?= $item->id ?></td>
														<td style=""><?= $item->uid ?></td>
														<td style=""><?= $item->goldnumber ?></td>
														<td style=""><?= $item->accountnumber ?></td>
														<td style="">

															<?= $item->realname ?>
                                                        </td>
														<td style=""><?= $item->bankaccount ?></td>
														<td style=""><?= $item->province ?></td>
														<td style=""><?= $item->city ?></td>
														<td style="">
															<?php if ($item->withdrawtype === '1') { ?>
																行内转账
															<?php } elseif ($item->withdrawtype === '2') { ?>
																同城跨行
															<?php } elseif ($item->withdrawtype === '3') { ?>
																异地跨行
															<?php } ?>
														</td>
														<td style=""><?= $item->costname ?></td>
														<td style="">
														<?php if ($item->status == 1) { ?>已通过<?php } ?>
														<?php if ($item->status == 2) { ?>已返回<?php } ?>
														<?php if ($item->status == 3) { ?>信息有误<?php } ?>
														<?php if ($item->status == 0) { ?>审核中<?php } ?>
														</td>
														<td><?php echo date('Y-m-d H:i:s',$item->createtime)?></td>
														<?php if ($user_type == 'ceshi') { ?>
                                                        <td>
														<?php if ($item->voucher != '') { ?><img src='<?= $item->voucher ?>' style="width:200px;height:200px;" /><?php } ?>
														</td>
														<?php } ?>
														<td>
                                                        	<a href="<?= $apppath ?>/userwithdraw/create?id=<?= $item->id ?>">
                                                             <button class="btn btn-warning btn-labeled fa fa-edit">编辑</button>
                                                           </a>
                                                        </td>
													</tr>

													<?php } ?>
													<?php } ?>
												<?php } ?>
											<?php } ?>
											</tbody>
										</table>
									<div class="bars pull-left">
											<a href="<?= $apppath ?>/userwithdraw/print">
											<button class="btn btn-info">
											<i class="demo-pli-cross"></i> 批量导出
											</button>
											</a>
											<a href="<?= $apppath ?>/userwithdraw/print?op=status&keywords=<?= $keywords ?>&starttime=<?= $starttime ?>&endtime=<?= $endtime ?>" style="color:#fff">
											<button class="btn btn-info">
												<i class="demo-pli-cross"></i> 导出
											</button>
											</a>
											<button id="demo-delete-row" type="button" class="btn btn-info" onclick="updateDataPass()"><i class="demo-pli-cross"></i> 批量完成
											</button>
											<button id="demo-delete-row" type="button" class="btn btn-info" onclick="restorder()"><i class="demo-pli-cross"></i> 订单退回
											</button>
									</div>
								</div>
								<div class="panel-body text-center">
									<ul class="pagination">
										<li ><a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'page', 1) ?>">首页</a></li>
										<?php if ($withdraw->current != 1) { ?>
											<li><a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'page', $withdraw->before) ?>">上一页</a></li>
										<?php } ?>

										<?php
										$pageStart = max(1, $withdraw->current - 5);
										if ($pageStart == 1) {
											$pageEnd = min($withdraw->current + 10, $withdraw->last);
										} else {
											$pageEnd = min($withdraw->current + 5, $withdraw->last);
										}
										if ($pageEnd == $withdraw->last) {
											$pageStart = max(1, $withdraw->current - 10);
										}
										for ($i = $pageStart; $i <= $pageEnd; $i++): ?>
										<li class="<?php if ($withdraw->current == $i) { ?>active<?php } ?>"><a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'page', $i) ?>"><?= $i ?></a></li>
										<?php endfor; ?>

										<?php if ($withdraw->current != $withdraw->last) { ?>
										<li><a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'page', $withdraw->next) ?>">下一页</a></li>
										<?php } ?>
										<li><a href="<?= addQueryStringVar($_SERVER["REQUEST_URI"], 'page', $withdraw->last) ?>">尾页</a></li>
									</ul>
								</div>
							</div>
						</div>
						<div class="clearfix"></div>
					</div>
				</div>
				</div>
			</div>

		</div>
	</div>
</div>
<script>
//批量审核
function checkedAll(e){
	var check=$(e).prop("checked");
	var inputs=$("#childInput").find("input");
	if(check){
		inputs.each(function(){
			$(this).prop("checked",true);
		});

	}else{
		inputs.each(function(){
			$(this).prop("checked",false);
		});
	}
};
function updateData(){
	var data=[];
	var jump=$("#urlPath").val();
	var inputs=$("#childInput").find("input");
	inputs.each(function(){
		if($(this).prop("checked")){
			data.push($(this).val());
		}
	});
	$.ajax({
		type: "post",
		url: jump,
		data: {"name":data},
		success: function(data) {
			data = JSON.parse(data);
			if(data.code==0){
				alert(data.msg);
				window.location.href = "<?= $apppath ?>/userwithdraw/list?op=list";
			}else{
				alert(data.msg);
			}
		}
	})

}
//批量通过
function checkedAllPass(e){
	var check=$(e).prop("checked");
	var inputs=$("#inputCheckPass").find("input");
	if(check){
		inputs.each(function(){
			$(this).prop("checked",true);
		});
	}else{
		inputs.each(function(){
			$(this).prop("checked",false);
		});
	}
};
function updateDataPass(){
	var data=[];
	var jump=$("#urlPathPass").val();
	var inputs=$("#inputCheckPass").find("input");
	inputs.each(function(){
		if($(this).prop("checked")){
			data.push($(this).val());
		}
	});
	$.ajax({
		type: "post",
		url: jump,
		data: {"name":data},
		success: function(data) {
			data = JSON.parse(data);
			if(data.code==0){
				alert(data.msg);
				window.location.href = "<?= $apppath ?>/userwithdraw/list?op=list";
			}else{
				alert(data.msg);
			}
		}
	})

}
//订单撤回
function restorder(){
	var data=[];
	var jump=$("#urlPathPass").val();
	var inputs=$("#inputCheckPass").find("input");
	inputs.each(function(){
		if($(this).prop("checked")){
			data.push($(this).val());
		}
	});
	$.ajax({
		type: "post",
		url: "<?= $apppath ?>/userwithdraw/rest",
		data: {"name":data},
		success: function(data) {
			data = JSON.parse(data);
			if(data.code==0){
				alert(data.msg);
				window.location.href = "<?= $apppath ?>/userwithdraw/list?op=list";
			}else{
				alert(data.msg);
			}
		}
	})

}


//订单打印




</script>

<!-- 提现模块结束--!>
