<!--用户列表-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="<?php if ($op === 'list') { ?>active<?php } ?>"><a href="?op=list">用户列表</a></li>
			<li class="<?php if ($op === 'edit') { ?>active<?php } ?>"><a href="?op=edit"><?php if (empty($id)) { ?>添加<?php } else { ?>编辑<?php } ?>用户</a></li>
			<?php if ($op === 'recharge') { ?><li class="active"><a href="javascript:void(0)">金币充值</a></li><?php } ?>
			<?php if ($op === 'logs') { ?><li class="active"><a href="javascript:void(0)">金币记录</a></li><?php } ?>
			<li class="<?php if ($op === 'manage') { ?>active<?php } ?>"><a href="?op=manage">用户挂单</a></li>
			<li class="<?php if ($op === 'channel') { ?>active<?php } ?>"><a href="?op=channel">渠道代理</a></li>
			<?php if ($op === 'performance') { ?><li class="active"><a href="javascript:void(0)">团队佣金</a></li><?php } ?>
			<?php if ($op === 'channels') { ?><li class="active"><a href="javascript:void(0)"><?php if ($type == 'common') { ?>推广奖励<?php } elseif ($type == 'channel') { ?>个人业绩<?php } ?></a></li><?php } ?>
			<?php if ($op === 'channellist') { ?><li class="active"><a href="javascript:void(0)">下级列表</a></li><?php } ?>
			<li class="<?php if ($op === 'salesman') { ?>active<?php } ?>"><a href="?op=salesman">业务员管理</a></li>
		</ul>
		<div class="tab-content">
			<?php if ($op === 'list') { ?>
			<div id="demo-lft-tab-1" class="tab-pane fade active in">
				<div class="panel">
					<div class="panel-heading">
						<div class="panel-control">
							<span class="label label-info">合计<?= $page->total_items ?>人 - 金币共计￥<?= $all_price ?> <?php if ($user_type == 'jindao') { ?>- 在线<?= $online ?>人<?php } ?></span>
						</div>
						<h4 class="panel-title">筛选</h4>
					</div>
					<div class="panel-body">
						<form class="form-horizontal">
							<div class="form-group">
								<label class="col-xs-12 col-sm-3 col-md-1 control-label">关键字</label>
								<div class="col-xs-12 col-sm-9 col-md-6">
									<input type="text" name="keyword" value="<?= $keyword ?>" placeholder="请输入UID、用户名、昵称，查找用户" class="form-control">
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
											<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-3 col-md-1 control-label"></label>
								<div class="col-xs-12 col-sm-9 col-md-6">
									<input type="hidden" name="op" value="list"/>
									<button class="btn btn-info fa fa-search" type="submit">搜索</button>
								</div>
							</div>
						</form>
					</div>
				</div>
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th>UID</th>
									<th>用户名</th>
									<th>状态</th>
									<th>昵称</th>
									<th>推荐人id</th>
									<th>用户组</th>
									<th>金币</th>
									<th>冻结金币</th>
									<th>金币总额</th>
									<th>上次登录</th>
									<th>注册时间</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
									<?php foreach ($page->items as $row) { ?>
										<tr>
											<td><?= $row->id ?></td>
											<td><?= $row->user ?></td>
											<td>
												<?php if ($row->status === '1') { ?>正常
												<?php } elseif ($row->status === '9') { ?>禁用
												<?php } else { ?>异常
												<?php } ?>
											</td>
											<td>
												<?php if (isset($row->nickname)) { ?>
													<?= $row->nickname ?>
												<?php } else { ?>
													暂未设置
												<?php } ?>
											</td>
											<td>
											<?php if ($row->superior > 0) { ?>
												<?= $row->superior ?>
											<?php } else { ?>---
											<?php } ?>
											</td>
											<td><?= $row->usergroup ?></td>
											<td><?= $row->coing ?></td>
											<td><?= $row->Frozen ?></td>
											<td><?= $row->coing + $row->Frozen ?></td>
											<td><?php if (!empty($row->lasttime)) { ?><?= date('Y-m-d H:i:s', $row->lasttime) ?><?php } else { ?>暂未登陆<?php } ?></td>
											<td><?= date('Y-m-d H:i:s', $row->createTime) ?></td>
											<td>
												<a href="?op=edit&id=<?= $row->id ?>">
													<button class="btn btn-warning btn-labeled fa fa-edit">编辑</button>
												</a>
												
												<a href="?op=manage&user=<?= $row->id ?>&status=1">
													<button class="btn btn-success btn-labeled fa fa-money">挂单记录</button>
												</a>
												<a href="?op=logs&uid=<?= $row->id ?>">
													<button class="btn btn-success btn-labeled fa fa-money">金币记录</button>
												</a>
												<a href="?op=channels&uid=<?= $row->id ?>">
                                                	<button class="btn btn-success btn-labeled fa fa-money">推广奖励</button>
                                                </a>
                                                <a href="?op=channellist&uid=<?= $row->id ?>">
                                                    <button class="btn btn-success btn-labeled fa fa-money">推广列表</button>
                                                </a>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<div class="panel-body text-center">
								<ul class="pagination">
									<li ><a href="?op=list&page=1&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php if (isset($keyword)) { ?>?keyword=<?= $keyword ?><?php } ?>">首页</a></li>
									<?php if ($page->current != 1) { ?>
										<li><a href="?op=list&page=<?= $page->before ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">上一页</a></li>
									<?php } ?>

									<?php
									$pageStart = max(1, $page->current - 5);
									if ($pageStart == 1) {
										$pageEnd = min($page->current + 10, $page->last);
									} else {
										$pageEnd = min($page->current + 5, $page->last);
									}
									if ($pageEnd == $page->last) {
										$pageStart = max(1, $page->current - 10);
									}
									for ($i = $pageStart; $i <= $pageEnd; $i++): ?>
									<li class="<?php if ($page->current == $i) { ?>active<?php } ?>"><a href="?op=list&page=<?= $i ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>"><?= $i ?></a></li>
									<?php endfor; ?>

									<?php if ($page->current != $page->last) { ?>
									<li><a href="?op=list&page=<?= $page->next ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">下一页</a></li>
									<?php } ?>
									<li><a href="?op=list&page=<?= $page->last ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">尾页</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
			<?php } elseif ($op == 'edit') { ?>
			<div id="demo-lft-tab-2" class="tab-pane fade active in">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">用户帐号</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" type="text" name="user" value="<?php if (isset($item)) { ?><?= $item->user ?><?php } ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">用户昵称</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" type="text" name="nickname" value="<?php if (isset($item)) { ?><?= $item->nickname ?><?php } ?>">
								</div>
							</div>
							<div class="form-group">
                            	<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">用户真实姓名</label>
                            	<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                            		<input class="form-control" type="text" name="realname" value="<?php if (isset($item)) { ?><?= $item->realname ?><?php } ?>">
                            	</div>
                            </div>
                            <div class="form-group">
                                 <label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">用户身份证号</label>
                                 <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                     <input class="form-control" type="text" name="idcard" value="<?php if (isset($item)) { ?><?= $item->idcard ?><?php } ?>">
                                 </div>
                            </div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">设置密码</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<div class="input-group">
										<input class="form-control" type="text" name="password" value="" placeholder="重置密码使用，不需要请勿填写">
										<?php if ($userType == 'chuangjin') { ?>
										<span class="input-group-addon" onclick="loginUrl();">网站登录</span>
										<span class="form-control"  onclick="lookPassword('<?= $item->repassword ?>');" id="idpassword">查看密码</span>
										<?php } ?>
									</div>
								</div>
							</div>
							<div class="form-group">
                            	<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">作者认证等级</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<select name="authLevel" class="form-control">
										<option value="0" <?php if (isset($item)) { ?><?php if ($item->authLevel === '0') { ?>selected<?php } ?><?php } ?>>普通作者</option>
										<option value="1" <?php if (isset($item)) { ?><?php if ($item->authLevel === '1') { ?>selected<?php } ?><?php } ?>>银牌作者</option>
										<option value="2" <?php if (isset($item)) { ?><?php if ($item->authLevel === '2') { ?>selected<?php } ?><?php } ?>>金牌作者</option>
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">业务员佣金</label>
								<div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
									<div class="input-group">
										<span class="input-group-addon">一级农场</span>
										<input type="text" name="salesmanRebate[1][1]" value="<?php if (isset($item->salesmanRebate['1']['1'])) { ?><?= $item->salesmanRebate['1']['1'] ?><?php } else { ?>0<?php } ?>" class="form-control">
										<span class="input-group-addon">%</span>
									</div>
									<div class="input-group">
										<span class="input-group-addon">二级农场</span>
										<input type="text" name="salesmanRebate[1][2]" value="<?php if (isset($item->salesmanRebate['1']['2'])) { ?><?= $item->salesmanRebate['1']['2'] ?><?php } else { ?>0<?php } ?>" class="form-control">
										<span class="input-group-addon">%</span>
									</div>
									<div class="input-group">
										<span class="input-group-addon">一级庄园</span>
										<input type="text" name="salesmanRebate[2][1]" value="<?php if (isset($item->salesmanRebate['2']['1'])) { ?><?= $item->salesmanRebate['2']['1'] ?><?php } else { ?>0<?php } ?>" class="form-control">
										<span class="input-group-addon">%</span>
									</div>
									<div class="input-group">
										<span class="input-group-addon">二级庄园</span>
										<input type="text" name="salesmanRebate[2][2]" value="<?php if (isset($item->salesmanRebate['2']['2'])) { ?><?= $item->salesmanRebate['2']['2'] ?><?php } else { ?>0<?php } ?>" class="form-control">
										<span class="input-group-addon">%</span>
									</div>
									<span class="help-block" style="color: red;">如果不需要请保持空或0， 否则会覆盖后台设置的全局比例</span>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">渠道代理</label>
								<div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
									<div class="input-group">
										<input class="form-control" type="text" name="channelRebate" value="<?php if (isset($item)) { ?><?= $item->channelRebate ?><?php } else { ?>0<?php } ?>" placeholder="设置渠道代理提成比例">
										<span class="input-group-addon" id="basic-addon2">%</span>
									</div>
									<span class="help-block" style="color: red;">如果不是渠道代理，请保持空</span>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">上级UID</label>
								<div class="col-xs-12 col-sm-6 col-md-4 col-lg-3">
									<input class="form-control" type="text" name="superiorUid" value="<?php if (isset($item)) { ?><?= $item->superiorUid ?><?php } else { ?>0<?php } ?>" placeholder="设置上级UID">
									<span class="help-block" style="color: red;">如果输入的上级UID是当前用户的下级，需要先调整此UID不是当前用户下级，然后再设置，否则祖母悖论</span>
								</div>
							</div>
							
								
								
									
								
							
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">用户组</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<select name="usergroup" class="form-control">
										<option value="代理机构" <?php if (isset($item)) { ?><?php if ($item->usergroup === '代理机构') { ?>selected<?php } ?><?php } ?>>代理机构</option>
										<option value="普通用户" <?php if (isset($item)) { ?><?php if ($item->usergroup === '普通用户') { ?>selected<?php } ?><?php } ?>>普通用户</option>
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<select name="status" class="form-control">
										<option value="1" <?php if (isset($item)) { ?><?php if ($item->status === '1') { ?>selected<?php } ?><?php } ?>>启用</option>
										<option value="9" <?php if (isset($item)) { ?><?php if ($item->status === '9') { ?>selected<?php } ?><?php } ?>>禁用</option>
									</select>
								</div>
							</div>
							<div class="panel-footer text-left">
								<input type="hidden" name='id' value="<?php if (isset($item)) { ?><?= $item->id ?><?php } ?>">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
			<script>
				function lookPassword(password){
					var pass =  $("input[name=password]").val();
					if(pass == ""){
						$("input[name=password]").val(password);
						$("#idpassword").html("隐藏密码");
					}else{
						$("input[name=password]").val("");
						$("#idpassword").html("查看密码");
					}
				}
				function loginUrl(){
					$.ajax({
						type: "post",
						url: "",
						data: {"isAjax":"1"},
						success: function(data) {
							data = JSON.parse(data);
							if(data.code==0){
								alert(data.msg);
								window.open(data.data);
							}else{
								alert(data.msg);
							}
						}
					})
				}
			</script>
			<?php } elseif ($op === 'manage') { ?>
			<div id="demo-lft-tab-3" class="tab-pane fade active in">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="get">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">用户帐号/ID</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" type="text" name="user" value="<?php if (isset($uid)) { ?><?= $uid ?><?php } ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">产品id</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" type="text" name="sid" value="<?php if (isset($sid)) { ?><?= $sid ?><?php } ?>">
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索条件</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<label class="radio-inline">
										<input type="radio" name="status" value="1" <?php if ($status == 1) { ?>checked<?php } ?>/>已完成
									</label>
									<label class="radio-inline">
										<input type="radio" name="status" value="0" <?php if ($status == 0) { ?>checked<?php } ?>/>未完成
									</label>
								</div>
							</div>
							<div class="text-lg-center">
								<input type="hidden" name="op" value="manage"/>
								<button class="btn btn-info fa fa-search" type="submit">搜索</button>
							</div>
						</form>
					</div>
					<div class="table-responsive">
						<table class="table table-hover">
							<thead>
							<tr>
								<th>挂单用户id</th>
								<th>产品id</th>
								<th>产品名称</th>
								<th>挂单数量</th>
								<th>挂单价格</th>
								<th>挂单时间</th>
								<th>挂单类型</th>
								<th>结束时间</th>
								<th>成交数量</th>
								<th>交易用户id</th>
							</tr>
							</thead>
							<tbody>
							<?php foreach ($page->items as $row) { ?>
								<tr>
									<td><?= $row->uid ?></td>
									<td><?= $row->sid ?></td>
									<td><?= $row->goods ?></td>
									<td><?= $row->number ?></td>
									<td><?= $row->price ?></td>
									<td>

										<?php echo date('Y-m-d H:i:s',$row->createtime)?>

									</td>
									<td>
										 <span class="label label-table label-success">
											 <?php if ($row->type === '1') { ?>收购
											 <?php } elseif ($row->type === '0') { ?>出售
											 <?php } elseif ($row->type === '2') { ?>撤销
											 <?php } ?>
										 </span>
									</td>
									<td>
										<?php if ($row->endtime != 0) { ?>
										<?php echo date('Y-m-d H:i:s',$row->endtime)?>
										<?php } ?>
									</td>
									<td><?= $row->dealnum ?></td>
									<td><?= $row->bid ?></td>
								</tr>
							<?php } ?>
							</tbody>
						</table>
						<div class="panel-body text-center">
							<ul class="pagination">
								<li ><a href="?op=manage&page=1<?php if (isset($keyword)) { ?>?keyword=<?= $keyword ?><?php } ?>">首页</a></li>
								<?php if ($page->current != 1) { ?>
									<li><a href="?op=manage&page=<?= $page->before ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">上一页</a></li>
								<?php } ?>

								<?php
								$pageStart = max(1, $page->current - 5);
								if ($pageStart == 1) {
									$pageEnd = min($page->current + 10, $page->last);
								} else {
									$pageEnd = min($page->current + 5, $page->last);
								}
								if ($pageEnd == $page->last) {
									$pageStart = max(1, $page->current - 10);
								}
								for ($i = $pageStart; $i <= $pageEnd; $i++): ?>
								<li class="<?php if ($page->current == $i) { ?>active<?php } ?>"><a href="?op=manage&page=<?= $i ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>"><?= $i ?></a></li>
								<?php endfor; ?>

								<?php if ($page->current != $page->last) { ?>
								<li><a href="?op=manage&page=<?= $page->next ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">下一页</a></li>
								<?php } ?>
								<li><a href="?op=manage&page=<?= $page->last ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">尾页</a></li>
							</ul>
						</div>
					</div>
				</div>
			</div>
			<?php } elseif ($op === 'recharge') { ?>
			<div id="demo-lft-tab-4" class="tab-pane fade active in">
				<div class="panel">
					<div class="panel-body">
						<form class="form-horizontal form-padding " method="post" action="?recharge">
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label" >id</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" type="text" name="id" value="<?php if (isset($item)) { ?><?= $item->id ?><?php } ?>"  >
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">用户帐号</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" type="text" name="user"  value="<?php if (isset($item)) { ?><?= $item->user ?><?php } ?>">
								</div>
							</div>
							
							<div class="form-group">
								<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">金币充值</label>
								<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<input class="form-control" type="text" name="rechargecoing" value="0">
								</div>
							</div>
							<div class="panel-footer text-left">
								<input type="hidden" name="op" value="recharge"/>

								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</form>
					</div>
				</div>
			</div>
			<?php } elseif ($op === 'logs') { ?>
			<div id="demo-lft-tab-1" class="tab-pane fade active in">
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th>UID</th>
									<th>操作人UID</th>
									<th>充值金额</th>
									<th>冻结金额</th>
									<th>操作时间</th>
								</tr>
								</thead>
								<tbody>
								<?php foreach ($page->items as $row) { ?>
									<tr>
										<td><?= $row->uid ?></td>
										<td><?= $row->oid ?></td>
										<td><?= $row->gold ?></td>
										<td><?= $row->frozen ?></td>
										<td><?= date('Y-m-d H:i:s', $row->createtime) ?></td>
									</tr>
								<?php } ?>
								</tbody>
							</table>
							<div class="panel-body text-center">
								<ul class="pagination">
									<li ><a href="?op=logs&page=1<?php if (isset($keyword)) { ?>?keyword=<?= $keyword ?><?php } ?>">首页</a></li>
									<?php if ($page->current != 1) { ?>
										<li><a href="?op=logs&page=<?= $page->before ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">上一页</a></li>
									<?php } ?>

									<?php
									$pageStart = max(1, $page->current - 5);
									if ($pageStart == 1) {
										$pageEnd = min($page->current + 10, $page->last);
									} else {
										$pageEnd = min($page->current + 5, $page->last);
									}
									if ($pageEnd == $page->last) {
										$pageStart = max(1, $page->current - 10);
									}
									for ($i = $pageStart; $i <= $pageEnd; $i++): ?>
									<li class="<?php if ($page->current == $i) { ?>active<?php } ?>"><a href="?op=logs&page=<?= $i ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>"><?= $i ?></a></li>
									<?php endfor; ?>

									<?php if ($page->current != $page->last) { ?>
									<li><a href="?op=logs&page=<?= $page->next ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">下一页</a></li>
									<?php } ?>
									<li><a href="?op=logs&page=<?= $page->last ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">尾页</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
			<?php } elseif ($op === 'channel') { ?>
			<div id="demo-lft-tab-1" class="tab-pane fade active in">
				<div class="panel">
					<div class="panel-heading">
						<div class="panel-control">
							<span class="label label-info">合计<?= $page->total_items ?>人</span>
						</div>
						<h4 class="panel-title">筛选</h4>
					</div>
					<div class="panel-body">
						<form class="form-horizontal">
							<div class="form-group">
								<label class="col-xs-12 col-sm-3 col-md-1 control-label">关键字</label>
								<div class="col-xs-12 col-sm-9 col-md-6">
									<input type="text" name="keyword" value="<?= $keyword ?>" placeholder="请输入UID、用户名、昵称，查找用户" class="form-control">
								</div>
							</div>

							<div class="form-group">
								<label class="col-xs-12 col-sm-3 col-md-1 control-label"></label>
								<div class="col-xs-12 col-sm-9 col-md-6">
									<input type="hidden" name="op" value="channel"/>
									<button class="btn btn-info fa fa-search" type="submit">搜索</button>
								</div>
							</div>
						</form>
					</div>
				</div>
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th>UID</th>
									<th>账号</th>
									<th>昵称</th>
									<th>用户组</th>
									<th>金币</th>
									<th>冻结金币</th>
									<th>金币总额</th>
									<th>渠道佣金比例</th>
									<th>注册时间</th>
									<th>状态</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<?php foreach ($page->items as $row) { ?>
									<tr>
										<td><?= $row->id ?></td>
										<td><?= $row->user ?></td>
										<td>
											<?php if (isset($row->nickname)) { ?>
												<?= $row->nickname ?>
											<?php } else { ?>
												暂未设置
											<?php } ?>
										</td>
										<td><?= $row->usergroup ?></td>
										<td><?= sprintf('%.4f', $row->coing) ?></td>
										<td><?= sprintf('%.4f', $row->Frozen) ?></td>
										<td><?= sprintf('%.4f', $row->coing + $row->Frozen) ?></td>
										<td><?= $row->channelRebate ?>%</td>
										<td><?= date('Y-m-d H:i:s', $row->createTime) ?></td>
										<td>
											<?php if ($row->status === '1') { ?>正常
											<?php } elseif ($row->status === '9') { ?>禁用
											<?php } else { ?>异常
											<?php } ?>
										</td>
										<td>
											<a href="?op=edit&id=<?= $row->id ?>">
												<button class="btn btn-warning btn-labeled fa fa-edit">编辑</button>
											</a>
											<a href="?op=performance&uid=<?= $row->id ?>">
												<button class="btn btn-success btn-labeled fa fa-money">团队佣金
												</button>
											</a>
											<a href="?op=channels&uid=<?= $row->id ?>&type=channel">
                                            	<button class="btn btn-success btn-labeled fa fa-money">个人业绩
                                            	</button>
                                            </a>
                                            <a href="?op=channellist&uid=<?= $row->id ?>">
                                                 <button class="btn btn-success btn-labeled fa fa-money">推广列表
                                                </button>
                                            </a>
										</td>
									</tr>
								<?php } ?>
								</tbody>
							</table>
							<div class="panel-body text-center">
								<ul class="pagination">
									<li ><a href="?op=channel&page=1<?php if (isset($keyword)) { ?>?keyword=<?= $keyword ?><?php } ?>">首页</a></li>
									<?php if ($page->current != 1) { ?>
										<li><a href="?op=channel&page=<?= $page->before ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">上一页</a></li>
									<?php } ?>

									<?php
									$pageStart = max(1, $page->current - 5);
									if ($pageStart == 1) {
										$pageEnd = min($page->current + 10, $page->last);
									} else {
										$pageEnd = min($page->current + 5, $page->last);
									}
									if ($pageEnd == $page->last) {
										$pageStart = max(1, $page->current - 10);
									}
									for ($i = $pageStart; $i <= $pageEnd; $i++): ?>
									<li class="<?php if ($page->current == $i) { ?>active<?php } ?>"><a href="?op=channel&page=<?= $i ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>"><?= $i ?></a></li>
									<?php endfor; ?>

									<?php if ($page->current != $page->last) { ?>
									<li><a href="?op=channel&page=<?= $page->next ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">下一页</a></li>
									<?php } ?>
									<li><a href="?op=channel&page=<?= $page->last ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">尾页</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
			<?php } elseif ($op === 'salesman') { ?>
			<div id="demo-lft-tab-1" class="tab-pane fade active in">
				<div class="panel">
					<div class="panel-heading">
						<h4 class="panel-title">筛选</h4>
					</div>
					<div class="panel-body">
						<form class="form-horizontal">
							<div class="form-group">
								<label class="col-xs-12 col-sm-3 col-md-1 control-label">关键字</label>
								<div class="col-xs-12 col-sm-9 col-md-6">
									<input type="text" name="keyword" value="<?= $keyword ?>"
										   placeholder="请输入UID、用户名、昵称，查找用户" class="form-control">
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-12 col-sm-3 col-md-1 control-label"></label>
								<div class="col-xs-12 col-sm-9 col-md-6">
									<input type="hidden" name="op" value="salesman"/>
									<button class="btn btn-info fa fa-search" type="submit">搜索</button>
								</div>
							</div>
						</form>
					</div>
				</div>
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th>UID</th>
									<th>账号</th>
									<th>昵称</th>
									<th>用户组</th>
									<th>金币</th>
									<th>冻结金币</th>
									<th>金币总额</th>
									<th>业务员佣金比例</th>
									<th>注册时间</th>
									<th>状态</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<?php foreach ($page['items'] as $row) { ?>
									<tr>
										<td><?= $row['id'] ?></td>
										<td><?= $row['user'] ?></td>
										<td>
											<?php if (isset($row['nickname'])) { ?>
												<?= $row['nickname'] ?>
											<?php } else { ?>
												暂未设置
											<?php } ?>
										</td>
										<td><?= $row['usergroup'] ?></td>
										<td><?= sprintf('%.4f', $row['coing']) ?></td>
										<td><?= sprintf('%.4f', $row['Frozen']) ?></td>
										<td><?= sprintf('%.4f', $row['coing'] + $row['Frozen']) ?></td>
										<td>
											<?php
												$salesmanRebate= json_decode($row['salesmanRebate'], true);
												echo '一级农场:' . $salesmanRebate[1][1] . '  二级农场:' . $salesmanRebate[1][2];
												echo '<br>';
												echo '一级庄园:' . $salesmanRebate[2][1] . '  二级庄园:' . $salesmanRebate[2][2];
											?>
										</td>
										<td><?= date('Y-m-d H:i:s', $row['createTime']) ?></td>
										<td>
											<?php if ($row['status'] === '1') { ?>正常
											<?php } elseif ($row['status'] === '9') { ?>禁用
											<?php } else { ?>异常
											<?php } ?>
										</td>
										<td>
											<a href="?op=edit&id=<?= $row['id'] ?>">
												<button class="btn btn-warning btn-labeled fa fa-edit">编辑</button>
											</a>
										</td>
									</tr>
								<?php } ?>
								</tbody>
							</table>

							<div class="panel-body text-center">
								<ul class="pagination">
									<li><a href="?op=performance&uid=<?= $uid ?>&page=1" class="demo-pli-arrow-right">首页</a></li>
									<?php if ($page['current'] != 1) { ?>
										<li><a href="?op=performance&uid=<?= $uid ?>&page=<?= $page['before'] ?>">上一页</a></li>
									<?php } ?>
									<?php if ($page['current'] != $page['last']) { ?>
										<li><a href="?op=performance&uid=<?= $uid ?>&page=<?= $page['next'] ?>">下一页</a></li>
									<?php } ?>
									<li><a href="?op=performance&uid=<?= $uid ?>&page=<?= $page['last'] ?>" class="demo-pli-arrow-right">尾页</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
			<?php } elseif ($op === 'performance') { ?>
			<div id="demo-lft-tab-1" class="tab-pane fade active in">
					<div class="panel">
						<div class="panel-control">
                    		<span class="label label-info">合计<?= $page['count'] ?>人 - 业绩共计￥<?= $totalPrice ?> - 佣金共计￥<?= $money ?></span>
                    	</div>
						<div class="panel-heading">
							<h4 class="panel-title">筛选</h4>
						</div>
						<div class="panel-body">
							<form class="form-horizontal">
								<div class="form-group">
									<label class="col-xs-12 col-sm-3 col-md-1 control-label">关键字</label>
									<div class="col-xs-12 col-sm-9 col-md-6">
										<input type="text" name="keyword" value="<?= $keyword ?>" placeholder="请输入UID、用户名、昵称，查找用户" class="form-control">
									</div>
								</div>

								<div class="form-group">
									<label class="col-xs-12 col-sm-3 col-md-1 control-label"></label>
									<div class="col-xs-12 col-sm-9 col-md-6">
										<input type="hidden" name="op" value="performance"/>
										<input type="hidden" name="uid" value="<?= $uid ?>"/>
										<button class="btn btn-info fa fa-search" type="submit">搜索</button>
									</div>
								</div>
							</form>
						</div>
					</div>
					<div class="panel">
						<div class="panel-body">
							<div class="table-responsive">
								<table class="table table-hover">
									<thead>
									<tr>
										<th>UID</th>
										<th>账号</th>
										<th>昵称</th>
										<th>农场业绩</th>
										<th>庄园业绩</th>
										<th>业绩小计</th>
									</tr>
									</thead>
									<tbody>
									<?php foreach ($page['items'] as $row) { ?>
										<tr>
											<td><?= $row['id'] ?></td>
											<td><?= $row['user'] ?></td>
											<td>
												<?php if (isset($row['nickname']) && !empty($row['nickname'])) { ?>
													<?= $row['nickname'] ?>
												<?php } else { ?>
													暂未设置
												<?php } ?>
											</td>
											<td><?= sprintf('%.4f', $row['type_1']) ?></td>
											<td><?= sprintf('%.4f', $row['type_2']) ?></td>
											<td><?= sprintf('%.4f', $row['type_1'] + $row['type_2']) ?></td>
										</tr>
									<?php } ?>
									</tbody>
								</table>
								<div class="panel-body text-center">
									<ul class="pagination">
										<li ><a href="?op=performance&page=1<?php if (isset($keyword)) { ?>?keyword=<?= $keyword ?><?php } ?>">首页</a></li>
										<?php if ($page->current != 1) { ?>
											<li><a href="?op=performance&page=<?= $page->before ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">上一页</a></li>
										<?php } ?>

										<?php
										$pageStart = max(1, $page->current - 5);
										if ($pageStart == 1) {
											$pageEnd = min($page->current + 10, $page->last);
										} else {
											$pageEnd = min($page->current + 5, $page->last);
										}
										if ($pageEnd == $page->last) {
											$pageStart = max(1, $page->current - 10);
										}
										for ($i = $pageStart; $i <= $pageEnd; $i++): ?>
										<li class="<?php if ($page->current == $i) { ?>active<?php } ?>"><a href="?op=performance&page=<?= $i ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>"><?= $i ?></a></li>
										<?php endfor; ?>

										<?php if ($page->current != $page->last) { ?>
										<li><a href="?op=performance&page=<?= $page->next ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">下一页</a></li>
										<?php } ?>
										<li><a href="?op=performance&page=<?= $page->last ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">尾页</a></li>
									</ul>
								</div>
							</div>
						</div>
					</div>
				</div>
				<?php } elseif ($op === 'channellist') { ?>
					<div id="demo-lft-tab-1" class="tab-pane fade active in">
						<div class="panel">
							<div class="panel-control">
								<span class="label label-info"><?php if (isset($channellist['number'])) { ?>直接推广<?= $channellist['number'] ?>个 - 间接推广<?= $channellist['sum'] - $channellist['number'] ?>个<?php } ?></span>
							</div>
							<div class="panel-heading">
								  <h4 class="panel-title">筛选</h4>
							</div>
							<div class="panel-body">
							   <form class="form-horizontal">
									<div class="form-group">
										<div class="form-group">
										<label class="col-xs-12 col-sm-3 col-md-1 control-label">关键字</label>
											<div class="col-xs-12 col-sm-9 col-md-6">
												<input type="text" name="keyword" value="<?= $keywords ?>" placeholder="请输入UID、用户名，查找用户" class="form-control">
											</div>
										</div>
										<div class="col-xs-12 col-sm-9 col-md-6">
											<input type="hidden" name="op" value="channellist"/>
											<input type="hidden" name="uid" value="<?= $uid ?>"/>
											<button class="btn btn-info fa fa-search" type="submit">搜索</button>
									   </div>
								   </div>
								</form>
							</div>
						</div>
							<div class="panel">
								<div class="panel-body">
									<div class="table-responsive">
										<table class="table table-hover">
											<thead>
											<tr>
												<th>ID</th>
												<th>用户名</th>
												<th>推广级别</th>
												<th>注册时间</th>
												<th>已领取</th>
												<th>未领取</th>
											</tr>
											</thead>
											<tbody>
											<?php if (isset($channellist)) { ?>
												<?php foreach ($channellist['items'] as $row) { ?>

													<tr>
													<td><?= $row['id'] ?></td>
													<td>
														<?= $row['user'] ?>
													</td>
													<td>
													<?php if (isset($row['level'])) { ?>
														<?= $row['level'] + 1 ?><?php } else { ?>未知
													<?php } ?>
													</td>
													<td><?= date('Y-m-d H:i:s', $row['createTime']) ?></td>
													<td><?= $row['yes'] ?></td>
													<td><?= $row['no'] ?></td>
													</tr>
												<?php } ?>
											<?php } ?>
											</tbody>
										</table>
										<div class="bars pull-left">
											<button class="btn btn-info">
												<a href="<?= $apppath ?>/user/print<?php if (isset($uid)) { ?>?uid=<?= $uid ?><?php } ?>" style="color:#fff">
													<i class="demo-pli-cross"></i> 批量导出
												 </a></button>
										</div>
									</div>
								</div>
							</div>
						</div>
				<?php } elseif ($op === 'channels') { ?>
					<div id="demo-lft-tab-1" class="tab-pane fade active in">
							<div class="panel">
							<div class="panel-control">
								<span class="label label-info">业绩<?= $all_money ?>￥ - 佣金<?= $all_price ?>￥</span>
							</div>
								<div class="panel-heading">
									<h4 class="panel-title">筛选</h4>
								</div>
								<div class="panel-body">
									<form class="form-horizontal">
									<div class="form-group">
											<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
												<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
													<?=Dhc\Component\MyTags::TimePiker("time",array('starttime'=>$starttime,'endtime'=>$endtime))?>
												</div>
										</div>
										<div class="form-group">
											<label class="col-xs-12 col-sm-3 col-md-1 control-label"></label>
											<div class="col-xs-12 col-sm-9 col-md-6">
												<input type="hidden" name="op" value="channels"/>
												<input type="hidden" name="type" value="<?php if (isset($type)) { ?><?= $type ?><?php } ?>"/>
												<input type="hidden" name="uid" value="<?= $uid ?>"/>
												<button class="btn btn-info fa fa-search" type="submit">搜索</button>
											</div>
										</div>
									</form>
								</div>
							</div>
							<div class="panel">
								<div class="panel-body">
									<div class="table-responsive">
										<table class="table table-hover">
											<thead>
											<tr>
												<th>来源UID</th>
												<th>来源昵称</th>
												<th>来源账号</th>
												<th>消费金额</th>
												<th>分佣比例</th>
												<th>获得佣金</th>
												<th>获得时间</th>
												<th>结算时间</th>
											</tr>
											</thead>
											<tbody>
											<?php if (isset($page)) { ?>
												<?php foreach ($page->items as $row) { ?>
													<tr>
													<td><?= $row['cUid'] ?></td>
													<td>
													</td>
													<td>
													<?php foreach ($realname as $names) { ?>
															<?php if ($names['id'] == $row['cUid']) { ?>
															<?= $names['user'] ?>
															<?php } ?>
													<?php } ?>
													</td>
													<td><?= $row['gold'] ?></td>
													<td><?= $row['rebate'] ?></td>
													<td><?= $row['amount'] ?></td>
													<td><?= date('Y-m-d H:i:s', $row['createTime']) ?></td>
													<td>
													<?php if ($row['effectTime'] !== '0') { ?>
														<?= date('Y-m-d H:i:s', $row['effectTime']) ?>
													<?php } ?>
													</td>
													</tr>
												<?php } ?>
											<?php } ?>
											</tbody>
										</table>

										<div class="panel-body text-center">
											<ul class="pagination">
												<li ><a href="?op=channels&page=1<?php if (isset($keyword)) { ?>?keyword=<?= $keyword ?><?php } ?>">首页</a></li>
												<?php if ($page->current != 1) { ?>
													<li><a href="?op=channels&page=<?= $page->before ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">上一页</a></li>
												<?php } ?>

												<?php
												$pageStart = max(1, $page->current - 5);
												if ($pageStart == 1) {
													$pageEnd = min($page->current + 10, $page->last);
												} else {
													$pageEnd = min($page->current + 5, $page->last);
												}
												if ($pageEnd == $page->last) {
													$pageStart = max(1, $page->current - 10);
												}
												for ($i = $pageStart; $i <= $pageEnd; $i++): ?>
												<li class="<?php if ($page->current == $i) { ?>active<?php } ?>"><a href="?op=channels&page=<?= $i ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>"><?= $i ?></a></li>
												<?php endfor; ?>

												<?php if ($page->current != $page->last) { ?>
												<li><a href="?op=channels&page=<?= $page->next ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">下一页</a></li>
												<?php } ?>
												<li><a href="?op=channels&page=<?= $page->last ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>">尾页</a></li>
											</ul>
										</div>
									</div>
								</div>
							</div>
						</div>

                <?php } ?>

		</div>
	</div>
</div>
