<?php $this->flashSession->output(); ?>
<!--商品信息-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="<?php if (isset($op)) { ?><?php if ($op == 'display') { ?>active<?php } else { ?> <?php } ?><?php } ?>">
				<a href="<?= $apppath ?>/orchard/orchard?op=display&page=<?= $landList->current ?>" aria-expanded="<?php if (isset($op)) { ?><?php if ($op == 'display') { ?>true<?php } else { ?> false<?php } ?><?php } ?>">果园管理</a>
			</li>
		</ul>
		<?php if ($op == 'display') { ?>
		<div class="tab-content">
			<div class="panel-body">
				<form class="form-horizontal form-padding " method="get" action="<?= $apppath ?>/orchard/orchard">
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索信息</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<input class="form-control" type="text" name="keywords" value="<?php if (isset($keywords)) { ?><?= $keywords ?><?php } ?>" placeholder="请输入会员编号">
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">土地信息</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<select name="landId" class="form-control">
								<?php foreach ($landIdInfo as $key => $row) { ?>
								<option value="<?= $key ?>" <?php if (isset($landId)) { ?><?php if ($key == $landId) { ?>selected<?php } ?><?php } ?>>
									<?= $row ?>
								</option>
								<?php } ?>
							</select>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">土地状态</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<select name="landStatus" class="form-control">
								<?php foreach ($landStatusInfo as $key => $row) { ?>
								<option value="<?= $key ?>" <?php if (isset($landStatus)) { ?><?php if ($key == $landStatus) { ?>selected<?php } ?><?php } ?>>
									<?= $row ?>
								</option>
								<?php } ?>
							</select>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
						</div>
					</div>
					<div class="text-lg-center">
						<button class="btn btn-info fa fa-search" type="submit">搜索</button>
					</div>
				</form>
			</div>
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if (isset($op)) { ?><?php if ($op == 'display') { ?>active in<?php } else { ?> <?php } ?><?php } ?>">
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
									<tr>
										<th>会员ID</th>
										<th>会员信息</th>
										<th>土地编号</th>
										<th>土地信息</th>
										<th>土地等级</th>
										<th>果实信息</th>
										<th>果实状态</th>
										<th>创建时间</th>
										<th>更新时间</th>
										<th>操作时间</th>
										<th>操作</th>
									</tr>
								</thead>
								<tbody>
								<?php if (isset($landList)) { ?>
								<?php foreach ($landList as $list) { ?>
								<?php if (!is_scalar($list)) { ?>
								<?php foreach ($list as $rows) { ?>
								<tr>
									<td><?= $rows->uid ?></td>
									<td><?= $rows->nickname ?><br/><?= $rows->mobile ?></td>
									<td><?= $rows->landId ?></td>

									<td><?php echo $landIdInfo[$rows->landId];?></td>
									<td><?php echo $landLevelInfo[$rows->landLevel];?></td>
									<td><?= $rows->goodsName ?><?= $rows->goodsNums ?>颗</td>
									<td><?php echo $landStatusInfo[$rows->landStatus];?></td>
									<td><?= date('Y-m-d H:i:s', $rows->createtime) ?></td>
									<td><?= date('Y-m-d H:i:s', $rows->updatetime) ?></td>
									<td><?= date('Y-m-d H:i:s', $rows->optime) ?></td>
									<td>
									<?php if ($rows->plowing == 9) { ?>
									<a href="<?= $apppath ?>/orchard/orchard?op=plowing&id=<?= $rows->id ?>&level=0">
										<button class="btn btn-warning btn-labeled">解冻</button>
									</a>
									<?php } else { ?>
									<a href="<?= $apppath ?>/orchard/orchard?op=plowing&id=<?= $rows->id ?>&level=9">
										<button class="btn btn-warning btn-labeled">冻结</button>
									</a>
									<?php } ?>
									<?php if ($rows->landLevel > 3) { ?>
									<a href="<?= $apppath ?>/orchard/orchard?op=downgrade&id=<?= $rows->id ?>">
										<button class="btn btn-warning btn-labeled">土地降级</button>
									</a>
									<?php } ?>
									</td>
									<?php } ?>
									<?php } ?>
									<?php } ?>
									<?php } ?>
								</tbody>
							</table>
							<div class="panel-body text-center">
								<?php if ($landList->total_pages > 1) { ?>
								<ul class="pagination">
									<li><a href="<?= $apppath ?>/orchard/orchard?op=display&page=1&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?>" class="demo-pli-arrow-right">首页</a></li>
									<?php if ($landList->current != 1) { ?>
									<li><a href="<?= $apppath ?>/orchard/orchard?op=display&page=<?= $landList->before ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?>">上一页</a></li>
									<?php } ?>
									<li><a href="#">第<?= $landList->current ?>页</a></li>
									<?php
									$pageStart = max(1, $landList->current - 5);
									if ($pageStart == 1) {
										$pageEnd = min($landList->current + 10, $landList->last);
									} else {
										$pageEnd = min($landList->current + 5, $landList->last);
									}
									if ($pageEnd == $landList->last) {
										$pageStart = max(1, $landList->current - 10);
									}
									for ($i = $pageStart; $i <= $pageEnd; $i++): ?>
									<li class="<?php if ($landList->current == $i) { ?>active<?php } ?>"><a href="?op=display&page=<?= $i ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?><?php if (isset($keyword)) { ?>&keyword=<?= $keyword ?><?php } ?>"><?= $i ?></a></li>
									<?php endfor; ?>
									<li><a href="#">共<?= $landList->total_pages ?>页</a></li>
									<?php if ($landList->current != $landList->last) { ?>
									<li><a href="<?= $apppath ?>/orchard/orchard?op=display&page=<?= $landList->next ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?>">下一页</a></li>
									<?php } ?>
									<li><a href="<?= $apppath ?>/orchard/orchard?op=display&page=<?= $landList->total_pages ?>&time[start]=<?= $starttime ?>&time[end]=<?= $endtime ?>" class="demo-pli-arrow-right">尾页</a></li>
								</ul>
								<?php } ?>
							</div>
						</div>
					</div>
				</div>
			</div>
			<?php } ?>
		</div>
	</div>
</div>
<!--日志模块结束--!>

