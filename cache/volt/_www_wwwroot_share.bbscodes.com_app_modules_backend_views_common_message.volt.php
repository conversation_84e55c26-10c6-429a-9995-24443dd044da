<div class="alert alert-<?= $data['type'] ?>">
	<h4>
		<span class="fa fa-warning fa-2x"></span>
		<?php if ($data['type'] == 'success') { ?>成功
		<?php } elseif ($data['type'] == 'info') { ?>提示
		<?php } elseif ($data['type'] == 'warning') { ?>警告
		<?php } elseif ($data['type'] == 'danger') { ?>错误
		<?php } ?>
	</h4>
	<p style="font-size: 16px"><?php if (!empty($data['message'])) { ?><?= $data['message'] ?><?php } ?></p>
	<p>
		<a href="<?= $data['redirect'] ?>" class="alert-link">[点击这里跳转]</a>&nbsp;&nbsp;<?= $this->tag->linkTo(['/admin/index/index', '[首页]', 'class' => 'alert-link']) ?>
	</p>
	<?php if (!empty($data['redirect']) && $data['type'] != 'danger') { ?>
	<script>
		var redirect = '<?= $data['redirect'] ?>';
		var delayTime = <?php if ($data['type'] == 'success') { ?>1500<?php } else { ?>2500<?php } ?>;
		setTimeout(function(){
			window.location.href = redirect;
		}, delayTime);
	</script>
	<?php } ?>
</div>
