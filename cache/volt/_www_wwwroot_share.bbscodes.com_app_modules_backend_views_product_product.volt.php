
<!--文章模块开始-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="<?php if ($show === 'list') { ?>active<?php } ?>">
				<a data-toggle="tab" href="#demo-lft-tab-1" aria-expanded="<?php if ($show === 'list') { ?>true<?php } else { ?>false<?php } ?>">产品列表</a>
			</li>
			<?php if ($show === 'edit') { ?>
			<li class="active">
				<a data-toggle="tab" href="#demo-lft-tab-2" aria-expanded="<?php if ($show === 'edit') { ?>true<?php } else { ?>false<?php } ?>">产品编辑</a>
			</li>
			<?php } ?>
		</ul>
		<div class="tab-content">
			<div id="demo-lft-tab-1" class="tab-pane fade <?php if ($show === 'list') { ?> active in<?php } ?>">
				<div class="panel">
					<div class="panel-body">

						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
								<tr>
									<th class="text-center">id</th>
									<th>产品名称</th>
									<th>初始价格</th>
									<th>创建时间</th>
									<th>状态</th>
									<th>手续费</th>
									<th>涨幅</th>
									<th>跌幅</th>
									<th>操作</th>
								</tr>
								</thead>
								<tbody>
								<?php foreach ($product as $row) { ?>
									<tr>
										<td><i> <?= $row->id ?></i></td>
										<td><i> <?= $row->title ?></i></td>
										<td><?= $row->startprice ?></td>
										<td><span class="text-muted"><i class="demo-pli-clock"></i> <?php echo date('Y-m-d H:i:s',$row->createtime)?></span></td>
										<td>
											<div class="label label-table label-success"><?php if ($row->status == '1') { ?>启用<?php } elseif ($row->status == '0') { ?>停用<?php } ?></div>
										</td>
										<td><?= $row->poundage ?></td>
										<td ><?= $row->rise ?></td>
										<td><?= $row->fall ?></td>
										<td>
											<a href="<?= $apppath ?>/product/product/edit/<?= $row->id ?>" class="btn btn-default btn-sm" title="编辑"><i class="fa fa-edit"></i></a>
										</td>
									</tr>
								<?php } ?>
								</tbody>
							</table>
							<div class="panel-body text-center">
								<ul class="pagination">
									<li><a href="<?= $apppath ?>/product/product/page/1" class="demo-pli-arrow-right">首页</a></li>
									<li><a href="<?= $apppath ?>/product/product/page/<?php if (isset($pagenow)) { ?><?= $pagenow - 1 ?><?php } else { ?>1<?php } ?>">上一页</a></li>
									<li><a href="<?= $apppath ?>/product/product/page/<?php if (isset($pagenow)) { ?><?= $pagenow + 1 ?><?php } else { ?>1<?php } ?>">下一页</a></li>
									<li><a href="<?= $apppath ?>/product/product/page/<?php if (isset($tpage)) { ?><?= $tpage ?><?php } else { ?>1<?php } ?>" class="demo-pli-arrow-right">尾页</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div id="demo-lft-tab-2" class="tab-pane fade <?php if ($show === 'edit') { ?> active in<?php } ?>">
				<div class="panel">
					<form class="panel-body form-horizontal form-padding " method="post"  action="<?= $apppath ?>/product/product/list">
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">产品名称</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input type="text" class="form-control" name="title" value="<?php if (isset($item->title)) { ?><?= $item->title ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">产品图片</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<?php if (isset($item)) { ?>
									<?=Dhc\Component\MyTags::tpl_upload_images("thumb",$item->thumb)?>
								<?php } else { ?>
									<?=Dhc\Component\MyTags::tpl_upload_images("thumb")?>
								<?php } ?>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">初始价格</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" id="demo-vs-definput"  name="startprice" value="<?php if (isset($item->startprice)) { ?><?= $item->startprice ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label" for="demo-email-input" >状态</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<select name="status" class="form-control"  id="demo-vs-definput" >
									<option value="1" <?php if (isset($item->status)) { ?><?php if ($item->status === '1') { ?> selected <?php } ?><?php } ?>>启用</option>
									<option value="0" <?php if (isset($item->status)) { ?><?php if ($item->status === '0') { ?> selected <?php } ?><?php } ?>>停用</option>
								</select>
							</div>
						</div>
						<div class="form-group">
                        							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label" for="demo-email-input" >交易状态</label>
                        							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                        								<select name="tradeStatus" class="form-control"  id="demo-vs-definput" >
                        									<option value="1" <?php if (isset($item->tradeStatus)) { ?><?php if ($item->tradeStatus === '1') { ?> selected <?php } ?><?php } ?>>开启</option>
                        									<option value="0" <?php if (isset($item->tradeStatus)) { ?><?php if ($item->tradeStatus === '0') { ?> selected <?php } ?><?php } ?>>关闭</option>
                        								</select>
                        							</div>
                        						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">涨幅</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" id="demo-vs-definput"  name="rise" value="<?php if (isset($item->rise)) { ?><?= $item->rise ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
                        							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">排序顺序</label>
                        							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                        								<input class="form-control" id="demo-vs-definput"  name="displayorder" value="<?php if (isset($item->displayorder)) { ?><?= $item->displayorder ?><?php } ?>">
                        							</div>
                        						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">跌幅</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" id="demo-vs-definput"  name="fall" value="<?php if (isset($item->fall)) { ?><?= $item->fall ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">手续费</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" id="demo-vs-definput"  name="poundage" value="<?php if (isset($item->poundage)) { ?><?= $item->poundage ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">简介</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" name="depict" value="<?php if (isset($item->depict)) { ?><?= $item->depict ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">种子周期/小时</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" name="seedTime" value="<?php if (isset($item->seedTime)) { ?><?= $item->seedTime ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">发芽周期/小时</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" name="sproutingTime" value="<?php if (isset($item->sproutingTime)) { ?><?= $item->sproutingTime ?><?php } ?>">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">生长周期/小时</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" name="growTime" value="<?php if (isset($item->growTime)) { ?><?= $item->growTime ?><?php } ?>">
							</div>
						</div>
						<div class="panel-footer text-left">
							<input type="hidden" value="<?php if (isset($item->id)) { ?><?= $item->id ?><?php } ?>" name="id">
							<button class="btn btn-success" type="submit">添加</button>
						</div>
					</form>
					<!--===================================================-->
					<!-- END BASIC FORM ELEMENTS -->
				</div>
			</div>

		</div>
	</div>
</div>
<!--文章模块结束--!>
