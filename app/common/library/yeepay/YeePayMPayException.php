<?php
/**
 * <AUTHOR>
 * @copyright Copyright (c) 2017 HNDH Software Technology Co., Ltd.
 * @link http://www.dhsoft.cn
 */

namespace Dhc\Library\yeepay;

class YeePayMPayException extends \Exception{
	public function __construct($message,$code = 0) {
		echo "错误码：" . $code;
		echo "<br>";
		echo "错误描述：" . $message;

		// 确保所有变量都被正确赋值
		parent::__construct($message,$code);
	}

	// 自定义字符串输出的样式
	public function __toString() {
		return __CLASS__ . ": [{$this->code}]: {$this->message}\n";
	}

}
