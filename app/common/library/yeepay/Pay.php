<?php
/**
 * <AUTHOR>
 * @copyright Copyright (c) 2017 HNDH Software Technology Co., Ltd.
 * createtime: 2017/11/3 18:56
 */

namespace Dhc\Library\yeepay;

use Dhc\Models\UserConfig;
use Phalcon\Exception;

class Pay
{
	const API_URL = 'https://ok.yeepay.com/paymobile/payapi/request';
	private $yeepay;
	private $data;

    /**
     * Pay constructor.
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
	{
		$notifyUrl = 'http://'.$_SERVER['HTTP_HOST'].'/wapi/notify/yb';
		$returnUrl = 'http://'.$_SERVER['HTTP_HOST'].'/web/#/userCenter/';
		// 易宝要求格式必须严格符合要求 字符串 数字类型必须正确
		$this->data = [
			'orderid' => $params['terminal_trace'],
			'amount' => intval($params['total_fee']),
			'callbackurl' => $notifyUrl,
			'fcallbackurl' => $returnUrl,
			'transtime' => time(),
			'currency' => 156,
			'productcatalog' => '1',
			'productname' => '在线充值',
			'productdesc' => '在线充值',
			'orderexpdate' => 60,
			'identitytype' => 2,
			'identityid' => '0',
			'terminaltype' => 1,
			'terminalid' => '00-00-00-00-00-00',
			'userip' => '127.0.0.1',
			'cardno' => '',
			'idcardtype' => '',
			'idcard' => '',
			'owner' => '',
			'paytool' => '',
			'directpaytype' => 0, // 0：默认；1：微信支付；2：支付宝支付；3：一键支付
			'userua' => 'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.118 Safari/537.36',
			'paytypes' => '',
			'version' => 0
		];

		$userConfig = UserConfig::findFirst("payType = 'YB' AND status = 1");
		if (empty($userConfig->merchant_no) || empty($userConfig->other)) {
			throw new Exception('支付错误，请联系客服配置参数！');
		}
		$userConfig->other = json_decode($userConfig->other, true);

		// 商户编号
		$merchantaccount = $userConfig->merchant_no;
		// 商户私钥
		$merchantPrivateKey = $userConfig->other['merchantPrivateKey'];
		// 商户公钥
		$merchantPublicKey = $userConfig->other['merchantPublicKey'];
		// 易宝公钥
		$yeepayPublicKey = $userConfig->other['yeepayPublicKey'];

		$this->yeepay = new YeePayMPay($merchantaccount, $merchantPublicKey, $merchantPrivateKey, $yeepayPublicKey);
	}

	public function run()
	{
		$result = $this->yeepay->webPay($this->data);
		// $yeepayPublicKey，请更改payurl为imghexstr
		if (!empty($result['error_code']) || empty($result['payurl'])) {
			throw new Exception('支付错误，请联系管理员或重试' . "[{$result['error_msg']}]");
		}
		return $result['payurl'];
	}
}
