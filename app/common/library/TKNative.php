<?php
/**
 * Created by PhpStorm.
 * User: XingBin
 * Date: 2017/7/26
 * Time: 16:16
 */
namespace Dhc\Library;
use Dhc\Models\UserConfig;
use Phalcon\Di\Injectable;

class TKNative extends Injectable{
	public $config;
	public $data;
	public $payUrl = "http://api.tengkunkeji.cn/api.aspx";
	public function __construct(){
		$userConfig = UserConfig::findFirst("payType ='TK' AND status = 1");
		if (empty($userConfig->merchant_no)||empty($userConfig->access_token)||empty($userConfig)){
			$this->ajaxResponse('','操作失败，请联系管理员','1');
		}
		$this->config = $userConfig;
	}
	function run($data){
		$pay_memberid = $this->config->merchant_no;
		if($data["type"] == "wechet"){
			$data["type"] = 1005;
		}elseif($data["type"] == "alipay"){
			$data["type"] = 1006;
		}
		$pay_orderid = $data["terminal_trace"];
		$pay_amount = $data["total_fee"]/100;
		$pay_applydate = date("Y-m-d H:i:s",time());
		$pay_bankcode = $data['type'];
		$pay_notifyurl = 'http://'.$_SERVER['HTTP_HOST'].'/wapi/notify/tk';
		$pay_callbackurl = 'http://'.$_SERVER['HTTP_HOST'].'/web/?token='.$data["token"].'#/userCenter/';
		$requestarray = array(
			"pay_version"=>"vb1.0",
			"pay_memberid" => $pay_memberid,
			"pay_orderid" => $pay_orderid,
			"pay_amount" => $pay_amount,
			"pay_applydate" => $pay_applydate,
			"pay_bankcode" => $pay_bankcode,
			"pay_notifyurl" => $pay_notifyurl,
			"pay_callbackurl" => $pay_callbackurl,
		);
		$this->data = $requestarray;
		$this->data["pay_md5sign"] = $this->getSign($this->config->access_token);
		$this->payUrl .="?".http_build_query($this->data);
		return $this->payUrl;
	}
	private function getSign($access_token){
		$initData = $this->data;
		if ($initData){
			$signPars = "";
			ksort($initData);
			reset($initData);
			foreach($initData as $k => $v) {
				$signPars .= $k . "=" . $v . "&";
			}
			$signPars .= 'key='.$access_token;
			$sign = strtoupper(md5($signPars));
			return $sign;
		} else {
			exit($initData['message']);
		}
	}

}
