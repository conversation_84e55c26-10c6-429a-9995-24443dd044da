<?php
/**
 * Created by PhpStorm.
 * User: XingBin
 * Date: 2017/7/26
 * Time: 16:16
 */
namespace Dhc\Library;
use Dhc\Models\UserConfig;
use Phalcon\Di\Injectable;

class WPNative extends Injectable{
	public $config;
	public $data;
	public $payUrl = "http://jspay.wiipay.cn/1/jspay/";
	public function __construct(){
		$userConfig = UserConfig::findFirst("payType ='WP' AND status = 1");
		if (empty($userConfig->terminal_id)||empty($userConfig->access_token)||empty($userConfig)){
			$this->ajaxResponse('','操作失败，请联系管理员','1');
		}
		$this->config = $userConfig;
	}
	function run($data){
		if($data["type"] == "wechet"){
			$data["url"] = "wxh5/wx_api.do";
		}elseif($data["type"] == "alipay"){
			$data["url"] = "ali/ali_api.do";
		}
		$orderid = $data["terminal_trace"];
		$pay_amount = $data["total_fee"]/100;
		$callbackurl = 'http://'.$_SERVER['HTTP_HOST'].'/web/?token='.$data["token"].'#/userCenter/';
		$requestarray = array(
			"app_id" => $this->config->terminal_id,
			"body" => "充值",
			"callback_url" => $callbackurl,
			"channel_id"=>"农场",
			"format" => "json",
			"out_trade_no" => $orderid,
			"total_fee" => $pay_amount,
			"version"=>"2.0",
		);
		$signPars = "";
		foreach ($requestarray as $key=>$val){
			$signPars .= $key . "=" . $val . "&";
		}
		$signPars = trim($signPars,"&");
		$signPars .=$this->config->access_token;
		$requestarray['sign'] = strtoupper(md5($signPars));
		$requestarray["url"] = $this->payUrl.$data["url"]."?".http_build_query($requestarray);
//		if($data["type"] == "wechet"){
//			return $requestarray["url"];
//		}
		$res = json_decode($this->payRequest($requestarray),true);
		if(@is_array($res) && $res["ret_state"] == "success" && !empty($res["pay_url"])){
			return $res["pay_url"];
		}else{
			return false;
		}
	}
	//发送请求
	public function payRequest($data){
		$url = $data["url"];
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL,$url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		$jsonList = curl_exec($ch);
		curl_close($ch);
		return $jsonList;
	}
	private function getSign($access_token){
		$initData = $this->data;
		if ($initData){
			$signPars = "";
			ksort($initData);
			foreach($initData as $k => $v) {
				$signPars .= $k . "=" . $v . "&";
			}
			$signPars = trim($signPars,"&");
			$signPars .=strtoupper($access_token);
			echo $signPars;
			$sign = strtoupper(md5($signPars));
			return $sign;
		} else {
			exit($initData['message']);
		}
	}

}
