<?php
/**
 * Created by PhpStorm.
 * User: XingBin
 * Date: 2017/7/26
 * Time: 16:16
 */
namespace Dhc\Library;
use Dhc\Models\UserConfig;
use Phalcon\Di\Injectable;

class QHNative extends Injectable{
	public $config;
	public $data;
	public function __construct(){
		$userConfig = UserConfig::findFirst("payType ='QH' AND status = 1");
		if (empty($userConfig->merchant_no)||empty($userConfig->access_token)||empty($userConfig)){
			$this->ajaxResponse('','操作失败，请联系管理员','1');
		}
		$this->config = $userConfig;
	}
	function run($data){
		$pay_memberid = $this->config->merchant_no;
		if($data["type"] == "wechet"){
			$data["type"] = 901;
		}elseif($data["type"] == "alipay"){
			$data["type"] = 904;
		}
		$pay_orderid = $data["terminal_trace"];
		$pay_amount = $data["total_fee"]/100;
		$pay_applydate = date("Y-m-d H:i:s",time());
		$pay_bankcode = $data['type'];
		$pay_notifyurl = 'http://'.$_SERVER['HTTP_HOST'].'/wapi/notify/qh';
		$pay_callbackurl = 'http://'.$_SERVER['HTTP_HOST'].'/web/?token='.$data["token"].'#/userCenter/';
		$requestarray = array(
			"pay_memberid" => $pay_memberid,
			"pay_orderid" => $pay_orderid,
			"pay_amount" => $pay_amount,
			"pay_applydate" => $pay_applydate,
			"pay_bankcode" => $pay_bankcode,
			"pay_notifyurl" => $pay_notifyurl,
			"pay_callbackurl" => $pay_callbackurl,
		);
		$this->data = $requestarray;
		$this->data["pay_md5sign"] = $this->getSign($this->config->access_token);
//		print_r($this->data);
		$res = $this->payRequest();
//		var_dump($res);
		$resinfo = json_decode($this->getErweima($res),true);
//		var_dump($resinfo);
		$url = explode("Location: ",$res);
		if(empty($url[1]) || $resinfo["status"] == "error"){
			return false;
		}
		if(strpos($url[1],"http://") !== false || strpos($url[1],"https://") !== false){
			return trim($url[1]);
		}else{
			return false;
		}
	}
	public function getErweima($str){
		$preg = '/\{[^{}]+\}/';
		$str = $str;
		preg_match($preg,$str,$strs);
		if(!empty($strs[0])){
			return $strs[0];
		}else{
			return false;
		}
	}
	private function getSign($access_token){
		$initData = $this->data;
		if ($initData){
			$signPars = "";
			ksort($initData);
			reset($initData);
			foreach($initData as $k => $v) {
				$signPars .= $k . "=" . $v . "&";
			}
			$signPars .= 'key='.$access_token;
			$sign = strtoupper(md5($signPars));
			return $sign;
		} else {
			exit($initData['message']);
		}
	}
	//发送请求
	public function payRequest(){
		$data= $this->data;
		$data['url'] = "http://gucun.ijizhan.net/Pay_Index.html";
		$ch = curl_init($data['url']);
		//设置请求为post
		curl_setopt($ch,CURLOPT_POST,1);
		//设置请求的变量
		curl_setopt($ch,CURLOPT_POSTFIELDS,$data);
		//设置响应返回数据 可省略
		//设置json请求
		curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
		//设置相应头信息
		curl_setopt($ch,CURLOPT_HEADER,1);
		//设置重定向
		curl_setopt($ch,CURLOPT_FOLLOWLOCATION,FALSE);
		//设置超时
		curl_setopt($ch,CURLOPT_CONNECTTIMEOUT,30);
		curl_setopt($ch,CURLOPT_TIMEOUT,30);
		//执行
		$output = curl_exec($ch);
		//关闭释放内存
		return $output;
	}

}
