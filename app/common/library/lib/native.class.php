<?php

/**
 * 鼎汉科技 Copyright (c) 2016 WWW.DHCC.WANG
 * This is NOT a free software, it under the license terms, visited http://www.dhcc.wang/ for more details.
 */
class Native
{
	private $data;
	private $response;

	public function __construct($data) {
		$this->data = $data;
	}

	public function pay() {
		$this->data['key_sign'] = $this->getSign();
		$this->response = json_decode($this->payRequest(), true);
		if($this->response["return_code"] ==2){
			return error(1,$this->response["return_msg"]);
		}
		return $this->response['qr_code'];
	}

	private function payRequest() {
		$url = "http://pay.lcsw.cn/lcsw/pay/100/prepay";
		$data_string = json_encode($this->data);
		$ch = curl_init($url);
		// 设置请求为POST
		curl_setopt($ch, CURLOPT_POST, 1);
		// 设置请求的变量
		curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
		//设置响应返回数据 可省略
		//设置json请求
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array(
			'Content-Type: application/json',
			'Content-Length: ' . strlen($data_string))
		);
		//设置响应显示头信息
		curl_setopt($ch, CURLOPT_HEADER, 0);
		//设置超时
		curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
		curl_setopt($ch, CURLOPT_TIMEOUT, 30);

		//执行
		$output = curl_exec($ch);
		//关闭释放内存
		curl_close($ch);
		//打印获得的数据
		return $output;
	}

	/**
	 * 初始化数据 获取签名 没有任何意义
	 * @return string
	 */
	private function getSign() {
		$initData = $this->initData();
		if (!is_error($initData)) {
			$param = "pay_ver={$this->data['pay_ver']}";
			$param .= "&pay_type={$this->data['pay_type']}";
			$param .= "&service_id={$this->data['service_id']}";
			$param .= "&merchant_no={$this->data['merchant_no']}";
			$param .= "&terminal_id={$this->data['terminal_id']}";
			$param .= "&terminal_trace={$this->data['terminal_trace']}";
			$param .= "&terminal_time={$this->data['terminal_time']}";
			$param .= "&total_fee={$this->data['total_fee']}";
			$param .= "&access_token={$this->data['access_token']}";
			unset($this->data['access_token']);
			return $sign = strtoupper(md5($param));
		} else {
			message($initData['message'], '', 'info');
		}
	}

	/**
	 * 校验签名
	 * @return bool
	 */
	private function checkSign() {
		$param = '';
		foreach ($this->response as $key => $val) {
			if ($key == 'key_sign') {
				continue;
			}
			$param .= '&' . $key . '=' . $val;
		}
		$param = trim($param, '&');

		if ($this->response['key_sign'] == md5($param)) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 初始化并检查数据
	 * @return array|bool
	 */
	private function initData() {
		if (empty($this->data)) {
			message("接口请求数据错误，请检查", '', 'info');
		}
		if (empty($this->data["pay_ver"])) {
			$this->data["pay_ver"] = '100';
		}
		//请求类型，“010”微信，“020”支付宝，“060”qq钱包
		$payCode = ['wechat'=>"010", 'alipay'=>"020", 'qq'=>"060"];
		if(!empty($payCode[$this->data["pay_type"]])){
			$this->data["pay_type"] = $payCode[$this->data["pay_type"]];
		}else{
			$this->data["pay_type"] = '010';
		}

		if (empty($this->data["service_id"])) {
			$this->data["service_id"] = '011';
		}
		if (empty($this->data["merchant_no"])) {
			message("接口请求数据错误，商务号不能为空", '', 'info');
		}
		if (empty($this->data["terminal_id"])) {
			message("接口请求数据错误，终端号不能为空", '', 'info');
		}
		if (empty($this->data["terminal_trace"])) {
			message("接口请求数据错误，订单号不能为空", '', 'info');
		}
		if (empty($this->data["terminal_time"])) {
			$this->data["terminal_time"] = TIMESTAMP;
		}
		if (empty($this->data["total_fee"]) || !is_numeric($this->data["total_fee"]) || $this->data["total_fee"] <= 0) {
			message("接口请求数据错误，订单金额有误", '', 'info');
		}
		if (empty($this->data["access_token"])) {
			message("接口请求数据错误，密匙错误", '', 'info');
		}
		return true;
	}
}
