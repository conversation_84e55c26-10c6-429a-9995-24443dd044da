<?php
namespace Dhc\Library;

use Dhc\Models\User;


class fuiouPay{
	public $data;
	public $userInfo;
	public function __construct($data,$uid){
		$this->data = $data;
		$user = new User();
		$this->userInfo = $user->findFirst("id=".$uid);
//		$this->userInfo->openid = "asadsasddsasad";
	}
	function pay(){
		$jsposPrePay = new JsPosPrepays();
		$jsposPrePay->setPay_ver("100");
		$jsposPrePay->setPay_type("010");
		$jsposPrePay->setService_id("012");
		$jsposPrePay->setMerchant_no($this->data['merchant_no']);
		$jsposPrePay->setTerminal_id($this->data['terminal_id']);
		$jsposPrePay->setTerminal_trace($this->data['terminal_trace']);
		$jsposPrePay->setTerminal_time(date("YmdHis"));
		$jsposPrePay->setTotal_fee($this->data['total_fee']);
		$jsposPrePay->setOpen_id($this->userInfo->openid);
		$jsposPrePay->setNotify_url($this->data["notify_url"]);
		$jsposPrePay->setAttach($this->data["attach"]);
		$jsposPrePay->setAccess_token($this->data['access_token']);
//		var_dump($jsposPrePay);
		$wOpt = JsPrePayDemos::jsposPrePayRe($jsposPrePay);
//		var_dump($wOpt);
		$wOpt = json_decode(json_encode($wOpt),true);
		var_dump($wOpt);exit;
		if(!is_array($wOpt) || empty($wOpt) || (!empty($wOpt['return_code']) && $wOpt['return_code'] != '01')){
			//exit("支付请求失败！请重试或联系客服");
			return array("code"=>1,"info"=>"支付请求失败！请重试或联系客服");
		}
		$wOpt["payPrice"] = $this->data['total_fee']/100;
		$wOpt["orderId"] = $this->data['terminal_trace'];
		return array("code"=>0,"info"=>$wOpt);

	}
}
?>

