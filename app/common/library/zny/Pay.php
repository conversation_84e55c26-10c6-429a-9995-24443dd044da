<?php
/**
 * <AUTHOR>
 * @copyright Copyright (c) 2017 HNDH Software Technology Co., Ltd.
 * createtime: 2017/12/11 15:08
 */

namespace Dhc\Library\zny;

class Pay
{
    const API_Url = 'https://www.aw5880.cn/pay/action';
    private $uid;
    private $token;
    private $return_url;
    private $notify_url;
    private $price;
    private $orderid;
    private $istype;

    public function __construct($config)
    {
        $this->uid = $config['merchant_no'];
        $this->token = $config['access_token'];
        $this->return_url = 'http://'.$_SERVER['HTTP_HOST'].'/wapi/Notify/zny';
        $this->notify_url = 'http://'.$_SERVER['HTTP_HOST'].'/wapi/Notify/zny';
        $this->price = sprintf('%.2f', $config['total_fee']);
        $this->orderid = $config['terminal_trace'];
        $this->istype = 'weixin' == $config['type'] ? '20001' : '10001';
    }

    public function run()
    {
        $postData = $this->getPostData();
        $result = $this->phpPost(self::API_Url, $postData);
        $result = json_decode($result, true);
        if ($result && $result['code'] == '200' ) {
            return $result["data"]["qrcode"];
        }
        return false;
    }

    private function phpPost($url, $post_data = array(), $timeout = 5, $header = "")
    {
        $header = empty($header) ? $this->defaultHeader() : $header;
        $post_string = http_build_query($post_data);
        $header .= "Content-length: " . strlen($post_string);
        $opts = array(
            'http' => array(
                'protocol_version' => '1.0',//http协议版本(若不指定php5.2系默认为http1.0)
                'method' => "POST",//获取方式
                'timeout' => $timeout,//超时时间
                'header' => $header,
                'content' => $post_string)
        );
        $context = stream_context_create($opts);
        return @file_get_contents($url, false, $context);
    }

    //默认模拟的header头
    private function defaultHeader()
    {
        $header = "User-Agent:Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9.2.12) Gecko/20101026 Firefox/3.6.12\r\n";
        $header .= "Accept:text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\r\n";
        $header .= "Accept-language: zh-cn,zh;q=0.5\r\n";
        $header .= "Accept-Charset: GB2312,utf-8;q=0.7,*;q=0.7\r\n";
        return $header;
    }

    private function getPostData()
    {
        $data = [
            'uid' => $this->uid,
            'price' => $this->price,
            'istype' => $this->istype,
            'notify_url' => $this->notify_url,
            'return_url' => $this->notify_url,
            'orderid' => $this->orderid,
            'orderuid' => '',
            'goodsname' => '在线充值',
        ];
        $data['key'] = md5($data["goodsname"] . $data["istype"] . $data["notify_url"] . $data["orderid"] . $data["orderuid"] . $data["price"] . $data["return_url"] . $this->token . $data["uid"]);
        return $data;
    }
}
