<?php
/**
 * <AUTHOR>
 * @copyright Copyright (c) 2017 HNDH Software Technology Co., Ltd.
 * @link http://www.dhsoft.cn
 */

namespace Dhc\Library;

use Dhc\Models\UserConfig;

class WxH5Pay
{
    const API_Url = 'https://api.mch.weixin.qq.com/pay/unifiedorder';
    public $appid;
    public $mch_id;
    public $notify_url;
    public $total_fee;
    public $key;

    /**
     * WxH5Pay constructor.
     */
    public function __construct()
    {
        $userConfig = UserConfig::findFirst("payType = 'wxh5' AND status = 1");
        if (empty($userConfig->merchant_no) || empty($userConfig->terminal_id)) {
            throw new Exception('支付错误，请联系客服配置参数！');
        }
        $this->appid = $userConfig->terminal_id;
        $this->mch_id = $userConfig->merchant_no;
        $this->key = $userConfig->access_token;
        $this->notify_url = 'http://'.$_SERVER['HTTP_HOST'].'/wapi/notify/wxh5';
    }

    /**
     * H5支付请求
     * @param array $data
     * @return bool|mixed
     */
    public function run($data = array())
    {
        $headers = array();
        $headers[] = 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8';
        $headers[] = 'Connection: Keep-Alive';
        $headers[] = 'Accept-Language: ru-RU,ru;q=0.8,en-US;q=0.5,en;q=0.3';
        $headers[] = 'Accept-Encoding: gzip, deflate';
        $headers[] = 'User-Agent: Mozilla/5.0 (Windows NT 6.1; rv:22.0) Gecko/20100101 Firefox/22.0';
        $dataInfo = $this->getData($data);
        $dataInfo['sign'] = $this->MakeSign($dataInfo);
        $xml = $this->data_to_xml($dataInfo);
        //$response = $this->http_post(self::API_Url,$xml);
        $response = $this->http_post(self::API_Url,$xml,$headers);
        if( !$response ){
            return false;
        }
        $result = $this->xml_to_data($response);

        if( !empty($result['result_code']) && !empty($result['err_code']) ){
            return $result['result_msg'];
        } else {
            return $result["mweb_url"];
        }
    }

    /**
     * 拼装数组
     * @param array $data
     * @return array
     */
    public function getData($data = array())
    {
        $data = [
            'appid' => $this->appid,
            'mch_id' => $this->mch_id,
            'nonce_str' => self::getNonceStr(),
            'sign' => '',
            'body' => $data['order_body'],
            'out_trade_no' => $data['terminal_trace'],
            'total_fee' => 1,
            'spbill_create_ip' => self::getIp(),
            'notify_url' =>$this->notify_url,
            'trade_type' => 'MWEB',
            //'scene_info' => json_encode(self::getSceneInfo(), JSON_UNESCAPED_UNICODE),
            'scene_info' => '{"h5_info": {"type":"Wap","wap_url": "http://kknc.bbscodes.com/","wap_name": "金币充值"}}',
        ];
        return $data;
    }

    /**
     * curl请求微信官方
     * @param string $url
     * @param array $post_data
     * @param array $header
     * @param int $timeout
     * @return mixed
     * @internal param $data
     */
    /*public function http_post($url, $data) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$url);
        curl_setopt($ch, CURLOPT_HEADER,0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $res = curl_exec($ch);
        curl_close($ch);
        return $res;
    }*/
    public function http_post($url='',$post_data=array(),$header=array(),$timeout=30) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书检查
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 从证书中检查SSL加密算法是否存在
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);

        $response = curl_exec($ch);

        curl_close($ch);

        return $response;
    }


    /**
     * 支付所需场景信息
     * @return array
     */
    public static function getSceneInfo()
    {
        $data = [
            'h5_info'=>[
                'type' =>'Wap',
                'wap_url' => 'http://'.$_SERVER['HTTP_HOST'],
                'wap_name' => '金币充值',
            ],
        ];
        return $data;
    }

    /**
     * 数组转xml
     * @param $params
     * @return bool|string
     */
    public function data_to_xml( $params ){
        if(!is_array($params)|| count($params) <= 0)
        {
            return false;
        }
        $xml = "<xml>";
        foreach ($params as $key=>$val)
        {
            if (is_numeric($val)){
                $xml.="<".$key.">".$val."</".$key.">";
            }else{
                $xml.="<".$key."><![CDATA[".$val."]]></".$key.">";
            }
        }
        $xml.="</xml>";
        return $xml;
    }

    /**
     * xml转数组
     * @param $xml
     * @return bool|mixed
     */
    public function xml_to_data($xml){
        if(!$xml){
            return false;
        }
        //将XML转为array
        //禁止引用外部xml实体
        libxml_disable_entity_loader(true);
        $data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        return $data;
    }
    /**
     * 生成随机32位字符串
     * @param int $length
     * @return string
     */
    public static function getNonceStr($length = 32)
    {
        $chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        $str ="";
        for ( $i = 0; $i < $length; $i++ )  {
            $str .= substr($chars, mt_rand(0, strlen($chars)-1), 1);
        }
        return $str;
    }


    /**
     * 拼接参数
     * @return string
     */
    public function ToUrlParams($data)
    {
        $buff = "";
        foreach ($data as $k => $v)
        {
            if($k != "sign" && $v != "" && !is_array($v)){
                $buff .= $k . "=" . $v . "&";
            }
        }
        $buff = trim($buff, "&");
        return $buff;
    }

    /**
     * 获取用户真实IP
     * @return array|false|string
     */
    public static function getIp()
    {
        $ip = $_SERVER['REMOTE_ADDR'];
        if (isset($_SERVER['HTTP_CDN_SRC_IP'])) {
            $ip = $_SERVER['HTTP_CDN_SRC_IP'];
        } elseif (isset($_SERVER['HTTP_CLIENT_IP']) && preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}$/', $_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && preg_match_all('#\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}#s', $_SERVER['HTTP_X_FORWARDED_FOR'], $matches)) {
            foreach ($matches[0] AS $xip) {
                if (!preg_match('#^(10|172\.16|192\.168)\.#', $xip)) {
                    $ip = $xip;
                    break;
                }
            }
        }
        return $ip;
    }

    /**
     * 生成签名
     * @param $data
     * @return string
     */
    public function MakeSign($data)
    {
        //签名步骤一：按字典序排序参数
        ksort($data);
        $string = $this->ToUrlParams($data);
        //签名步骤二：在string后加入KEY
        $string = $string . "&key=".$this->key;

        //签名步骤三：MD5加密
        $string = md5($string);
        //签名步骤四：所有字符转为大写
        $result = strtoupper($string);
        return $result;
    }

    /**
     * 错误提示信息
     * @param $code
     * @return mixed
     */
    public function error_code( $code )
    {
        $errList = array(
            'NOAUTH'                =>  '商户未开通此接口权限',
            'NOTENOUGH'             =>  '用户帐号余额不足',
            'ORDERNOTEXIST'         =>  '订单号不存在',
            'ORDERPAID'             =>  '商户订单已支付，无需重复操作',
            'ORDERCLOSED'           =>  '当前订单已关闭，无法支付',
            'SYSTEMERROR'           =>  '系统错误!系统超时',
            'APPID_NOT_EXIST'       =>  '参数中缺少APPID',
            'MCHID_NOT_EXIST'       =>  '参数中缺少MCHID',
            'APPID_MCHID_NOT_MATCH' =>  'appid和mch_id不匹配',
            'LACK_PARAMS'           =>  '缺少必要的请求参数',
            'OUT_TRADE_NO_USED'     =>  '同一笔交易不能多次提交',
            'SIGNERROR'             =>  '参数签名结果不正确',
            'XML_FORMAT_ERROR'      =>  'XML格式错误',
            'REQUIRE_POST_METHOD'   =>  '未使用post传递参数 ',
            'POST_DATA_EMPTY'       =>  'post数据不能为空',
            'NOT_UTF8'              =>  '未使用指定编码格式',
        );
        if( array_key_exists( $code , $errList ) ){
            return $errList[$code];
        }
    }
}