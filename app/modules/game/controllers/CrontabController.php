<?php
namespace Dhc\Modules\Game\Controllers;
use Dhc\Models\Order;
use Dhc\Models\User;
use Dhc\Models\Config;
use Dhc\Models\TradeLogs;
use Dhc\Models\UserProduct;
use Phalcon\Http\Response;

class CrontabController extends ControllerBase{
	//定时处理大盘交易未完全成功的信息
	public function returnOrderAction() {
		//获取未处理完的订单记录 200
		if(!@in_array(date("H"),array(1,2,3,4))){
			echo 2;exit;
		}
		$configInfo = Config::findFirst("key='other'");
		$info = unserialize($configInfo->value);
		if($info["isCrontab"] == 0){
			echo "3";exit;
		}
		$list = $this->getOrderList(1);
		if(empty($list)){
			echo 9;exit;
		}
		$this->db->begin();
		foreach ($list as $key=>$val){
			$res = $this->returnInfo($val);
			if($res == false){
				$this->db->rollback();
				print_r($val);
				echo 8;exit;
			}
		}
		$this->db->commit();
		echo 1;exit;
	}
	//订单处理
	function returnInfo($data){
		$order = new Order();
		$info = $order->findFirst("id='{$data['id']}'");
		if(empty($info)){
			return false;
		}
		$info->status = 1;
		$info->endtime = TIMESTAMP;
		$info->crontabtime = TIMESTAMP;
		$flag = $info->update();
		if($flag == false){
			return false;
		}
		$user = new User();
		$userInfo = $user->findFirst("id=".$data['uid']);
		if(empty($userInfo)){
			return false;
		}
		if($data["type"] == 1){//收 退金币
			$price = sprintf("%.4f",($data["number"] - $data["dealnum"])*$data["price"]);
			if($userInfo->Frozen < $price){
				$rea = $reb = $rec = true;
			}else {
				$rea = $this->db->query("UPDATE `dhc_user` SET coing=coing+" . $price . ",Frozen=Frozen+" . (-$price) . " WHERE id='{$data['uid']}' AND coing='{$userInfo->coing}' AND Frozen='{$userInfo->Frozen}'");
				$reb = $this->saveTradeOrderLogs(["uid" => $data['uid'], "num" => ($price), "mobile" => $userInfo->user, "log" => 'crontab自动撤销订单退回金币增加' . ($price), "type" => 'addcoing']);
				$rec = $this->saveTradeOrderLogs(["uid" => $data['uid'], "num" => ($price), "mobile" => $userInfo->user, "log" => 'crontab自动撤销订单退回扣除冻结金币' . ($price), "type" => 'dedfrozencoing']);
			}
		}elseif($data["type"] == 0){//卖 退果实
			$userProduct = UserProduct::findFirst("uid = {$data['uid']} and sid={$data['sid']}");
			if(empty($userProduct)){
				return false;
			}
			$number = $data["number"] - $data["dealnum"];
			if($number<=0 || $userProduct->frozen<$number){
				$rea = $reb = $rec = true;
			}else {
				$rea = $this->db->query("UPDATE `dhc_user_product` SET number=number+" . $number . ",frozen=frozen+" . (-$number) . " WHERE id='{$userProduct->id}' AND uid='{$data['uid']}' AND sid='{$data['sid']}' AND number='{$userProduct->number}' AND frozen='{$userProduct->frozen}'");
				$reb = $this->saveTradeOrderLogs(["uid" => $data['uid'], "num" => ($number), "mobile" => $userInfo->user, "log" => 'crontab自动撤销订单退回产品增加' . ($number), "type" => 'addproduct']);
				$rec = $this->saveTradeOrderLogs(["uid" => $data['uid'], "num" => ($number), "mobile" => $userInfo->user, "log" => 'crontab自动撤销订单退回扣除冻结产品' . ($number), "type" => 'dedfrozenproduct']);
			}
		}
		if($rea == false || $reb == false || $rec == false){
			return false;
		}
		return true;
	}
	//订单信息获取
	function getOrderList($true = false){
		$order = new Order();
		$endtime = strtotime(date("Ymd",TIMESTAMP));
		if($true){
			$endtime += 24*3600;
		}
		$starttime = $endtime - 24*3600;
		return $order->find(
			array(
				'conditions'	=>	"createtime>$starttime AND createtime< $endtime AND status=0 AND crontabtime=0",
				'order'			=>	'id ASC',
				'limit'			=>	'200'
			)
		)->toArray();
	}
	public function saveTradeOrderLogs($data = array()){
		$logs = new TradeLogs();
		$logs->uid = $data["uid"];
		$logs->mobile = $data["mobile"];
		$logs->num = $data["num"];
		$logs->logs = $data["log"];
		$logs->type = $data["type"];
		$logs->createtime = TIMESTAMP;
		$logs->status = 1;
		$result =  $logs->save();
		return $result;
	}
}
