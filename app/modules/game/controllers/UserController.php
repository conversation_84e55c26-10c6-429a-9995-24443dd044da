<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace Dhc\Modules\Game\Controllers;
use Dhc\Library\EmgApi;
use Dhc\Library\IDCardApi;
use Dhc\Models\OrchardDowngrade;
use Dhc\Models\User;
use Dhc\Models\Orchard;
use Dhc\Models\OrchardUser;
use Dhc\Models\OrchardGoods;
use Dhc\Models\OrchardLand;
use Dhc\Models\OrchardChest;
use Dhc\Models\OrchardChestlist;
use Dhc\Models\Product;
use Dhc\Models\UserProduct;
use MongoDB\BSON\Timestamp;
use Phalcon\Http\Response;
use Phalcon\Paginator\Adapter\Model as PaginatorModel;

class UserController extends ControllerBase{
	public $psize = 5;
	protected function initialize() {
		$this->checkToken();
		$this->config = $this->getConfig("houseInfo");
	}
	/**
	 * 用户仓库
	 */
	public function wareHouseAction(){
		$data[0] = $this->getProduct("sid");//种子信息
		$data[1] = $this->getDui();//材料
		$data[2] = $this->gemstone();//宝石
		$data[3] = $this->getprop();//道具
		$data[4] = $this->getSkin();//皮肤//背景信息
		$data = array_merge($data[0],$data[1],$data[2],$data[3],$data[4]);
		$this->ajaxResponse($data, "用户仓库信息",0);
	}
		//房屋等级
	function houseInfoAction(){
		$data = $this->getHouseInfo();
		$this->ajaxResponse($data, "房屋信息返回成功！",0);
	}
	//房屋升级
	function upHouseAction(){
		$dataInfo = $this->getHouseInfo();
		$config = $this->getConfig("houseInfo");
		$this->db->begin();
		if($dataInfo["grade"]>count($config) || $dataInfo["upgrade"]-1 >count($config)){
			$this->db->rollback();
			$this->ajaxResponse("", "暂无更高级别，无法进行升级！",1);
		}
		if($dataInfo["userInfo"]["diamonds"]<$dataInfo["upInfo"]["0"] && $dataInfo["upInfo"]["0"]>0){
			$this->db->rollback();
			$this->ajaxResponse("", "{$this->zuanshiTitle}不足，暂无法升级！",1);
		}
		//淘金乐园扣除推荐礼包
        if(USER_TYPE == 'taojin'){
		    $giftNum = $this->deductGifts($dataInfo["upgrade"]);
            if(!empty($dataInfo["upInfo"]["1"])){
                $dataInfo["upInfo"]["1"] -=$giftNum;
            }
            if(!empty($dataInfo["upInfo"]["2"])){
                $dataInfo["upInfo"]["2"] -=$giftNum;
            }
            if(!empty($dataInfo["upInfo"]["3"])){
                $dataInfo["upInfo"]["3"] -=$giftNum;
            }
        }
		if($dataInfo["userInfo"]["wood"]<$dataInfo["upInfo"]["1"] && $dataInfo["upInfo"]["1"]>0){
			$this->db->rollback();
			$this->ajaxResponse("", "木材不足，暂无法升级！",1);
		}
		if($dataInfo["userInfo"]["stone"]<$dataInfo["upInfo"]["2"] && $dataInfo["upInfo"]["2"]>0){
			$this->db->rollback();
			$this->ajaxResponse("", "石材不足，暂无法升级！",1);
		}
		if($dataInfo["userInfo"]["steel"]<$dataInfo["upInfo"]["3"] && $dataInfo["upInfo"]["3"]>0){
			$this->db->rollback();
			$this->ajaxResponse("", "钢材不足，暂无法升级！",1);
		}
		if(!empty($dataInfo["upInfo"][$this->kuguazhiId]) && $dataInfo["userInfo"]["kuguazhi"]<$dataInfo["upInfo"][$this->kuguazhiId]){
			$this->db->rollback();
			$this->ajaxResponse("", "苦瓜汁不足，暂无法升级！",1);
		}
		$user = new OrchardUser();
		$user =$user->findFirst("uid='{$this->userid}' AND grade='{$dataInfo["grade"]}'");
		if($user == false){
			$this->db->rollback();
			$this->ajaxResponse("", "会员信息获取失败，无法升级，请重试！",1);
		}
		$upGrade = $this->getConfig("upGrade");
		if($user->grade>=$upGrade && $upGrade>0){
			$this->db->rollback();
			$this->ajaxResponse("", "已达到当前最高等级，暂无法升级！",1);
		}
		$user->diamonds -= $dataInfo["upInfo"][0];
		$user->wood -= $dataInfo["upInfo"][1];
		$user->stone -= $dataInfo["upInfo"][2];
		$user->steel -= $dataInfo["upInfo"][3];
		$user->grade +=1;
		$user->updatetime = TIMESTAMP;

		if($dataInfo["upInfo"]["0"]>0){
			$flag = $this->saveOrchardLogs(array("mobile"=>$dataInfo["userInfo"]["mobile"],"types"=>"deddiamonds","nums"=>-$dataInfo["upInfo"][0],"msg"=>"房屋升级至".$dataInfo["upgrade"]."级扣除{$this->zuanshiTitle}".$dataInfo["upInfo"][0]."颗","dataInfo"=>json_encode($dataInfo)));
			if($flag == false){
				$this->db->rollback();
				$this->ajaxResponse("", "会员{$this->zuanshiTitle}日志信息更新失败，房屋升级失败，请重试！",1);
			}
		}
		if($dataInfo["upInfo"]["1"]>0){
			$flag = $this->saveOrchardLogs(array("mobile"=>$dataInfo["userInfo"]["mobile"],"types"=>"dedwood","nums"=>-$dataInfo["upInfo"][1],"msg"=>"房屋升级至".$dataInfo["upgrade"]."级扣除木材".$dataInfo["upInfo"][1]."颗","dataInfo"=>json_encode($dataInfo)));
			if($flag == false){
				$this->db->rollback();
				$this->ajaxResponse("", "会员木材日志信息更新失败，房屋升级失败，请重试！",1);
			}
		}
		if($dataInfo["upInfo"]["2"]>0){
			$flag = $this->saveOrchardLogs(array("mobile"=>$dataInfo["userInfo"]["mobile"],"types"=>"dedstone","nums"=>-$dataInfo["upInfo"][2],"msg"=>"房屋升级至".$dataInfo["upgrade"]."级扣除石材".$dataInfo["upInfo"][2]."颗","dataInfo"=>json_encode($dataInfo)));
			if($flag == false){
				$this->db->rollback();
				$this->ajaxResponse("", "会员石材日志信息更新失败，房屋升级失败，请重试！",1);
			}
		}
		if($dataInfo["upInfo"]["3"]>0){
			$flag = $this->saveOrchardLogs(array("mobile"=>$dataInfo["userInfo"]["mobile"],"types"=>"dedsteel","nums"=>-$dataInfo["upInfo"][3],"msg"=>"房屋升级至".$dataInfo["upgrade"]."级扣除钢材".$dataInfo["upInfo"][3]."颗","dataInfo"=>json_encode($dataInfo)));
			if($flag == false){
				$this->db->rollback();
				$this->ajaxResponse("", "会员钢材日志信息更新失败，房屋升级失败，请重试！",1);
			}
		}
		if(!empty($dataInfo["upInfo"][$this->kuguazhiId])){
			$flag = $this->saveOrchardLogs(array("mobile"=>$dataInfo["userInfo"]["mobile"],"types"=>"dedkuguazhi","nums"=>-$dataInfo["upInfo"][$this->kuguazhiId],"msg"=>"房屋升级至".$dataInfo["upgrade"]."级扣除苦瓜汁".$dataInfo["upInfo"][$this->kuguazhiId]."个","dataInfo"=>json_encode($dataInfo)));
			if($flag == false){
				$this->db->rollback();
				$this->ajaxResponse("", "会员苦瓜汁日志信息更新失败，房屋升级失败，请重试！",1);
			}
			$user->kuguazhi -= $dataInfo["upInfo"][$this->kuguazhiId];
		}
		if(USER_TYPE == "kk" && !empty($dataInfo["upInfo"]["nums"])){
			//会员等级房屋信息 直推人数 消费金额
			$re = $this->getKKUserHouseUpInfo($dataInfo["upInfo"]["nums"],$dataInfo["upInfo"]["price"]);
			if($re == false){
				$this->db->rollback();
				$this->ajaxResponse("", "当前状态暂不支持房屋升级！",1);
			}
		}
		$flag = $user->update();
		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("", "会员信息更新失败，房屋升级失败，请重试！",1);
		}
		$flag = $this->addLand();
		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("", "会员土地开启失败，请重试！",1);
		}
		$this->db->commit();
		$orchardUser = $user->findFirst("uid='{$this->userid}'");
        //$orchardUser = $this->object2array($orchardUser);
		$data = array(
		    "stone" => $orchardUser->stone,
            "wood" => $orchardUser->wood,
            "steel" => $orchardUser->steel,
            "diamonds" => $orchardUser->diamonds,
        );
		$this->ajaxResponse($data, "恭喜房屋等级成功升级为{$dataInfo['upgrade']}级！",0);
	}
	//获取房屋信息
	function getHouseInfo(){
		$houseInfo = $this->selectUser($this->userid, "houseInfo");
		$this->config[count($this->config)+1] = array(0=>0,1=>0,2=>0,3=>0);
		$data = $this->getDui();
		foreach ($data as $key => &$value) {
			$value["price"] = $this->config[$houseInfo["grade"]][$key+1];
		}
		$data[] = array(
			"cId"=>999,
			"tName"=>"{$this->zuanshiTitle}",
			"type"=>999,
			"depict"=>"可用来购买商城中的物品，也可兑换背景，升级房屋和土地",
			"price"=>$this->config[$houseInfo["grade"]][0],
			"num"=>$houseInfo["diamonds"]
		);
		if(USER_TYPE == "yansheng"){
			$houseInfo["kuguazhi"] = $this->selectUser($this->userid, "kuguazhi");
			$goodsInfo = $this->getOneOrchardGoodsInfo($this->kuguazhiId);
			if($houseInfo["grade"]>=count($this->config)){
				$this->config[$houseInfo["grade"]]["17"] = 0;
			}
			$data[] = array(
				"cId"=>$this->kuguazhiId,
				"tName"=>"{$goodsInfo['tName']}",
				"type"=>3,
				"depict"=>"{$goodsInfo['depict']}",
				"price"=>$this->config[$houseInfo["grade"]][$this->kuguazhiId],
				"num"=>$houseInfo["kuguazhi"]
			);
		}
		return array(
			"grade"=>$houseInfo["grade"],
			"upgrade"=>$houseInfo["grade"]>count($this->config)?"":$houseInfo["grade"]+1,
			"upInfo"=> $houseInfo["grade"]>count($this->config)?"暂无更高级别": $this->config[$houseInfo["grade"]],
			"dataInfo"=>$data,
			"userInfo"=>$houseInfo
		);
	}
	//土地升级信息
	function landUpInfoAction(){
		$data = $this->getLandUpInfo();
		$this->ajaxResponse($data, "土地升级信息返回成功！",0);
	}
	//土地升级操作
	function saveLandUpAction(){
		if($this->request->isPost()){
			$this->db->begin();
			$type = $this->request->getPost("type");
			$dataInfo = $this->getLandUpInfo();
			if(empty($type) || empty($dataInfo[$type])){
				$this->db->rollback();
				$this->ajaxResponse("", "请求失败，暂无法升级土地！",1);
			}
			if($dataInfo[$type]["needNum"]["need"] ==1 || $dataInfo[$type]["needNum"]["landId"]<0){
				$this->db->rollback();
				$this->ajaxResponse("", "请求失败，暂无土地可升级！",1);
			}
			$land = new OrchardLand();
			$landInfo = $land->findFirst("uid='{$this->userid}' AND landId='{$dataInfo[$type]["needNum"]["landId"]}'");
			if($landInfo == false){
				$this->db->rollback();
				$this->ajaxResponse("", "请求失败，土地获取失败！",1);
			}
			$landInfo->landLevel = $type;
			$landInfo->updatetime = $landInfo->optime = TIMESTAMP;
			$flag = $landInfo->update();
			if($landInfo == false){
				$this->db->rollback();
				$this->ajaxResponse("", "升级失败，土地等级升级失败！",1);
			}
			foreach ($dataInfo[$type]["cost"] as $key => $value) {
				if($value["price"]>$value["num"]){
					$this->db->rollback();
					$this->ajaxResponse("", "升级失败{$value["tName"]}数量不足！",1);
				}
				if(!empty($value["goodsId"])){
					$flag = $this->saveProduct($value["goodsId"],$value["price"],"ded");
					$flag1 = $this->saveOrchardLogs(array("mobile"=>$this->mobile,"landId"=>$dataInfo[$type]["needNum"]["landId"],"types"=>"dedgoods","landId"=>$value["goodsId"],"nums"=>-$value["price"],"msg"=>"土地升级扣除".$value["tName"].$value["price"]."颗"));
				}else{
					$flag = $this->updateUser($this->userid, "diamonds", $value["price"],"ded");
					$flag1 = $this->saveOrchardLogs(array("mobile"=>$this->mobile,"landId"=>$dataInfo[$type]["needNum"]["landId"],"types"=>"deddiamonds","nums"=>-$value["price"],"msg"=>"土地升级扣除".$value["tName"].$value["price"]."颗"));
				}
				if($flag == false || $flag1 == false){
					$this->db->rollback();
					$this->ajaxResponse("", "升级失败，更新{$value["tName"]}数量操作失败！",1);
				}
			}
			if(USER_TYPE == "chuangjin" || USER_TYPE=="jinlilai"){
				$res = $this->updateLandDown($type);
				if($res == false){
					$this->db->rollback();
					$this->ajaxResponse("", "升级失败，更新日志操作失败！",1);
				}
			}
			$this->db->commit();
			$data = array(
				"landId"=>$dataInfo[$type]["needNum"]["landId"]
			);
			$this->ajaxResponse($data, "升级成功，土地等级越高产生的果子品质越高！",0);
		}
	}
	//土地升级参数信息获取 $product冲突 修改
	function getLandUpInfo(){
		$orchard = new Orchard();
		$landTypeInfo = $orchard->getLandType();
		$landDepictInfo = $orchard->getLandDepict();
		$landUpInfo = $this->getConfig("landUpInfo");
		$houseInfo = $this->getConfig("houseInfo");
		$product = $this->getUserProductInfo("sid");
		if(empty($landUpInfo)){
			$this->ajaxResponse("", "土地升级信息暂无！",1);
		}
		$data = array();
		foreach ($landUpInfo as $key => $value) {
			$info = array();
			foreach ($value as $k => $v) {
				$arr = array(
					"num"=>"0",
					"price"=>$v["num"],
					"type"=>1
//					"unit"=>"颗",
				);
				if(!empty($v["pid"]) && !empty($product[$v['pid']])){
					$arr["num"] = $product[$v['pid']]["number"];
					$arr["goodsId"] = $arr["tId"] = $v["pid"];
					$arr["tName"] = $product[$v['pid']]["goodsName"];
					$arr["depict"] = !empty($product[$v['pid']]["depict"])?$product[$v['pid']]["depict"]:"";
				}
				if(!empty($v["pid"]) && empty($product[$v['pid']]) && empty($arr["tName"])){
					$products = new Product();
					$products = $products->findFirst("id='{$v['pid']}'")->toArray();
					$arr["goodsId"]= $arr["tId"] = $v["pid"];
					$arr["tName"] = $products["title"];
					$arr["depict"] = !empty($products["depict"])?$products["depict"]:"";
				}
				if(empty($v["pid"])){
					$arr["num"] = $this->selectUser($this->userid, "diamonds");
					$arr["tName"] = "{$this->zuanshiTitle}";
					$arr["depict"] = "可用来购买商城中的物品，也可兑换背景，升级房屋和土地";
					$arr["tId"] = "999";
					$arr["type"] = "999";
//					if($arr["userNums"]>10000){
//						$arr["userNums"] = sprintf("%.2f",$arr["userNums"]/1000);
//						$arr["unit"] = "万颗";
//					}
				}
				$info[$k] = $arr;
			}
			$data[$key] = array(
				"name"=>$landTypeInfo[$key],
				"type"=>8,
				"depict"=>$landDepictInfo[$key],
				"level"=>$key,
				"tId"=>$key,
				"countLand"=>count($houseInfo)+1,
				"landNum"=>$this->selectLandNums($key),
				"needNum"=>$this->needLandNums($key),
				"cost"=>$info
			);
		}
		return $data;
	}
	//查询该类型土地数量
	function selectLandNums($key){
		if($key<1 || $key>6){
			return 0;
		}
		$orchard = new OrchardLand();
		$landNums = $orchard->find("uid='{$this->userid}' AND landLevel>={$key}");
		$landNum = $this->object2array($landNums);
		return count($landNum);
	}
	//查询有没有土地可升级
	function needLandNums($key){
		$isOpen = 1;
		if($key ==4){
			$isOpen = 0;
			$grade = array(
				"6"=>2,
				"7"=>4,
				"8"=>6,
				"9"=>8,
				"10"=>10,
				"11"=>12,
				"12"=>12
				);
			$userGrade = $this->selectUser($this->userid, "grade");
			if($userGrade>=6){
				$orchard = new OrchardLand();
				$landInfo = $orchard->find("uid='{$this->userid}' AND landLevel>={$key}");
				$landInfo = $this->object2array($landInfo);
				if(count($landInfo)<$grade[$userGrade]){
					$isOpen = 1;
				}
			}
		}elseif(@in_array($key,array(5))){
			$isOpen = 0;
			$userGrade = $this->selectUser($this->userid, "grade");
			if($userGrade>=7){
				$isOpen = 1;
			}
		}
		$orchard = new OrchardLand();
		$landLevel = $key-1;
		$landNums = $orchard->findFirst("uid='{$this->userid}' AND landLevel={$landLevel} ORDER by landId asc");
		if($isOpen ==0 || $landNums == false){
			return array(
				"need"=>1,
				"needInfo"=>"暂无可升级的土地",
				"landId"=>0
			);
		}else{
			return array(
				"need"=>0,
				"needInfo"=>"有土地可进行升级",
				"landId"=>$landNums->landId
			);
		}
	}
	//点击宝箱返回信息
	function chestCheckAction(){
		$data = $this->checkChest();
		if($data == false || $this->is_error($data)){
			$this->ajaxResponse("", "宝箱返回信息失败！{$data["message"]}",1);
		}
		unset($data['award']);
		$this->ajaxResponse($data, "宝箱返回信息成功！",0);
	}
	//邀请人 宝箱 启用
	function chestSendAction(){
		$this->db->begin();
		$data = $this->checkChest();
		if($data == false || $this->is_error($data)){
			$this->db->rollback();
			$this->ajaxResponse("", "宝箱开启失败！{$data["message"]}",1);
		}
		$flag = $this->sendChest($data);
		if($this->is_error($flag) || $flag == false){
			$this->db->rollback();
			$this->ajaxResponse("", "抱歉！{$flag["message"]}",1);
		}
		$this->db->commit();
		$data["chestList"] = $this->getChestList($this->request->getPost("tId"));
		$this->ajaxResponse($data, "宝箱邀请人发送成功！",0);
	}
	//超级宝箱开启
	function chestSuperOpenAction(){
		$oneInfo = $this->onoInfo();
		$onoTitleInfo = $this->onoTitleInfo();
		$this->db->begin();
		$tId = $this->request->getPost("tId");
		$id = $this->request->getPost("id");
		$user = new OrchardUser();
		$userInfo = $user->findFirst("uid='{$this->userid}'");
		if($userInfo == false){
			$this->db->rollback();
			$this->ajaxResponse("", "用户信息获取失败！",1);
		}
		if(empty($id)){
			$chest = new OrchardChest();
			$item = $chest->findFirst("uid='{$this->userid}' AND tId='{$tId}' AND status=2 AND nums>0");
			if($item == false){
				$this->db->rollback();
				$this->ajaxResponse("", "宝箱信息获取失败1！",1);
			}
			$id = $item->id;
			$item->nums -= 1;
			$item->status = 1;
			$item->updatetime = TIMESTAMP;
			$flag = $item->update();
			if($flag == false){
				$this->db->rollback();
				$this->ajaxResponse("", "宝箱信息更新失败！",1);
			}

			$flag = $this->saveOrchardLogs(array("mobile"=>$userInfo->mobile,"types"=>"ded".$oneInfo[$item->tId."3"],"nums"=>-1,"msg"=>"开启宝箱扣除{$onoTitleInfo[$oneInfo[$item->tId."3"]]}1"));
			if($flag == false){
				$this->db->rollback();
				$this->ajaxResponse("", "宝箱信息更新失败！",1);
			}
			$model = $oneInfo[$item->tId."3"];
			$userInfo->$model -=1;
			$userInfo->updatetime = TIMESTAMP;
			$flag = $userInfo->update();
			if($flag == false){
				$this->db->rollback();
				$this->ajaxResponse("", "用户信息更新失败！",1);
			}
			$condition = " cid='{$id}'";
		}else{
			$condition = " id='{$id}'";
		}
		$chestlist = new OrchardChestlist();
		$list = $chestlist->findFirst("$condition AND uid='{$this->userid}' AND tId='{$tId}' AND price>0 AND status=1");
		if($list == false){
			$this->db->rollback();
			$this->ajaxResponse("", "宝箱尚未开启，请耐心等待！",1);
		}
		$price = $list->price;
		$list->status = 2;
		$list->isRed = 1;
		$list->updatetime = TIMESTAMP;
		$flag = $list->update();

		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("", "宝箱信息更新失败！",1);
		}
		//奖金领取
		$flag = $this->saveOrchardLogs(array("mobile" => $userInfo->mobile, "types" => "addcoing", "msg" => "宝箱开启获得果园币".$price, "nums" => $price, "dataInfo" => json_encode(array("info" => $this->object2array($list)))));
		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("", "奖金信息更新失败！",1);
		}
		//金币增加
		$user = new User();
		$user = $user->findFirst("id='{$this->userid}'");
		$user->coing += $price;
		$flag = $user->update();
		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("", "奖金信息更新失败!！",1);
		}
		$oneInfo = $this->onoInfo();
		$onoTitleInfo = $this->onoTitleInfo();
		$message = "恭喜[{$userInfo->nickname} 的家园]开启".$onoTitleInfo[$oneInfo[$tId."3"]]."获得果园币".$price;
		$flag = $this->saveDoubleEffect(array("mark"=>$oneInfo[$tId."3"],"types"=>$tId,"nums"=>$price,"msg"=>$message));
		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("", "宝箱开启日志更新失败!！",1);
		}
		$this->db->commit();
		$this->ajaxResponse($price, $message,0);
	}
	//宝箱点击开启
	function chestOpenAction(){
		$this->db->begin();
		$this->db->query("SELECT * FROM `dhc_orchard_user` WHERE uid='{$this->userid}' FOR UPDATE")->fetch();
		$data = $this->checkChest();
		if($data == false || $this->is_error($data)){
			$this->db->rollback();
			$this->ajaxResponse("", "宝箱开启失败！{$data["message"]}",1);
		}
		$flag = $this->chestOpen($data);
		if($this->is_error($flag) || $flag == false){
			$this->db->rollback();
			$this->ajaxResponse("", "抱歉！{$flag["message"]}",1);
		}
		$this->db->commit();
		$this->ajaxResponse(array("fruit"=>$flag["datafruit"],"coin"=>$flag["datacoin"]), $flag["message"], 0);
	}
	//邀请好友
	function sendChest($data){
		$cost = $data["cost"];
		$uidStr = trim($this->request->getPost("uidStr"));
		$uidList = explode(",",$uidStr.",{$this->userid}");
//		if(count($uidList) < $cost[0][0] || $cost[0][0]<=0 || $cost[0][1]<=0){
//			return $this->error(1,"宝箱信息暂无法开启");
//		}
		$tId = $this->request->getPost("tId");
		$chest = new OrchardChest();
		$item = $chest->findFirst("uid='{$this->userid}' AND tId='{$tId}' AND nums>0");
		if($item == false){
			return $this->error(1,"宝箱信息获取失败");
		}
		$chestlist = new OrchardChestlist();
		$isOpen = $chestlist->findFirst("uid='{$this->userid}' AND tId='{$tId}' AND status =1 AND cid='{$item->id}' AND price>0");
		if(!empty($isOpen)){
			return $this->error(1,"存在未领取的宝箱，无法为开启下一个宝箱邀请好友！");
		}
		//检测是否可邀请
//		$flag = $this->chestOpenInfo($item->id,1);
//		if($flag == false || $this->is_error($flag)){
//			return $this->error(1,"暂无法邀请".$flag["message"]);
//		}
//		$item->status = $flag;
		$item->uidStr = $uidStr;
		$item->updatetime = TIMESTAMP;
		if($item->endtime< TIMESTAMP || $item->status ==2){
			$item->status =1;
			$item->starttime = TIMESTAMP;
			$item->endtime = TIMESTAMP + $cost[0][1];
		}
		$flag = $item->update();
		if($flag == false){
			return $this->error(1,"信息同步失败！");
		}
		foreach ($uidList as $key=>$uid){
			$chestList = new OrchardChestList();
			$uList = $chestList->findFirst("uid='{$uid}' AND tId='{$tId}' AND disUid='{$this->userid}' AND cid='{$item->id}' AND createtime='{$item->starttime}'");
			if($uList == false){
				$chestList->uid = $uid;
				$user = $this->selectUser($uid,"user");
				if($user == false){
					return $this->error(1,"会员信息{$uid}获取失败");
				}
				$chestList->tId = $tId;
				$chestList->cid = $item->id;
				$chestList->disUid = $this->userid;
				$chestList->starttime = $item->starttime;
				$chestList->endtime = $item->starttime + $cost[0][1];
				$chestList->createtime = $item->starttime;
				if($uid ==  $this->userid){
					$chestList->status = 1;
				}else{
					$chestList->status = 0;
				}
				$chestList->updatetime = TIMESTAMP;
				$flag = $chestList->save();
				if($flag ==  false){
					return $this->error(1,"邀请好友".$uid."失败！");
				}
			}
		}
		return true;
	}
	//宝箱信息
	function checkChest(){
		if($this->request->isPost()){
			$data = array();
			$mark = $this->request->getPost("mark");
			$tId = $this->request->getPost("tId");
			$marks = [4 => "cchest", 5 =>"schest", 6 => "gchest", 7 => "dchest",18=>"ccchest",19=>"cschest",20=>"cgchest"];
			//	"183" => "ccchest",//超级铜宝箱
//			"193" => "cschest",//超级银宝箱
//			"203" => "cgchest",//超级金宝箱
			if(empty($mark) || empty($tId) || $marks[$tId] != $mark){
				return $this->error(1,"宝箱信息不存在");
			}

			$markNums = $this->selectUser($this->userid, $mark);
			if($markNums<=0){
				return $this->error(1,"宝箱数量不足无法开启");
			}
			$goods = $this->getOneOrchardGoodsInfo($tId);
			if(empty($goods)){
				return $this->error(1,"宝箱信息异常无法开启");
			}
			$chanceInfo = json_decode($goods["chanceInfo"],true);
			$award = $chanceInfo["winning"];
			$cost = json_decode($goods["cost"],true);
			if(empty($award) || empty($cost)){
				return $this->error(1, "宝箱尚未设置奖励！");
			}
			$data['desc'] = $chanceInfo['desc'];
			$data["award"] = $award;
			$data["cost"] = $cost;
			if(@in_array($tId,array(18,19,20))) {
				$data["chestList"] = $this->getChestList($tId);
			}else{
				foreach ($data["award"] as $key => $val) {
					if (is_numeric($val['goodsId'])) {
						$goodsInfo = $this->getOneProductInfo($val['goodsId']);
						if (!empty($goodsInfo["title"]) > 0) {
							$data["award"][$key]["goodsName"] = $goodsInfo["title"];
						} else {
							$data["award"][$key]["goodsName"] = "";
						}
					} else if ($val['goodsId'] == "coin") {
						$data["award"][$key]["goodsName"] = "金币";
					}
				}

				foreach ($data["cost"] as $key => $val) {
					if ($val[0] > 0 && $val[1] > 0) {
						$goodsInfo = $this->getOneProductInfo($val[0]);
						if (!empty($goodsInfo["title"]) > 0) {
							$data["cost"][$key]["goodsName"] = $goodsInfo["title"];
						} else {
							$data["cost"][$key]["goodsName"] = "";
						}
					}
				}
			}
			return $data;
		}
		return false;
	}
	//邀请人信息
	function getChestList($tId){
		$chest = new OrchardChest();
		$chest = $chest->findFirst("uid='{$this->userid}' AND tId='{$tId}'");
		if($chest == false){
			return $this->error(1,"宝箱信息存在异常！");
		}
		$chestList = new OrchardChestlist();
		$list = $chestList->find(array(
			'conditions'	=>	"disUid='{$this->userid}' AND tId='{$tId}' AND uid !='{$this->userid}' AND cid='{$chest->id}' AND starttime='{$chest->starttime}' AND status in(0,1,2)",
			'columns'		=>	'starttime,uid,status,id,cid,endtime,price',
			'order'			=>	"id asc"
		))->toArray();
		if(!empty($list)){
			$statusInfo = array("已邀请","已同意","已开启",9=>"已取消",8=>"已返还");
			foreach ($list as $key=>&$val){
				$nickname = $this->selectUser($val["uid"],"nickname");
				$val["nickname"] = !empty($nickname)?$nickname:"获取失败";
				$val["statusInfo"] = $statusInfo[$val['status']];
			}
		}else{
			$list = array();
		}
		return $list;
	}
	//被邀请记录返回
	function getCheskListsAction() {
		$data = $this->getCheskLists();
		$this->ajaxResponse($data, "被邀请开宝箱信息",0);
	}
	function  getCheskLists(){
		$pindex = max(1,$this->request->getPost("page"));
		$list = new OrchardChestlist();
		$lists = $list->find(array(
			'conditions'	=>	"uid = '{$this->userid}'  AND disUid !='{$this->userid}' AND ( endtime>=".TIMESTAMP . " or status in (0,1,2))",
			'columns'		=>	'id,uid,tId,status,disUid,endtime,cid,price',
			'order'			=>	"id desc",
			'limit'=>'100'
		));
		$paginator = new PaginatorModel(array("data"	=> $lists,"limit"	=>$this->psize,"page" =>$pindex));
		$page = $paginator->getPaginate();
		$data = array();
		$list = $this->object2array($page);
		if(!empty($list["items"])){
			foreach ($list["items"] as $key =>&$value) {
				$nickname = $this->selectUser($value["disUid"],"nickname");
				$value["nickname"] = $nickname?$nickname:"获取失败";
				$chest = new OrchardChest();
				$chest = $chest->findFirst("id='{$value['cid']}'");
				$value["price"] = $chest->price?$chest->price:"获取失败";
//				$goods = $this->getOneOrchardGoodsInfo($value["tId"]);
//				$value["tName"] = $goods["tName"];
			}
		}
		$data['list'] = $list["items"];
		$data['curPage'] = $list["current"];
		$data['totalPage'] = $list["total_pages"];
		return $data;
	}
	//点击同意 加入宝箱开启
	function addChestInfoAction(){
		if($this->request->isPost()){
			$oneInfo = $this->onoInfo();
			$onoTitleInfo = $this->onoTitleInfo();
			$id = $this->request->getPost("id");
			$chestList = new OrchardChestlist();
			$chestList = $chestList->findFirst("id='{$id}'");
			if(empty($chestList) || $chestList->status != 0){
				$this->ajaxResponse("", "宝箱信息暂无法同意邀请",1);
			}
			$tName = $onoTitleInfo[$oneInfo[$chestList->tId."3"]];
			if(empty($tName)){
				$this->ajaxResponse("", "宝箱信息获取失败2！",1);
			}
			if($chestList->endtime<TIMESTAMP){
				$this->ajaxResponse("", "宝箱信息已到期，无法开启！",1);
			}
			$chest = new OrchardChest();
			$chest = $chest->findFirst("id='{$chestList->cid}'");
			if($chest == false || $chest->price<=0){
				$this->ajaxResponse("", "宝箱信息获取失败3！",1);
			}
			$this->db->begin();
			$chestList->updatetime = TIMESTAMP;
			$chestList->status =1;
			$chestList->isRed =1;
			$flag = $chestList->update();
			if($flag == false){
				$this->db->rollback();
				$this->ajaxResponse("","宝箱信息更新失败4！",1);
			}
			$user = new \Dhc\Models\OrchardUser();
			$user = $user->findFirst("uid='{$this->userid}'");
			if($user == false){
				$this->db->rollback();
				$this->ajaxResponse("","用户信息获取失败",1);
			}
			if($user->diamonds< $chest->price){
				$this->db->rollback();
				$this->ajaxResponse("","用户钻石不足",1);
			}
			$user->diamonds -= $chest->price;
			$user->updatetime = TIMESTAMP;
			$flag = $user->update();
			if($flag == false){
				$this->db->rollback();
				$this->ajaxResponse("","金额不足，无法开启宝箱！",1);
			}
			$flag = $this->saveOrchardLogs(array("mobile"=>$user->mobile,"types"=>"deddiamonds","nums"=> -$chest->price,"msg"=>"开启宝箱".$tName."扣除{$this->zuanshiTitle}".$chest->price));
			$this->db->commit();
			$data = $this->getCheskLists();
			$this->ajaxResponse($data,"已接受邀请，点击查看详情！",0);
		}
	}
	//查看宝箱邀请信息
	function getChestInfoAction(){
		$id = $this->request->getPost("id");
		$chestlist = new OrchardChestlist();
		$list = $chestlist->findFirst("id='{$id}' AND uid='{$this->userid}'");
		if(empty($list) || empty($id)){
			$this->ajaxResponse("", "宝箱信息获取失败",1);
		}
		$list = $chestlist->find(array(
			'conditions'	=>	"disUid='{$list->disUid}' AND tId='{$list->tId}' AND cid='{$list->cid}' AND starttime='{$list->starttime}' AND status in(0,1,2)",
			'columns'		=>	'starttime,uid,status,id,cid,endtime,price',
			'order'			=>	"id asc"
		))->toArray();
		if(!empty($list)){
			$statusInfo = array("已邀请","已同意","已开启",9=>"已取消",8=>"已返还");
			foreach ($list as $key=>&$val){
				$nickname = $this->selectUser($val["uid"],"nickname");
				$val["nickname"] = !empty($nickname)?$nickname:"获取失败";
				$val["statusInfo"] = $statusInfo[$val['status']];
			}
		}else{
			$list = array();
		}
		$this->ajaxResponse($list, "开宝箱信息返回",0);
	}
	//宝箱开启效果
	function chestOpen($data){
		$cost = $data["cost"];
		$award = $data["award"];
		foreach ($cost as $key => $val){
			if($val[0] > 0 && $val[1] > 0){
				$flag = $this->saveProduct($val[0], $val[1], "ded");
				if($flag == false){
					return $this->error(1,"宝箱开启失败！{$val["goodsName"]}数量不足!");
				}
				$flag = $this->saveOrchardLogs(array("mobile"=>$this->mobile,"types"=>"dedgoods","landId"=>$val[0],"nums"=>-$val[1],"msg"=>"开启宝箱减少".$val["goodsName"].$val[1]."颗"));
				if($flag == false){
					return $this->error(1,"宝箱开启失败！{$val["goodsName"]}日志记录更新失败!");
				}
			}
		}

		foreach ($award as $key => $val){
			if($val['min'] > 0 && $val['max'] > 0 && $val['min'] <= $val['max']){
				$award[$key]['num'] = rand($val['min'], $val['max']);
			}else{
				unset($award[$key]);
			}
		}
		if (!empty($award)) {
			//道具数量更新
			$user = new OrchardUser();
			$user = $user->findFirst("uid='{$this->userid}'");
			$mark = $this->request->getPost("mark");
			if ($user->$mark <= 0) {
				return $this->error(1, "{$data['title']}数量不足无法开启！");
			}
			$user->$mark -= 1;
			$user->updatetime = TIMESTAMP;
			$flag = $user->update();
			if ($flag == false) {
				return $this->error(1, "{$data['title']}数量更新失败！");
			}
			$mark = $this->request->getPost("mark");
			$markTitle = $this->onoTitleInfo();
			// 更新奖品
			$message = "开启{$markTitle[$mark]},获得";
			$datacoin = 0;
			$datafruit = array(
			);
			foreach ($award as $key => $val) {
				if (is_numeric($val['goodsId'])) {
					$flag = $this->saveProduct($val['goodsId'], $val['num']);
					if ($flag == false) {
						return $this->error(1, "宝箱开启失败，{$val["goodsName"]}更新失败！");
					}
					$flagData = [
						"mobile" => $this->mobile,
						"types" => "addgoods",
						"nums" => $val["num"],
						"landId" => $val['goodsId'],
						"msg" => "开启宝箱获得奖励" . $val["goodsName"] . "数量" . $val['num']
					];
					//创金开出种子
					if($val["goodsId"] ==$this->seedId){
						$flagData["landId"] = 0;
						$flagData["types"] = "addseed";
					}
					$flag = $this->saveOrchardLogs($flagData);
					if ($flag == false) {
						return $this->error(1, "宝箱开启失败，{$val["goodsName"]}更新失败！");
					}
					$message .=  $val['num']."个".$val["goodsName"];
					$data["message"] = "宝箱开启成功，获得" . "个" . $val["goodsName"] . "，数量：" . $val['num'];
					$datafruit[] = array($val["goodsId"],$val["num"]);

				} else if ($val['goodsId'] == "coin") {
					$userModel = new User();
					$userInfo = $userModel->findFirst("id={$this->userid}");
					if($userInfo == false){
						return $this->error(1, "用户信息获取失败，请重试！");
					}
					$userInfo->coing += $val['num'];
					$flag = $userInfo->save();
					if($flag == false){
						return $this->error(1, "用户金币添加失败, 请重试！");
					}
					$message .= $val['num']."个金币";
					$datacoin = $val["num"];
				}
			}
		} else {
			return $this->error(1, "宝箱开启失败，没有获得任何奖励！");
		}
		$flag = $this->saveDoubleEffect(array("mark"=>$this->request->getPost("mark"),"types"=>$this->request->getPost("tId"),"nums"=>$val["num"],"msg"=>"恭喜[{$user->nickname} 的家园]".$message));
		if($flag == false){
			return $this->error(1,"宝箱开启日志更新失败！");
		}
		return $data = ["message"=>"恭喜您".$message,"datafruit"=>$datafruit,"datacoin"=>$datacoin];

//		if($data["openInfo"]["id"]>0 && $data["openInfo"]["num"]>0){
//			$flag = $this->saveProduct($data["openInfo"]["id"], $data["openInfo"]["num"], "ded");
//			if($flag == false){
//				return $this->error(1,"{$data['title']}开启失败！{$data["openInfo"]["goodsName"]}记录更新失败!");
//			}
//			$flag = $this->saveOrchardLogs(array("mobile"=>$this->mobile,"types"=>"dedgoods","landId"=>$data["openInfo"]["id"],"nums"=>-$data["openInfo"]["num"],"msg"=>"开启{$data['title']}减少".$data["openInfo"]["goodsName"].$data["openInfo"]["num"]."颗"));
//			if($flag == false){
//				return $this->error(1,"{$data['title']}开启失败！{$data["openInfo"]["goodsName"]}日志记录更新失败!");
//			}
//		}
//		if(!@in_array($data["winning"]["type"],array(1,2,3))){
//			return $this->error(1,"{$data['title']}开启失败！!");
//		}
//		//道具数量更新
//		$user = new OrchardUser();
//		$user = $user->findFirst("uid='{$this->userid}'");
//		$mark = $this->request->getPost("mark");
//		if($user->$mark <=0){
//			return $this->error(1,"{$data['title']}数量不足无法开启！");
//		}
//		$user->$mark -=1;
//		$user->updatetime = TIMESTAMP;
//		$flag = $user->update();
//		if($flag == false){
//			return $this->error(1,"{$data['title']}数量更新失败！");
//		}
//		$mt = rand(1,$data["success"]);
//		if($mt != 1){
//			$data["message"] = "{$data['title']}开启成功，未获得额外效果！";
//			return $data;
//		}
//		if($data["winning"]["type"] ==1){
//			$nums = rand($data["winning"][1][1], $data["winning"][1][2]);
//			if($nums<=0){
//				$data["message"] = "{$data['title']}开启成功，未获得更多{$data["winning"][1]["goodsName"]}！";
//				return $data;
//			}
//			$flag = $this->saveProduct($data["winning"][1][0], $nums);
//			if($flag == false){
//				return $this->error(1,"{$data['title']}开启失败，{$data["winning"][1]["goodsName"]}更新失败！");
//			}
//			$flag = $this->saveOrchardLogs(array("mobile"=>$this->mobile,"types"=>"addgoods","nums"=>$nums,"landId"=>$data["winning"][1][0],"msg"=>"开启{$data['title']}获得果实".$data["winning"][1]["goodsName"].$nums."颗"));
//			if($flag == false){
//				return $this->error(1,"{$data['title']}开启失败，{$data["winning"][1]["goodsName"]}更新失败！");
//			}
//			$data["message"] = "{$data['title']}开启成功，获得".$nums.$data["winning"][1]["goodsName"];
//		}elseif($data["winning"]["type"] ==2){
//			$nums = rand($data["winning"][2][1], $data["winning"][2][2]);
//			if($nums<=0){
//				$data["message"] = "{$data['title']}开启成功，未获得更多{$data["winning"][2]["goodsName"]}！";
//				return $data;
//			}
//			$flag = $this->updateUser($this->userid, "diamonds", $nums);
//			if($flag == false){
//				return $this->error(1,"{$data['title']}开启失败，{$data["winning"][2]["goodsName"]}更新失败！");
//			}
//			$flag = $this->saveOrchardLogs(array("mobile"=>$this->mobile,"types"=>"adddiamonds","nums"=>$nums,"msg"=>"开启{$data['title']}获得{$data["winning"][2]["goodsName"]}".$nums));
//			if($flag == false){
//				return $this->error(1,"{$data['title']}开启失败，{$data["winning"][2]["goodsName"]}日志更新失败！");
//			}
//			$data["message"] = "{$data['title']}开启成功，获得".$nums.$data["winning"][2]["goodsName"];
//		}elseif($data["winning"]["type"] ==3){
//			$nums = rand($data["winning"][3][1], $data["winning"][3][2]);
//			if($nums<=0){
//				$data["message"] = "{$data['title']}开启成功，未获得果实翻倍效果！";
//				return $data;
//			}
//			//宝箱翻倍效果
//			$data["message"]  = "{$data['title']}开启成功，获得".$nums."倍果实收益效果";
//		}
//		$flag = $this->saveDoubleEffect(array("mark"=>$data["mark"],"types"=>$data["winning"]['type'],"nums"=>$nums,"msg"=>$data["message"]));
//		if($flag == false){
//			return $this->error(1,"{$data['title']}开启失败！");
//		}
//		return $data;
	}

	//获取会员编号 EMG
	function getUserCodeAction(){
		$EMG = new EmgApi();
		$userCode = json_decode($EMG->getUserCode(),true);
		$this->ajaxResponse($userCode["userCode"], "新注册编号返回", 0);
	}
	//内部注册 EMG
	function regAction(){
		if ($this->request->isPost()) {
			$EMG = new EmgApi();
			$userCode = $this->request->getPost("userCode");
			if (empty($userCode)) {
				$this->ajaxResponse('', '用户编号获取失败', '1');
			}
			$userInfoCode = $this->selectUserInfo($this->userid,"userCode");
			if (empty($userInfoCode)){
				$this->ajaxResponse('', '当前用户暂不支持操作', '1');
			}
			$username= $this->request->getPost("username");
			if (strlen($username) != 11) {
				$this->ajaxResponse('', '请输入十一位手机号', '1');
			}
			//真实姓名
			$realname = $this->request->getPost("realname");
			if(empty($realname)){
				$this->ajaxResponse('', '真实姓名不可为空', '1');
			}
			//用户信息读取
			$user = new User();
			$result1 = $user->findFirst("user = '$username' ORDER BY id desc");
			if ($result1) {
				$this->ajaxResponse('', $username . '该用户已存在!无法注册', '1');
			}
			//身份证号
			$idcard = $this->request->getPost("idCard");
			if(empty($idcard)){
				$this->ajaxResponse('', '请输入正确的身份证号码', '1');
			}
			// @zl 添加身份证实名认证
			$idCardApi = new IDCardApi($realname, $idcard);
			if (!$idCardApi->check()) {
				$this->ajaxResponse('', '姓名和身份证不匹配，请重新填写！', '1');
			}
			//两次密码
			$password =  $this->request->getPost("password");
			$repassword =  $this->request->getPost("rePassword");
			if(empty($password) || empty($repassword)){
				$this->ajaxResponse("", "密码不可为空!",1);
			}
			if($password != $repassword){
				$this->ajaxResponse("", "两次密码输入不一致!",1);
			}
			$payPassword = $this->request->getPost("payPassword");

			//推荐人
			$spread = $this->request->getPost("spread");
			if($spread>0){
				$spreadUser = $user->findFirst("id = $spread");
				if(empty($spreadUser)){
					$this->ajaxResponse("", "推荐人信息不存在或已被禁用!",1);
				}
				$parentCode = $spreadUser->userCode;
				if(empty($parentCode)){
					$this->ajaxResponse('', '推荐人信息获取失败！', '1');
				}
				$user->superior = $spread."-".$spreadUser->superior;
			}
			//安置人
			$managerCode = $this->request->getPost("anzhiren");
			if($managerCode>0){
				$managerCodeUser = $user->findFirst("id = $spread");
				if(empty($managerCodeUser)){
					$this->ajaxResponse("", "安置人信息不存在或已被禁用!",1);
				}
			}
			$user->superior = $spread."-".$spreadUser->superior;
			$salt = $this->create_salt(6);
			$userpassword = sha1($salt . $password);
			$user->user = $username;
			$user->nickname = $user->realname  = $realname;
			$user->idcard = $idcard;
			$user->salt = $salt;
			$user->password = $userpassword;
			$user->createTime = TIMESTAMP;
			$user->lasttime = TIMESTAMP;
			$user->token = TIMESTAMP;
			$user->userCode = $userCode;
			$this->db->begin();
			$result = $user->save();
			if($result == false){
				$this->db->rollback();
				$this->ajaxResponse("","注册失败",1);
			}
			$data = [
				'userCode' => $userCode,
				'username' => $realname,
				'userPhone' => $username,
				'parentCode' => $userInfoCode,
				"managerCode"=>$managerCode,
				"regUserCode"=>$userInfoCode,
				"payPassword"=>md5($payPassword),
				"idcard"=>$idcard,
				'password' => $password,
			];
			$re = json_decode($EMG->register($data),true);
			if($re["Code"] != 1){
				$this->db->rollback();
				$this->ajaxResponse("","注册会员失败！[{$re['Message']}]",1);
			}
			$this->db->commit();
			$this->ajaxResponse("","注册成功！",0);
		}
	}
	//外部连接 EMG
	function getThirdPartyLoginAction(){
		if ($this->request->isPost()) {
			$EMG = new EmgApi();
			$userInfoCode = $this->selectUserInfo($this->userid,"userCode");
			if (empty($userInfoCode)){
				$this->ajaxResponse('', '当前用户暂不支持操作', '1');
			}
			$url = array(
				1=>"/Member/Home/User",
				2=>"/Member/Home/Account",
				3=>"/Member/Home/RegManager",
				4=>"/Member/Home/OptionManager",
				5=>"/Member/Home/IntegralManager",
				6=>"/Member/Home/MessageManager",
			);
			$url = $EMG->returnUrl(array(
				"userCode"=>$userInfoCode,
				"password"=> $this->request->getPost("password"),
				"returnUrl"=>$url[$this->request->getPost("type")]
			));
			$this->ajaxResponse($url, '连接请求返回', 0);
		}
	}
}
