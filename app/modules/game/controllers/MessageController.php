<?php
namespace Dhc\Modules\Game\Controllers;
use Dhc\Models\Config;
/**
 * 发送短信验证
 * @param $data 发送内容
 * @param $to  发送地址
 * return  返回状态
 * Time: 9:36
 */
class MessageController extends ControllerBase{
	public $sendMessage="";

	protected function initialize() {
		$this->checkToken();
	}
	/**
	 * 发送post请求
	 * @param string $url 请求地址
	 * @param array $post_data post键值对数据
	 * @return string
	 */
	public function sendAction($mobile,$content){
		if (empty($content)){
			$this->ajaxResponse('error','请求类型错误','1');
		}
		$config = new Config();
		$messageInfo = $config->findFirst("key = 'message'");
		$this->sendMessage = unserialize($messageInfo->value);
		$post_data = array();
		$post_data['userid'] = $this->sendMessage["userid"];
		$post_data['account'] =$this->sendMessage["account"];
		$post_data['password'] = $this->sendMessage["password"];
		$post_data['mobile']	=$mobile;
		$post_data['content']	=$content."【{$this->sendMessage["sign"]}】";
		$url=$this->sendMessage["url"];
		$o='';
		foreach ($post_data as $k=>$v)
		{
			$o.="$k=".urlencode($v).'&';
		}
		$post_data=substr($o,0,-1);
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_URL,$url);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
		//curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //Èç¹ûÐèÒª½«½á¹ûÖ±½Ó·µ»Øµ½±äÁ¿Àï£¬ÄÇ¼ÓÉÏÕâ¾ä¡£
		$result = curl_exec($ch);
		$result = $this->xmlToArray($result);
		if ($result['returnstatus'] == "Success") {
			return true;
		} else {
			return $result['message'];
		}
	}
	public function oneAction(){
		$post_data = array();
		$post_data['userid'] = $this->sendMessage["userid"];
		$post_data['account'] = $this->sendMessage["account"];
		$post_data['password'] = $this->sendMessage["password"];
		$url='http://120.24.238.58:8888/sms.aspx?action=overage';
		$o='';
		foreach ($post_data as $k=>$v)
		{
			$o.="$k=".urlencode($v).'&';
		}
		$post_data=substr($o,0,-1);
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_URL,$url);
//		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
//curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //Èç¹ûÐèÒª½«½á¹ûÖ±½Ó·µ»Øµ½±äÁ¿Àï£¬ÄÇ¼ÓÉÏÕâ¾ä¡£
		$result = curl_exec($ch);

	}
	public function twoAction(){
		$post_data = array();
		$post_data['userid'] = $this->sendMessage["userid"];
		$post_data['account'] = $this->sendMessage["account"];
		$post_data['password'] = $this->sendMessage["password"];
		$url='http://120.24.238.58:8888/sms.aspx?action=overage';
		$o='';
		foreach ($post_data as $k=>$v)
		{
			$o.=urlencode("$k=".$v).'&';
		}
		$post_data=substr($o,0,-1);
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_URL,$url);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
//curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //Èç¹ûÐèÒª½«½á¹ûÖ±½Ó·µ»Øµ½±äÁ¿Àï£¬ÄÇ¼ÓÉÏÕâ¾ä¡£
		$result = curl_exec($ch);
	}
	private function xmlToArray($xml) {
		//禁止引用外部xml实体
		libxml_disable_entity_loader(true);
		$values = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
		return $values;
	}

	//消息 通知
	public function getMessageAction(){
		$time1 = time()-10*60;
		$timing = intval($this->request->getPost("timing"));
		if($timing <= 0 || $timing >= time()){
			$timing = 0;
		}
		if(USER_TYPE == "jindao"){
			$this->olineUser($this->userid);
		}
		$time = max($time1,$timing);
		$data = $this->selectDouble($time);
		if(empty($data)){
			$this->ajaxResponse("", "消息空!", 1);
		}
		//超级宝箱信息
		if (USER_TYPE == "chuangjin"){
			$data["cheskList"] = $this->getCheskListInfo();
		}
		$this->ajaxResponse($data, "世界消息获取成功!", 0);
	}
}
