<?php
namespace Dhc\Modules\Game\Controllers;
use Dhc\Models\Config;
use Dhc\Models\Orchard;
use Dhc\Models\OrchardOrder;
use Dhc\Models\OrchardGoods;
use Dhc\Models\OrchardUser;
use Dhc\Models\OrchardAnimal;
use Dhc\Models\OrchardLogs;
use Dhc\Models\OrchardHailFellow;
use Dhc\Models\Product;
use Dhc\Models\TradeLogs;
use Dhc\Models\User;
use Dhc\Models\UserLog;
use Dhc\Models\UserProduct;
use Phalcon\Paginator\Adapter\Model as PaginatorModel;
use Dhc\Modules\Game\DogController as ControllersBase;
use Phalcon\Http\Response;

class PastureController extends ControllerBase{
	public $psize = 10;
	protected function initialize() {
		$this->checkToken();
		$this->pasture = $this->getConfig("pasture");
		$this->onoInfo = $this->onoInfo();
		$this->dayTime = strtotime(date("Y-m-d",time()));
	}
	public function getInfoAction(){
		//进入另一个用户
		$ownerId = $this->request->getPost("ownerId");
		$oldUid = 0;
		if(!empty($ownerId)){
			$oldUid = $this->userid;
			$this->userid = $ownerId;
		}
		$pasture = $this->selectUser($this->userid,"pasture");
		$data = array(
			"pasture"=>$pasture,
			"pasture1"=>0,
			"pasture2"=>0,
			"pasture3"=>0,
			"pasture4"=>0,
		);
		//是否存在动物
		for($i=1;$i<=$pasture["pasture"];$i++){
			$animal = new OrchardAnimal();
			$isa = $animal->findFirst("uid='{$this->userid}' AND aid='{$i}' AND status in(1,2)");
			if($isa){
				$data["pasture".$i] = 1;
			}
			if(!empty($oldUid)){
				$sql = "SELECT sum(nums) as nums FROM `dhc_orchard_animal` WHERE uid='{$this->userid}' AND aid='{$i}' AND status in(1,2)";
				$totalMoney = $this->db->query($sql)->fetch();
				$data["pastureNums".$i] = $totalMoney["nums"]?$totalMoney["nums"]:0;
			}
		}
		if(!empty($oldUid)){
			$data["user"] = $this->selectUser($this->userid, "index");
		}
		$this->ajaxResponse($data, "牧场信息返回成功！",0);
	}
	//升级信息
	public function getUpInfoAction(){
		$data = $this->getPastureInfo();
		$this->ajaxResponse($data, "牧场信息返回成功！",0);
	}
	//牧场升级信息
	public function upPastureAction(){
		$this->db->begin();
		$data = $this->getPastureInfo();
		if($data["upgrade"]<=0){
			$this->db->rollback();
			$this->ajaxResponse("","暂无法开通牧场！",1);
		}
		if($data["userLevel"]<$data["minimumLevel"] && $data["minimumLevel"]>0){
			$this->db->rollback();
			$this->ajaxResponse("","当前等级不够，无法开通，请升级至{$data["minimumLevel"]}级后重试！",1);
		}
		$user = new OrchardUser();
		$userInfo = $user->findFirst("uid='{$this->userid}'");
		if($userInfo == false){
			$this->db->rollback();
			$this->ajaxResponse("","用户信息获取失败！",1);
		}
		if(USER_TYPE == 'kk' && !empty($data["upInfo"]['grade']) && $data["upInfo"]["grade"]>$userInfo->grade){
			$this->db->rollback();
			$this->ajaxResponse("","当前农场等级不足，牧场暂时无法升级，请升级后再来！",1);
		}
		//钻石信息更新
		if(!empty($data["upInfo"]["diamonds"]["num"])){
			if($data["upInfo"]["diamonds"]["num"]>$data["upInfo"]["diamonds"]["tNums"]){
				$this->db->rollback();
				$this->ajaxResponse("",$this->zuanshiTitle."不足，无法开通！",1);
			}
			$userInfo->diamonds -= $data["upInfo"]["diamonds"]["num"];
			$flag = $this->saveOrchardLogs(array("uid"=>$this->userid,"mobile"=>$userInfo->mobile,"types"=>"deddiamonds","nums"=>-$data["upInfo"]["diamonds"]["num"],"landId"=>$data["upgrade"],"msg"=>"牧场{$data["upgrade"]}升级开通扣除".$this->zuanshiTitle.$data["upInfo"]["diamonds"]["num"]));
			if($flag == false){
				$this->db->rollback();
				$this->ajaxResponse("","用户".$this->zuanshiTitle."日志更新失败！",1);
			}
		}
		if(!empty($data["upInfo"]["goods"])){
			foreach ($data["upInfo"]["goods"] as $key=>$val){
				if($val["num"]>$val["tNums"]){
					$this->db->rollback();
					$this->ajaxResponse("",$val["title"]."不足，无法开通！",1);
				}
				$flag1 = $this->saveProduct($val["pid"],$val["num"],"ded");
				$flag2 = $this->saveOrchardLogs(array("mobile"=>$userInfo->mobile,"landId"=>$val["pid"],"types"=>"dedgoods","nums"=>-$val["num"],"msg"=>"牧场{$data["upgrade"]}升级扣除".$val["title"].$val["num"]."个"));
				if ($flag1 == false || $flag2 == false){
					$this->db->rollback();
					$this->ajaxResponse("",$val["title"]."更新失败！",1);
				}
			}
		}
		$userInfo->pasture += 1;
		$userInfo->updatetime = TIMESTAMP;
		$flag = $userInfo->update();
		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("","用户".$this->zuanshiTitle."更新失败！",1);
		}
		$this->db->commit();
		$data = array(
			"diamonds"=>$userInfo->diamonds
		);
		$this->ajaxResponse($data,"牧场开通成功！",0);
	}
	public function getPastureInfo(){
		$userInfoPasture= $this->selectUser($this->userid,"pasture");
		$data = array(
			"minimumLevel"=>$this->pasture["grade"],
			"userLevel"=>$userInfoPasture["grade"],
			"pasture"=>$userInfoPasture["pasture"],
			"upgrade"=>$userInfoPasture["pasture"]>=count($this->pasture["upgrade"])?"0":$userInfoPasture["pasture"]+1,
			"upInfo"=> $userInfoPasture["pasture"]>=count($this->pasture["upgrade"])?"": $this->pasture["upgrade"][$userInfoPasture["pasture"]+1],
		);
		if(empty($data["upInfo"])){
			$this->ajaxResponse("","请联系管理员确认升级信息！",1);
		}
		if(!empty($data["upInfo"]["goods"])){
			$product = $this->getUserProductInfo("sid");
			foreach ($data["upInfo"]["goods"] as $key=>&$val){
				$goods = $this->getOneProductInfo($val["pid"]);
				if(!empty($goods) && !empty($goods['title'])){
					$val["title"] = $goods['title'];
				}else{
					$val["title"] = "";
				}
				if(!empty($product[$val['pid']])){
					$val["tNums"] = $product[$val['pid']]["number"];
				}else{
					$val["tNums"] = 0;
				}
			}
		}
		if(!empty($data["upInfo"]["diamonds"])){
			$data["upInfo"]["diamonds"] =array(
				"num"=>$data["upInfo"]["diamonds"],
				"title"=>$this->zuanshiTitle,
				"tNums"=>$userInfoPasture["diamonds"],
			);
		}
		return $data;
	}
	//牧场信息查看
	function getAnimalInfoAction(){
		$animal = new OrchardAnimal();
		$aid = max(1,$this->request->getPost("aid"));
		$pindex = max(1,$this->request->getPost("page"));
		$lists = $animal->find(array(
			'conditions'	=>	"uid = $this->userid AND status in(1,2) AND aid='{$aid}'",
			'columns'		=>	'id,feedtime,days,info,createtime',
			'order'			=>	"id ASC"
		));
		$paginator = new PaginatorModel(array("data"	=> $lists,"limit"	=>$this->psize,"page" =>$pindex));
		$page = $paginator->getPaginate();
		$list = $this->object2array($page);
		if(!empty($list["items"])){
			foreach ($list["items"] as &$value) {
				$value["time"] = date("Y-m-d H:i:s",$value["createtime"]);
				$info = json_decode($value["info"],true);
				$value["speed"] = sprintf("%.2f",($info["days"] - $value["days"])/$info["days"])*100;
				if($value['feedtime']>$this->dayTime){
					$value["feedStaus"] = 1;
				}else{
					$value["feedStaus"] = 0;
				}
				unset($value["info"],$value["createtime"]);
			}
		}
		$data['list'] = $list["items"];
		$data['curPage'] = $list["current"];
		if($list["total_pages"]>0){
			if($list["total_pages"]>100){
				$totalPage = 100;
			}else{
				$totalPage = $list["total_pages"];
			}
		}else{
			$totalPage =1;
		}
		$data['totalPage'] = $totalPage;
		$this->ajaxResponse($data, "牧场信息列表",0);
	}
	//喂养
	public function saveFeedAnimalAction(){
		$id = max(1,$this->request->getPost("id"));
		$animal = new OrchardAnimal();
		$animalInfo = $animal->findFirst("id='{$id}' AND uid='{$this->userid}' AND status=1");
		if($animalInfo == false){
			$this->ajaxResponse("","牧场信息获取失败！",1);
		}
		if($animalInfo->feedtime> $this->dayTime){
			$this->ajaxResponse("","已经喂养，无需重复喂养！",1);
		}
		$info = json_decode($animalInfo->info,true);
		$this->db->begin();
		$animalInfo->feedtime = TIMESTAMP;
		$animalInfo->feednums += $info["feed"];
		$animalInfo->days -=1;
		if($animalInfo->days <=0){
			$animalInfo->status = 2;
		}
		$animalInfo->updatetime = TIMESTAMP;
		$flag = $animalInfo->update();
		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("","喂养失败！",1);
		}
		$user = new OrchardUser();
		$userInfo = $user->findFirst("uid='{$this->userid}'");
		if($userInfo == false){
			$this->db->rollback();
			$this->ajaxResponse("","会员信息获取失败！",1);
		}
		if($userInfo->feed <$info["feed"]){
			$this->db->rollback();
			$this->ajaxResponse("","当前饲料不足，无法喂养！",1);
		}
		$userInfo->feed -= $info["feed"];
		$userInfo->updatetime = TIMESTAMP;
		$flag = $userInfo->update();
		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("","喂养更新失败！",1);
		}
		$flag = $this->saveOrchardLogs(array("uid"=>$this->userid,"mobile"=>$this->mobile,"types"=>'dedfeed',"landId"=>$animalInfo->aid,"nums"=>-$info["feed"],"msg"=>"喂养".$animalInfo->title."扣除饲料".$info["feed"]."袋"));
		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("","喂养饲料更新日式失败！",1);
		}
		$this->db->commit();
		$this->ajaxResponse("","喂养成功！",0);
	}
	//收获 动物
	public function animalHarvestAction(){
		$id = max(1,$this->request->getPost("id"));
		$animal = new OrchardAnimal();
		$animalInfo = $animal->findFirst("id='{$id}' AND uid='{$this->userid}' ");
		if($animalInfo == false){
			$this->ajaxResponse("","牧场信息获取失败！",1);
		}
		if($animalInfo->status !=2){
			$this->ajaxResponse("","暂时无法收获！",1);
		}
		$this->db->begin();
		$animalInfo->status = 3;
		$animalInfo->updatetime = TIMESTAMP;
		$flag = $animalInfo->update();
		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("","收获失败！",1);
		}
		$goodsId = "9000".$animalInfo->aid;
		$flag = $this->saveProduct($goodsId, $animalInfo->nums);
		if ($flag == false) {
			$this->db->rollback();
			$this->ajaxResponse("","{$animalInfo->title}收获失败！",1);
		}
		$flag = $this->saveOrchardLogs(array("uid"=>$this->userid,"mobile"=>$this->mobile,"types"=>"addgoods","landId"=>$goodsId,"nums"=>$animalInfo->nums,"msg"=>"牧场".$id."收获".$animalInfo->title.$animalInfo->nums."只"));
		if($flag == false){
			$this->db->rollback();
			$this->ajaxResponse("","{$animalInfo->title}收获日志更新失败！",1);
		}
		$this->db->commit();
		$this->ajaxResponse("","收获成功！",0);
	}
	//日志记录
	public function logsAction(){
		$logs = new OrchardLogs();
		$pindex = max(1,$this->request->getPost("page"));
		$lists = $logs->find(array(
			'conditions'	=>	"uid = $this->userid AND status=1 AND (types in('addfeed','dedfeed') or landId>90000 or msg like '%牧场%')",
			'columns'		=>	'createtime,msg,nums',
			'order'			=>	"createtime DESC"
		));
		$paginator = new PaginatorModel(array("data"	=> $lists,"limit"	=>$this->psize,"page" =>$pindex));
		$page = $paginator->getPaginate();
		$list = $this->object2array($page);
		if(!empty($list["items"])){
			foreach ($list["items"] as &$value) {
				$value["time"] = date("Y-m-d H:i:s",$value["createtime"]);
			}
		}
		$data['list'] = $list["items"];
		$data['curPage'] = $list["current"];
		if($list["total_pages"]>0){
			if($list["total_pages"]>100){
				$totalPage = 100;
			}else{
				$totalPage = $list["total_pages"];
			}
		}else{
			$totalPage =1;
		}
		$data['totalPage'] = $totalPage;
		$this->ajaxResponse($data, "日志列表",0);
	}
	//清空日志记录
	public function emlogsAction(){
		$logs = new OrchardLogs();
//		$pindex = max(1,$this->request->getPost("page"));
		$lists = $logs->find(array(
			'conditions'	=>	"uid = $this->userid AND status=1 AND (types in('addfeed','dedfeed') or landId>90000 or msg like '%牧场%')",
			'columns'		=>	'createtime,msg,id',
			'order'			=>	"createtime desc"
		));
//		$paginator = new PaginatorModel(array("data"	=> $lists,"limit"	=>$this->psize,"page" =>$pindex));
//		$page = $paginator->getPaginate();
		$list = $this->object2array($lists);
		if(empty($list)){
			$this->ajaxResponse("", "已无更多日志记录!",1);
		}
		$flag = true;
		$this->db->begin();
		if(!empty($list)){
			foreach ($list as $value) {
				$logs = new OrchardLogs();
				$item = $logs->findFirst("id={$value['id']}");
				$item->status = 2;
				$flag = $item->update();
				if($flag == false){
					break;
				}
			}
		}
		if($flag){
			$this->db->commit();
			$this->ajaxResponse("", "一键清空操作成功!",0);
		}else{
			$this->db->rollback();
			$this->ajaxResponse("", "一键清空操作失败!",1);
		}
	}
	//排行榜
	public function rankingsAction(){
		$pindex = max(1,$this->request->getPost("page"));
		$user = new OrchardUser();
		$lists = $user->find(array(
			'conditions'	=>	"pasture >0",
			'columns'		=>	'uid,nickname,pasture,diamonds',
			'order'			=>	"pasture desc,diamonds desc",
			'limit'=>'100'
		));
		$paginator = new PaginatorModel(array("data"	=> $lists,"limit"	=>$this->psize,"page" =>$pindex));
		$page = $paginator->getPaginate();
		$arr = $this->object2array($lists);
		$data = array();
		if(!empty($arr)){
			foreach ($arr as $key => $value) {
				if($value["uid"] == $this->userid){
					$data["rank"] =$key+1;
					break;
				}
			}
		}
		$list = $this->object2array($page);
		if(!empty($list["items"])){
			foreach ($list["items"] as $key =>&$value) {
				$value["rank"] = ($key+1)+($pindex-1)*$this->psize;
				$value["userName"] = $value["nickname"];
				$value["level"] = $value["pasture"];
				unset($list["items"][$key]["nickname"],$list["items"][$key]["pasture"]);
			}
		}
		$data['list'] = $list["items"];
		$data['curPage'] = $list["current"];
		$data['totalPage'] = $list["total_pages"];
		$this->ajaxResponse($data, "排行榜信息",0);
	}
	//好友申请记录
	function hailListAction(){
		$hailFellow = new OrchardHailFellow();
		$pindex = max(1,$this->request->getPost("page"));
		$lists = $hailFellow->find(array(
			'conditions'	=>	"huid = $this->userid  AND status in (0,1)",
			'columns'		=>	'createtime,uid,status,id',
			'order'			=>	"createtime desc"
		));
		$paginator = new PaginatorModel(array("data"	=> $lists,"limit"	=>$this->psize,"page" =>$pindex));
		$page = $paginator->getPaginate();
		$list = $this->object2array($page);
		if(!empty($list["items"])){
			foreach ($list["items"] as $key=>&$value) {
				$user = $this->selectUser($value["uid"], "pastureInfo");
				if(empty($user)){
					unset($list["items"][$key]);
				}
				$value["userName"] = !empty($user["userName"])?$user["userName"]:"";
				$value["grade"] = !empty($user["level"])?$user["level"]:"0";
				$value["diamonds"] = !empty($user["diamonds"])?$user["diamonds"]:"";
				$value["time"] = date("Y-m-d H:i:s",$value["createtime"]);
				if($value["status"] == 1){
					$value["info"] = "已同意";
				}elseif($value["status"] ==9){
					$value["info"] = "已拒绝";
				}else{
					$value["info"] = "未同意";
				}
			}
		}

		$data['list'] = array_values($list["items"]);
		$data['curPage'] = $list["current"];
		$data['totalPage'] = $list["total_pages"]>0?$list["total_pages"]:1;
		$this->ajaxResponse($data, "好友申请列表",0);
	}
}
