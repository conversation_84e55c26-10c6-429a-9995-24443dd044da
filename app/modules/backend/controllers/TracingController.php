<?php
/**
 * <AUTHOR>
 * @copyright Copyright (c) 2017 HNDH Software Technology Co., Ltd.
 * createtime: 2017/11/8 14:35
 */

namespace  Dhc\Modules\Backend\Controllers;


use Dhc\Models\Recharge;
use Phalcon\Http\Request;

class TracingController extends ControllerBase
{
	const TYPES = ['type'];
	public function indexAction(){
		$type = $this->request->get('type', 'string', 'coin');
		$uid = $this->request->get('uid', 'int', '', true);
		// 查找
		$this->coin($uid);
	}

	private function coin($uid)
	{
		$data = [];
		// 充值
		$recharge = Recharge::find([
			"conditions" => "uid = :uid: AND payStatus = 2",
			"bind" => [
				'uid' => $uid
			]
		])->toArray();
		foreach ($recharge as $item) {
			$data[] = [
				'num' => $item['number'],
				'type' => $item['payType'],
				'log' => '充值',
				'time' => date('Y-m-d H:i:s', $item['createTime']),
			];
		}
		print_r($data);
	}

}
