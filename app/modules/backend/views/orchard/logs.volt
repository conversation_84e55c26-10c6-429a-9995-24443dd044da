<?php $this->flashSession->output(); ?>
<!--商品信息-->
<div class="nav-tabs-custom">
	<div class="tab-base">
		<ul class="nav nav-tabs">
			<li class="{% if op is defined %}{% if op == 'display' %}active{% else %} {% endif %}{% endif %}">
				<a href="{{ apppath }}/orchard/logs?op=display&page={{logsList['current']}}" aria-expanded="{% if op is defined %}{% if op == 'display' %}true{% else %} false{% endif %}{% endif %}">日志列表</a>
			</li>
			<li class="{% if op is defined %}{% if op == 'list' %}active{% else %} {% endif %}{% endif %}">
				<a href="{{ apppath }}/orchard/logs?op=list&page={{logsList['current']}}" aria-expanded="{% if op is defined %}{% if op == 'display' %}true{% else %} false{% endif %}{% endif %}">消息列表</a>
			</li>
			{% if op == 'post' %}
			<li class="{% if op is defined %}{% if op == 'post' %}active{% else %} {% endif %}{% endif %}">
				<a href="{{ apppath }}/orchard/logs?op=post" aria-expanded="{% if op is defined %}{% if op == 'post' %}true{% else %} false{% endif %}{% endif %}">系统消息发布中</a>
			</li>
			{% endif %}
			{% if hostType == 'chuangjin' %}
			<li class="{% if op is defined %}{% if op == 'downgrade' %}active{% else %} {% endif %}{% endif %}">
				<a href="{{ apppath }}/orchard/logs?op=downgrade" aria-expanded="{% if op is defined %}{% if op == 'downgrade' %}true{% else %} false{% endif %}{% endif %}">房屋降级记录</a>
			</li>
			<li class="{% if op is defined %}{% if op == 'downgradeLand' %}active{% else %} {% endif %}{% endif %}">
				<a href="{{ apppath }}/orchard/logs?op=downgradeLand" aria-expanded="{% if op is defined %}{% if op == 'downgradeLand' %}true{% else %} false{% endif %}{% endif %}">土地降级记录</a>
			</li>
			<li class="{% if op is defined %}{% if op == 'chestlist' %}active{% else %} {% endif %}{% endif %}">
				<a href="{{ apppath }}/orchard/logs?op=chestlist" aria-expanded="{% if op is defined %}{% if op == 'chestlist' %}true{% else %} false{% endif %}{% endif %}">宝箱邀请记录</a>
			</li>
			<li class="{% if op is defined %}{% if op == 'animal' %}active{% else %} {% endif %}{% endif %}">
				<a href="{{ apppath }}/orchard/logs?op=animal" aria-expanded="{% if op is defined %}{% if op == 'animal' %}true{% else %} false{% endif %}{% endif %}">牧场信息记录</a>
			</li>
			{% endif %}
		</ul>
		{% if op == 'display' %}
		<div class="tab-content">
			<div class="panel-body">
				<form class="form-horizontal form-padding " method="get" action="{{ apppath }}/orchard/logs">
					<div class="form-group">
						<div class="panel-control">
                    							<span class="label label-info">合计{{ total_nums }}</span>
                    	</div>
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索信息</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<input class="form-control" type="text" name="keywords" value="{% if keywords is defined %}{{ keywords }}{% endif %}" placeholder="请输入会员编号 信息 数量">
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">信息类型</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<select name="types" class="form-control">
								{% for key,row in logsType %}
								<option value="{{ key }}" {% if types is defined %}{% if key == types %}selected{% endif %}{% endif %}>
									{{ row }}
								</option>
								{% endfor %}
							</select>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<select name="status" class="form-control">
								<option value="1" {% if status is defined %}{% if status == 1 %}selected{% endif %}{% endif %}>
									正常
								</option>
								<option value="2" {% if status is defined %}{% if status == 2 %}selected{% endif %}{% endif %}>
									已读
								</option>
							</select>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
						</div>
					</div>
					<div class="text-lg-center">
						<button class="btn btn-info fa fa-search" type="submit">搜索</button>
					</div>
				</form>
			</div>
			<div id="demo-lft-tab-1" class="tab-pane fade {% if op is defined %}{% if op == 'display' %}active in{% else %} {% endif %}{% endif %}">
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
									<tr>
										<th>日志ID</th>
										<th>会员ID</th>
										<th>会员信息</th>
										<th>操作类型</th>
										<th>数量</th>
										<th>消息</th>
										<th>状态</th>
										<th>时间</th>
									</tr>
								</thead>
								<tbody>
								{% if logsList is defined %}
								{% for rows in logsList['list'] %}
								<tr>
									<td>{{ rows['id'] }}</td>
									<td>{{ rows['uid'] }}</td>
									<td>{{ rows['mobile'] }}</td>
									<td>
										{% for key,row  in  logsType %}
										{% if key == rows['types'] %}
										{{ row }}
										{% endif %}
										{% endfor%}
									</td>
									<td>{{ rows['nums'] }}</td>
									<td>{{ rows['msg'] }}</td>
									<td>
										{% if rows['status'] == '1' %}
											正常
										{% elseif rows['status'] == '2' %}
											已读
										{% else %}
											异常
										{% endif %}
									</td>
									<td>{{ date("Y-m-d H:i:s",rows['createtime']) }}</td>
									</tr>
									{% endfor %}
									{% endif %}
								</tbody>
							</table>
							<div class="bars pull-left">
								<a href="{{apppath}}/orchard/logs?op=export_logs&keywords={{keywords}}&types={{types}}&status={{status}}&starttime={{starttime}}&endtime={{endtime}}">
									<button class="btn btn-info">
									<i class="demo-pli-cross"></i> 导出
									</button>
								</a>
							</div>
							<div class="panel-body text-center">
								<ul class="pagination">
									<li><a href="{{apppath}}/orchard/logs?op=display&page=1{%if starttime is defined%}&time[start]={{starttime}}&time[end]={{endtime}}{%endif%}{%if keywords is defined%}&keywords={{keywords}}{%endif%}{%if types is defined%}&types={{types}}{%endif%}" class="demo-pli-arrow-right">首页</a></li>
									<li><a href="{{apppath}}/orchard/logs?op=display&page={{ logsList['before'] }}{%if starttime is defined%}&time[start]={{starttime}}&time[end]={{endtime}}{%endif%}{%if keywords is defined%}&keywords={{keywords}}{%endif%}{%if types is defined%}&types={{types}}{%endif%}">上一页</a></li>
									<li><a href="#">第{{ logsList['current'] }}页</a></li>
									<li><a href="#">共{{ logsList['total_pages'] }}页</a></li>
									<li><a href="{{apppath}}/orchard/logs?op=display&page={{ logsList['next'] }}{%if starttime is defined%}&time[start]={{starttime}}&time[end]={{endtime}}{%endif%}{%if keywords is defined%}&keywords={{keywords}}{%endif%}{%if types is defined%}&types={{types}}{%endif%}">下一页</a></li>
									<li><a href="{{apppath}}/orchard/logs?op=display&page={{ logsList['total_pages'] }}{%if starttime is defined%}&time[start]={{starttime}}&time[end]={{endtime}}{%endif%}{%if keywords is defined%}&keywords={{keywords}}{%endif%}{%if types is defined%}&types={{types}}{%endif%}" class="demo-pli-arrow-right">尾页</a></li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
			{% endif %}
			{% if op == 'list' %}
			<div class="tab-content">
			<div class="panel-body">
				<form class="form-horizontal form-padding " method="get" action="{{ apppath }}/orchard/logs?op=list">
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索信息</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<input class="form-control" type="text" name="keywords" value="{% if keywords is defined %}{{ keywords }}{% endif %}" placeholder="请输入会员编号 信息 数量">
							<input class="form-control" type="hidden" name="op" value="list">
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">信息类型</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<select name="types" class="form-control">
								{% for key,row in logsType %}
								<option value="{{ key }}" {% if types is defined %}{% if key == types %}selected{% endif %}{% endif %}>
									{{ row }}
								</option>
								{% endfor %}
							</select>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
							<select name="status" class="form-control">
								<option value="1" {% if status is defined %}{% if status == 1 %}selected{% endif %}{% endif %}>
									公布中
								</option>
								<option value="2" {% if status is defined %}{% if status == 2 %}selected{% endif %}{% endif %}>
									已结束
								</option>
							</select>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
									<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
						</div>
					</div>
					<div class="text-lg-center">
						<button class="btn btn-info fa fa-search" type="submit">搜索</button>
						<a href="{{ apppath }}/orchard/logs?op=post">
							<div class="btn btn-default"><i class="glyphicon glyphicon-leaf"></i> 系统消息发布</div>
						</a>
					</div>
				</form>
			</div>
			<div id="demo-lft-tab-1" class="tab-pane fade {% if op is defined %}{% if op == 'list' %}active in{% else %} {% endif %}{% endif %}">
				<div class="panel">
					<div class="panel-body">
						<div class="table-responsive">
							<table class="table table-hover">
								<thead>
									<tr>
										<th>日志ID</th>
										<th>会员ID</th>
										<th>操作类型</th>
										<th>数量</th>
										<th>消息</th>
										<th>状态</th>
										<th>时间</th>
									</tr>
								</thead>
								<tbody>
								{% if logsList is defined %}
								{% for rows in logsList['list'] %}
								<tr>
									<td>{{ rows['id'] }}</td>
									<td>{{ rows['uid'] }}</td>
									<td>
										{% for key,row  in  logsType %}
										{% if key == rows['types'] %}
										{{ row }}
										{% endif %}
										{% endfor%}
									</td>
									<td>{{ rows['nums'] }}</td>
									<td>{{ rows['msg'] }}</td>
									<td>
										{% if rows['status'] == '1' %}
										<a class="btn btn-default btn-sm" href="{{apppath}}/orchard/logs?op=status&id={{ rows['id'] }}">公布中</a>
										{% elseif rows['status'] == '2' %}
										<a class="btn btn-default btn-sm" href="{{apppath}}/orchard/logs?op=status&id={{ rows['id'] }}">已结束</a>
										{% else %}
											异常
										{% endif %}
									</td>
									<td>{{ date("Y-m-d H:i:s",rows['createtime']) }}</td>
									{% endfor %}

									{% endif %}
								</tbody>
							</table>
							<div class="panel-body text-center">
								{% if logsList['total_pages'] >1 %}
								<ul class="pagination">
									<li><a href="{{apppath}}/orchard/logs?op=list&page=1" class="demo-pli-arrow-right">首页</a></li>
									<li><a href="{{apppath}}/orchard/logs?op=list&page={{ logsList['before'] }}">上一页</a></li>
									<li><a href="#">第{{ logsList['current'] }}页</a></li>
									<li><a href="#">共{{ logsList['total_pages'] }}页</a></li>
									<li><a href="{{apppath}}/orchard/logs?op=list&page={{ logsList['next'] }}">下一页</a></li>
									<li><a href="{{apppath}}/orchard/logs?op=list&page={{ logsList['total_pages'] }}" class="demo-pli-arrow-right">尾页</a></li>
								</ul>
								{% endif %}
							</div>
						</div>
					</div>
				</div>
			</div>
			{% endif %}
			{% if op == 'post' %}
			<div class="tab-content">
				<div id="demo-lft-tab-1" class="tab-pane fade {% if op is defined%}{% if op == 'post' %}active in{% else %} {% endif %}{% endif%}">
					<div class="panel">
						<div class="panel-body">
							<form class="form-horizontal form-padding " method="post" action="{{ apppath }}/orchard/logs?op=post">
								<div class="form-group">
									<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">系统消息</label>
									<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
										<input class="form-control" id="demo-vs-definput" type="text" name="msg" value="">
									</div>
								</div>
								<div class="panel-footer text-left">
									<button class="btn btn-success" type="submit">提交</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
			{% endif %}
			{% if op == 'downgrade'%}
			<div class="tab-content">
            			<div class="panel-body">
            				<form class="form-horizontal form-padding " method="get" action="{{ apppath }}/orchard/logs?op=downgrade">
            					<div class="form-group">
            						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索信息</label>
            						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
            							<input class="form-control" type="text" name="keywords" value="{% if keywords is defined %}{{ keywords }}{% endif %}" placeholder="请输入会员编号 等级">
            							<input class="form-control" type="hidden" name="op" value="downgrade">
            						</div>
            					</div>
            					<div class="form-group">
            						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
            						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
            							<select name="status" class="form-control">
            								<option value="1" {% if status is defined %}{% if status == 1 %}selected{% endif %}{% endif %}>
            									系统
            								</option>
            								<option value="2" {% if status is defined %}{% if status == 2 %}selected{% endif %}{% endif %}>
            									管理
            								</option>
            							</select>
            						</div>
            					</div>
            					<div class="form-group">
            						<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
            						<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
            									<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
            						</div>
            					</div>
            					<div class="text-lg-center">
            						<button class="btn btn-info fa fa-search" type="submit">搜索</button>
            					</div>
            				</form>
            			</div>
            			<div id="demo-lft-tab-1" class="tab-pane fade {% if op is defined %}{% if op == 'downgrade' %}active in{% else %} {% endif %}{% endif %}">
            				<div class="panel">
            					<div class="panel-body">
            						<div class="table-responsive">
            							<table class="table table-hover">
            								<thead>
            									<tr>
            										<th>降级ID</th>
            										<th>会员ID</th>
            										<th>房屋等级</th>
            										<th>降级等级</th>
            										<th>升级时间</th>
            										<th>降级时间</th>
            										<th>类型</th>
            									</tr>
            								</thead>
            								<tbody>
            								{% if List is defined %}
            								{% for rows in List['list'] %}
            								<tr>
            									<td>{{ rows['id'] }}</td>
            									<td>{{ rows['uid'] }}</td>
            									<td>{{ rows['houseLv'] }}
            									</td>
            									<td>{{ rows['grade'] }}</td>
            									<td>{{ date("Y-m-d H:i:s",rows['htime']) }}</td>

            									<td>{{ date("Y-m-d H:i:s",rows['createtime']) }}</td>
            									<td>
													{% if rows['status'] == '1' %}
													系统
													{% elseif rows['status'] == '2' %}
													管理
													{% else %}
														异常
													{% endif %}
												</td>
											{% endfor %}

											{% endif %}
            								</tbody>
            							</table>
            							<div class="panel-body text-center">
            								{% if List['total_pages'] >1 %}
            								<ul class="pagination">
            									<li><a href="{{apppath}}/orchard/logs?op=downgrade&page=1" class="demo-pli-arrow-right">首页</a></li>
            									<li><a href="{{apppath}}/orchard/logs?op=downgrade&page={{ List['before'] }}">上一页</a></li>
            									<li><a href="#">第{{ List['current'] }}页</a></li>
            									<li><a href="#">共{{ List['total_pages'] }}页</a></li>
            									<li><a href="{{apppath}}/orchard/logs?op=downgrade&page={{ List['next'] }}">下一页</a></li>
            									<li><a href="{{apppath}}/orchard/logs?op=downgrade&page={{ List['total_pages'] }}" class="demo-pli-arrow-right">尾页</a></li>
            								</ul>
            								{% endif %}
            							</div>
            						</div>
            					</div>
            				</div>
            			</div>
			{% endif %}
			{% if op == 'downgradeLand'%}
			<div class="tab-content">
				<div class="panel-body">
					<form class="form-horizontal form-padding " method="get" action="{{ apppath }}/orchard/logs?op=downgradeLand">
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索信息</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" type="text" name="keywords" value="{% if keywords is defined %}{{ keywords }}{% endif %}" placeholder="请输入会员编号 等级">
								<input class="form-control" type="hidden" name="op" value="downgradeLand">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<select name="status" class="form-control">
									<option value="1" {% if status is defined %}{% if status == 1 %}selected{% endif %}{% endif %}>
										系统
									</option>
									<option value="2" {% if status is defined %}{% if status == 2 %}selected{% endif %}{% endif %}>
										管理
									</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
										<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
							</div>
						</div>
						<div class="text-lg-center">
							<button class="btn btn-info fa fa-search" type="submit">搜索</button>
						</div>
					</form>
				</div>
				<div id="demo-lft-tab-1" class="tab-pane fade {% if op is defined %}{% if op == 'downgradeLand' %}active in{% else %} {% endif %}{% endif %}">
					<div class="panel">
						<div class="panel-body">
							<div class="table-responsive">
								<table class="table table-hover">
									<thead>
										<tr>
											<th>降级ID</th>
											<th>会员ID</th>
											<th>土地等级</th>
											<th>更新等级</th>
											<th>降级时间</th>
											<th>时间</th>
											<th>类型</th>
										</tr>
									</thead>
									<tbody>
									{% if List is defined %}
									{% for rows in List['list'] %}
									<tr>
										<td>{{ rows['id'] }}</td>
										<td>{{ rows['uid'] }}</td>
										<td>{{ landLevelInfo[rows['houseLv']] }}
										</td>
										<td>{{ landLevelInfo[rows['grade']] }}</td>
										<td>{{ date("Y-m-d H:i:s",rows['htime']) }}</td>

										<td>{{ date("Y-m-d H:i:s",rows['createtime']) }}</td>
										<td>
											{% if rows['status'] == '1' %}
											系统
											{% elseif rows['status'] == '2' %}
											管理
											{% else %}
												异常
											{% endif %}
										</td>
									{% endfor %}

									{% endif %}
									</tbody>
								</table>
								<div class="panel-body text-center">
									{% if List['total_pages'] >1 %}
									<ul class="pagination">
										<li><a href="{{apppath}}/orchard/logs?op=downgradeLand&page=1" class="demo-pli-arrow-right">首页</a></li>
										<li><a href="{{apppath}}/orchard/logs?op=downgradeLand&page={{ List['before'] }}">上一页</a></li>
										<li><a href="#">第{{ List['current'] }}页</a></li>
										<li><a href="#">共{{ List['total_pages'] }}页</a></li>
										<li><a href="{{apppath}}/orchard/logs?op=downgradeLand&page={{ List['next'] }}">下一页</a></li>
										<li><a href="{{apppath}}/orchard/logs?op=downgradeLand&page={{ List['total_pages'] }}" class="demo-pli-arrow-right">尾页</a></li>
									</ul>
									{% endif %}
								</div>
							</div>
						</div>
					</div>
				</div>
			{% endif %}
			{% if op == 'chestlist' %}
			<div class="tab-content">
				<div class="panel-body">
					<form class="form-horizontal form-padding " method="get" action="{{ apppath }}/orchard/logs?op=chestlist">
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索信息</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" type="text" name="keywords" value="{% if keywords is defined %}{{ keywords }}{% endif %}" placeholder="请输入会员编号 发起人会员编号">
								<input class="form-control" type="hidden" name="op" value="chestlist">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<select name="status" class="form-control">
									<option value="5" {% if status is defined %}{% if status == 5 %}selected{% endif %}{% endif %}>
										全部
									</option>
									<option value="0" {% if status is defined %}{% if status ==0 %}selected{% endif %}{% endif %}>
										已邀请
									</option>
									<option value="1" {% if status is defined %}{% if status == 1 %}selected{% endif %}{% endif %}>
										已接受
									</option>
									<option value="2" {% if status is defined %}{% if status == 2 %}selected{% endif %}{% endif %}>
										已开启
									</option>
									<option value="9" {% if status is defined %}{% if status == 9 %}selected{% endif %}{% endif %}>
										已取消
									</option>
									<option value="8" {% if status is defined %}{% if status == 8 %}selected{% endif %}{% endif %}>
										已返还
									</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">宝箱</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<select name="tId" class="form-control">
									<option value="0" {% if tId is defined %}{% if tId == 0 %}selected{% endif %}{% endif %}>
										全部
									</option>
									<option value="18" {% if tId is defined %}{% if tId ==18 %}selected{% endif %}{% endif %}>
										超级铜宝箱
									</option>
									<option value="19" {% if tId is defined %}{% if tId == 19 %}selected{% endif %}{% endif %}>
										超级银宝箱
									</option>
									<option value="20" {% if tId is defined %}{% if tId == 20 %}selected{% endif %}{% endif %}>
										超级金宝箱
									</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
										<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
							</div>
						</div>
						<div class="text-lg-center">
							<button class="btn btn-info fa fa-search" type="submit">搜索</button>
						</div>
					</form>
				</div>
				<div id="demo-lft-tab-1" class="tab-pane fade {% if op is defined %}{% if op == 'chestlist' %}active in{% else %} {% endif %}{% endif %}">
					<div class="panel">
						<div class="panel-body">
							<div class="table-responsive">
								<table class="table table-hover">
									<thead>
										<tr>
											<th>会员ID</th>
											<th>宝箱信息</th>
											<th>邀请人ID</th>
											<th>金币</th>
											<th>状态</th>
											<th>开始时间</th>
											<th>结束时间</th>
										</tr>
									</thead>
									<tbody>
									{% if List is defined %}
									{% for rows in List['list'] %}
									<tr>
										<td>{{ rows['uid'] }}</td>
										<td>{{ typesInfo[rows['tId']] }}</td>
										<td>{{ rows['disUid'] }}
										</td>
										<td>{{ rows['price'] }}</td>
										<td>{% if rows['status'] == '0' %}
											已邀请
											{% elseif rows['status'] == '1' %}
											已接受
											{% elseif rows['status'] == '2' %}
											已开启
											{% elseif rows['status'] == '9' %}
                                            已取消
                                            {% elseif rows['status'] == '8' %}
                                            已返还
											{% else %}
												异常
											{% endif %}</td>
										<td>{{ date("Y-m-d H:i:s",rows['starttime']) }}</td>

										<td>{{ date("Y-m-d H:i:s",rows['endtime']) }}</td>
									{% endfor %}

									{% endif %}
									</tbody>
								</table>
								<div class="panel-body text-center">
									{% if List['total_pages'] >1 %}
									<ul class="pagination">
										<li><a href="{{apppath}}/orchard/logs?op=chestlist&page=1" class="demo-pli-arrow-right">首页</a></li>
										<li><a href="{{apppath}}/orchard/logs?op=chestlist&page={{ List['before'] }}">上一页</a></li>
										<li><a href="#">第{{ List['current'] }}页</a></li>
										<li><a href="#">共{{ List['total_pages'] }}页</a></li>
										<li><a href="{{apppath}}/orchard/logs?op=chestlist&page={{ List['next'] }}">下一页</a></li>
										<li><a href="{{apppath}}/orchard/logs?op=chestlist&page={{ List['total_pages'] }}" class="demo-pli-arrow-right">尾页</a></li>
									</ul>
									{% endif %}
								</div>
							</div>
						</div>
					</div>
				</div>
			{% endif %}
			{% if op == 'animal' %}
			<div class="tab-content">
				<div class="panel-body">
					<form class="form-horizontal form-padding " method="get" action="{{ apppath }}/orchard/logs?op=chestlist">
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">搜索信息</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<input class="form-control" type="text" name="keywords" value="{% if keywords is defined %}{{ keywords }}{% endif %}" placeholder="请输入会员编号 动物信息">
								<input class="form-control" type="hidden" name="op" value="animal">
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">状态</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<select name="status" class="form-control">
									<option value="0" {% if status is defined %}{% if status == 0 %}selected{% endif %}{% endif %}>
										全部
									</option>
									<option value="1" {% if status is defined %}{% if status == 1 %}selected{% endif %}{% endif %}>
										喂养中
									</option>
									<option value="2" {% if status is defined %}{% if status == 2 %}selected{% endif %}{% endif %}>
										已成熟
									</option>
									<option value="3" {% if status is defined %}{% if status == 3 %}selected{% endif %}{% endif %}>
										已收获
									</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">动物</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								<select name="aid" class="form-control">
									<option value="0" {% if aid is defined %}{% if aid == 0 %}selected{% endif %}{% endif %}>
										全部
									</option>
									<option value="1" {% if aid is defined %}{% if aid ==1 %}selected{% endif %}{% endif %}>
										玉兔
									</option>
									<option value="2" {% if aid is defined %}{% if aid == 2 %}selected{% endif %}{% endif %}>
										刺猬
									</option>
									<option value="3" {% if aid is defined %}{% if aid == 3 %}selected{% endif %}{% endif %}>
										浣熊
									</option>
									<option value="4" {% if aid is defined %}{% if aid == 4 %}selected{% endif %}{% endif %}>
										金毛
									</option>
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-4 col-md-2 col-lg-1 control-label">时间</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
										<?=Dhc\Component\MyTags::TimePiker("time",array("starttime"=>$starttime,"endtime"=>$endtime))?>
							</div>
						</div>
						<div class="text-lg-center">
							<button class="btn btn-info fa fa-search" type="submit">搜索</button>
						</div>
					</form>
				</div>
				<div id="demo-lft-tab-1" class="tab-pane fade {% if op is defined %}{% if op == 'animal' %}active in{% else %} {% endif %}{% endif %}">
					<div class="panel">
						<div class="panel-body">
							<div class="table-responsive">
								<table class="table table-hover">
									<thead>
										<tr>
											<th>会员ID</th>
											<th>动物</th>
											<th>状态</th>
											<th>剩余天数</th>
											<th>累计喂养</th>
											<th>上次喂养时间</th>
											<th>开始时间</th>
											<th>更新时间</th>
										</tr>
									</thead>
									<tbody>
									{% if List is defined %}
									{% for rows in List['list'] %}
									<tr>
										<td>{{ rows['uid'] }}</td>
										<td>{{ animalInfo[rows['aid']]['title'] }} </td>
										<td>{{ statusInfo[rows['status']] }}
										<td>{{ rows['days'] }}
										</td>
										<td>{{ rows['feednums'] }}</td>
										<td>{% if rows['feedtime']>0 %}{{ date("Y-m-d H:i:s",rows['feedtime']) }}{% endif %}</td>
										<td>{{ date("Y-m-d H:i:s",rows['createtime']) }}</td>
										<td>{{ date("Y-m-d H:i:s",rows['updatetime']) }}</td>
									{% endfor %}

									{% endif %}
									</tbody>
								</table>
								<div class="panel-body text-center">
									{% if List['total_pages'] >1 %}
									<ul class="pagination">
										<li><a href="{{apppath}}/orchard/logs?op=animal&page=1&status={{ status }}&aid={{ aid }}&keywords={{ keywords }}" class="demo-pli-arrow-right">首页</a></li>
										<li><a href="{{apppath}}/orchard/logs?op=animal&page={{ List['before'] }}&status={{ status }}&aid={{ aid }}&keywords={{ keywords }}">上一页</a></li>
										<li><a href="#">第{{ List['current'] }}页</a></li>
										<li><a href="#">共{{ List['total_pages'] }}页</a></li>
										<li><a href="{{apppath}}/orchard/logs?op=animal&page={{ List['next'] }}&status={{ status }}&aid={{ aid }}&keywords={{ keywords }}">下一页</a></li>
										<li><a href="{{apppath}}/orchard/logs?op=animal&page={{ List['total_pages'] }}&status={{ status }}&aid={{ aid }}&keywords={{ keywords }}" class="demo-pli-arrow-right">尾页</a></li>
									</ul>
									{% endif %}
								</div>
							</div>
						</div>
					</div>
				</div>
			{% endif %}
		</div>
	</div>
</div>
<!--日志模块结束--!>

