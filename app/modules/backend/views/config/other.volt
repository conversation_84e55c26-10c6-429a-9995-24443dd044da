<div class="tab-base">

	<?php $this->flashSession->output(); ?>
	<!--Nav Tabs-->
	<ul class="nav nav-tabs">
		<li class="active">
			<a data-toggle="tab" href="#">其他设置</a>
		</li>
	</ul>

	<!--Tabs Content-->
	<div class="tab-content">
		<div class="tab-pane fade active in">
			<div class="panel">
				{#<div class="panel-heading">#}
				{#<h3 class="panel-title">Sample Toolbar</h3>#}
				{#</div>#}
				<div class="panel-body">
					<form class="form-horizontal form-padding" method = 'post'>
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">每天可提现</label>
							<div class="col-xs-12 col-sm-4 col-md-4 col-lg-4">
								<input type="text" name="maxWithdraw" placeholder="设置每个用户每天最大可提现金额，0表示无限" class="form-control" value="{%if configInfo['maxWithdraw'] is defined%}{{configInfo['maxWithdraw']}}{%endif%}">
							</div>
						</div>
						{% if hostType == "kk" %}
						<div  class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">提现教程文档</label>
							<div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
								{% if configInfo["course"] is defined %}
								<?php echo Dhc\Component\MyTags::tpl_ueditor('course',$configInfo["course"])?>
								{% else %}
								<?php  echo Dhc\Component\MyTags::tpl_ueditor('course')?>
								{% endif %}
							</div>
						</div>
						<div  class="form-group">
                            <label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">支付提示语</label>
                            <div class="col-xs-12 col-sm-8 col-md-8 col-lg-6">
                                {% if configInfo["course"] is defined %}
                                <?php echo Dhc\Component\MyTags::tpl_ueditor('payInfo',$configInfo["payInfo"])?>
                                {% else %}
                                <?php  echo Dhc\Component\MyTags::tpl_ueditor('payInfo')?>
                                {% endif %}
                            </div>
                        </div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">提现最低房屋等级</label>
							<div class="col-xs-12 col-sm-4 col-md-4 col-lg-4">
								<input type="text" name="minWithdrawGrade" placeholder="" class="form-control" value="{%if configInfo['minWithdrawGrade'] is defined%}{{configInfo['minWithdrawGrade']}}{%endif%}">
							</div>
						</div>

						{% endif %}
						{% if hostType == "chuangjin" %}
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">交易自动撤销</label>
							<div class="col-xs-12 col-sm-4 col-md-4 col-lg-4">
								<select name="isCrontab" class="form-control">
									<option value="1" {% if configInfo != '' %}{% if configInfo['isCrontab'] == 1 %}selected{% endif %}{% endif %}>启用</option>
									<option value="0" {% if configInfo != '' %}{% if configInfo['isCrontab'] == 0 %}selected{% endif %}{% endif %}>禁用</option>
								</select>
							</div>
						</div>
						{% endif %}
						<div class="form-group">
							<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label"></label>
							<div class="col-xs-12 col-sm-4 col-md-4 col-lg-4">
								<button class="btn btn-success" type="submit">提交</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
