<?php

namespace Dhc\Modules\Frontend\Controllers;

use Dhc\Component\VerifyImage;
use Dhc\Models\User;

class IndexController extends ControllerBase
{

	public function indexAction() {
		if($_SERVER["HTTP_HOST"] === "kknc.bbscodes.com"){
			header("Location:../farm/");
		}else{
			header("Location:../web/");
		}
		$this->view->disable();

	}


	public function wechatAction()
	{
		/** @var Request $request */
		$request = $this->request;
		$uid = $request->get('state', 'int');
		$code = $request->get('code', 'int');
		if (empty($uid) || empty($code)) {
			echo '微信登录失败，请重试或联系管理员[01]！';
			exit;
		}
		$user = User::findFirst($uid);
		if (empty($user)) {
			echo '微信登录失败，请重试或联系管理员[02]！';
			exit;
		}

		$result = $this->curl_get('https://api.weixin.qq.com/sns/oauth2/access_token?appid=wxb126c714318918d6&secret=f0e240e6e61990d5d8884ed9c1eff577&code=' . $_GET['code'] . '&grant_type=authorization_code');
		$result = json_decode($result, true);
		if (empty($result['openid'])) {
			echo '微信登录失败，请重试或联系管理员[03]！';
			exit;
		}
		$user->openid = $result['openid'];
		if (!$user->save()) {
			echo '微信登录失败，请重试或联系管理员[04]！';
			exit;
		} else {
			header('Location: /web/#/userCenter/');
			exit;
		}
	}

	private function curl_get($url)
	{
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		$output = curl_exec($ch);
		curl_close($ch);
		return $output;
	}

	public function infoAction() {
		$this->view->disable();
		$verify = new VerifyImage();
		$verify->entry(1);
	}

	public function checkAction() {
		$this->view->disable();
		$code = $this->request->get('code');
		$verify = new VerifyImage();
//		var_dump($verify->check($code, 1));
	}

	public function notFoundAction() {
		$router = $this->di->get("router");
		echo "你要寻找的页面不存在！<br>";
		echo "链接：" . $this->request->getURI() . "<br>";
		echo "模块：" . $router->getModuleName() . "<br>";
		echo "控制器：" . $router->getControllerName() . "<br>";
		echo "方法：" . $router->getActionName() . "<br>";
		$this->view->disable();
	}
}
