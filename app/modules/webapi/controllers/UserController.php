<?php

namespace Dhc\Modules\Webapi\Controllers;

use function Dhc\Func\Common\getArrayKey;
use function Dhc\Func\Common\object2array;
use Dhc\Library\Distribution;
use Dhc\Library\Sms;
use Dhc\Models\Config;
use Dhc\Models\Dailydate;
use Dhc\Models\DistributionList;
use Dhc\Models\Orchard;
use Dhc\Models\OrchardLand;
use Dhc\Models\OrchardUser;
use Dhc\Models\Order;
use Dhc\Models\Product;
use Dhc\Models\Recharge;
use Dhc\Models\TelMessage;
use Dhc\Models\User;
use Dhc\Models\UserConfig;
use Dhc\Models\UserCost;
use Dhc\Models\UserGive;
use Dhc\Models\UserLog;
use Dhc\Models\UserProduct;
use Dhc\Models\UserRenascence;
use Dhc\Models\UserVirtual;
use Dhc\Models\UserWithdraw;
use Dhc\Models\WithdrawAddress;
use Phalcon\Exception;
use Phalcon\Paginator\Adapter\Model as PaginatorModel;

class UserController extends ControllerBase
{
	public $pindex = 10;

	//用户信息
	public function userInfoAction()
	{
		//个人信息
		$id = $this->uid;
		if ($this->request->isPost()) {
			$user = new User();
			$userInfo = $user->findFirst(
				array(
					'conditions' => "id = $id",
					'columns' => "nickname,avatar,coing,Frozen,Frozen,user,id,smallfruit,channelRebate,telphone,idcardStatus,voucher"
				)
			);
			$payPassword = User::findFirst(
				[
					'conditions' => "id = $id",
					'columns' => 'payPassword'
				]
			);
			if (empty($payPassword->payPassword)) {
				$payPasswordStatus = 9;
			} else {
				$payPasswordStatus = 1;
			}
			$userInfo->gold = (floatval($userInfo->coing) + floatval($userInfo->Frozen));
			$mobile = $userInfo->user;
			$userLoginTime = UserLog::findFirst(
				array(
					'conditions' => "user = '$mobile'",
					'columns' => 'logintime',
					'order' => "logintime DESC",
					'limit' => 1
				)
			);
			if ($userLoginTime) {
				$userInfo->logintime = $userLoginTime->logintime;
			}
			$data['userInfo'] = $userInfo;
			// 获取土地价值/类型
			$orchardConfig = new Orchard;
			$landUpInfo = $orchardConfig->findFirst([
				'columns' => 'landUpInfo'
			]);
			$landPrice = $orchardConfig->getLandPrice($landUpInfo->landUpInfo);

			// 获取产品价值/类型
			$landProductPrice = Order::find([
				'conditions' => 'status = 1',
				'columns' => 'sid, price',
				'order' => 'endtime DESC',
				'group' => 'sid'
			]);
			$landProductPrice = getArrayKey(object2array($landProductPrice), 'sid');
			// 拥有的土地数量/类型
			$landList = OrchardLand::find([
				'conditions' => "uid = '{$this->uid}'",
				'columns' => 'goodsId, goodsNums, landLevel'
			]);
			$landPriceAll = 0;
			$landProductPriceAll = 0;
			foreach ($landList as $land) {
				if ($land->landLevel != 1) {
					$landPriceAll += @$landPrice[$land->landLevel]['price'];
				}
				if ($land->goodsId > 0 && isset($landProductPrice[$land->goodsId])) {
					$landProductPriceAll += $landProductPrice[$land->goodsId]['price'] * $land->goodsNums;
				}
			}
			$data['landPriceAll'] = sprintf('%.4f', $landPriceAll);
			$data['landProductPriceAll'] = sprintf('%.4f', $landProductPriceAll);

			// 计算钻石价值
			$diamondPrice = OrchardUser::findFirst([
				'conditions' => "uid = '{$this->uid}'",
				'columns' => 'diamonds'
			]);
			$diamondPrice = $diamondPrice['diamonds'];
			$data['diamondPrice'] = sprintf('%.4f', $diamondPrice / 100); // 汇率
			//支付密码开启状态
			$data['payPassword'] = $payPasswordStatus;
			//kk
			if(USER_TYPE == "kk") {
				$data["tiXianImg"] =$userInfo->voucher;
				$kkmessage = Config::findFirst("key = 'game'");
				$kkmessage = unserialize($kkmessage->value);
				$data["giveStatus"] = $kkmessage["fruits"];
				$other = Config::findFirst("key = 'other'");
				$data["other"] = unserialize($other->value);
				$data["other"]["payInfo"] = htmlspecialchars_decode($data["other"]["payInfo"]);
			}elseif(USER_TYPE == "chuangjin"){
				$kkmessage = Config::findFirst("key = 'game'");
				$kkmessage = unserialize($kkmessage->value);
				$data["giveStatus"] = $kkmessage["fruits"];
			}
//			$url = 'http://' . $_SERVER['HTTP_HOST'];
//			$data['ref1'] = $this->erweimas($url.'/web/#/register/'.$this->uid,'register');
//			$data['ref2'] = $this->erweimas($url.'/farm/?ref='.$this->uid,'farm');
			$this->ajaxResponse($data, '用户基本信息', '0');
		}
	}

	//所持产品信息
	public function havingProductAction()
	{
		if ($this->request->isPost()) {
			$product = new UserProduct();
			$td = strtotime(date('Y-m-d', TIMESTAMP));
			$uid = $this->uid;
			if (empty($uid)) {
				$this->ajaxResponse('', '用户uid错误', '1');
			}
			$productList = $product->find(
				array(
					'conditions' => "uid = {$uid}",
					'order' => 'id'
				)
			);
			$data = ['sms' => [], 'product' => []];
			$user = User::findFirst("id = {$uid}");
			if ($user->smallfruit == '1') {
				$giveNum = UserGive::sum(
					[
						"column" => "number",
						"conditions" => "uid = {$uid} AND createtime > {$td}",
					]
				);
				$data['sms'] = [
					'isOpen' => true,
					'giveNum' => $giveNum
				];
			} else {
				$data['sms'] = [
					'isOpen' => false,
					'giveNum' => 0
				];
			}
			foreach ($productList as $value) {
				$sid = $value->sid;
				$price = $this->getProductPrice($sid);
				$number = $value->number;
				$title = $this->getProductName($sid);
				$data['product'][] = array(
					'sid' => $sid,
					'number' => $number,
					'title' => $title->title,
					'thumb' => $title->thumb,
					'price' => $price,
					'frozen' => $value->frozen,
				);
			}
			if (!empty($data)) {
				$this->ajaxResponse($data, '用户产品资产', '0');
			} else {
				$this->ajaxResponse('', '一无所有', '1');
			}
		}
	}

	//经营状态
	public function managementAction()
	{
		if ($this->request->isPost()) {

			$id = $this->uid;
			if (empty($id)) {
				$this->ajaxResponse('', '用户id错误', '1');
			}
			$product = new UserProduct();
			$productList = $product->find("uid  = $id ORDER BY id DESC");
			foreach ($productList as $value) {
				$sid = $value->sid;
				$price = $value->number * $this->getProductPrice($value->sid);
				$sellPrice = $this->getSellPrice($id, $sid);
				$buyPrice = $this->getBuyPrice($id, $sid);
				$data[] = array(
					'sid' => $value->sid,
					'title' => $this->getProductName($sid)->title,
					'thumb' => $this->getProductName($sid)->thumb,
					'price' => $price,
					'sellprice' => $sellPrice,
					'buyprice' => $buyPrice,
					'reward' => ($sellPrice + $price) - $buyPrice
				);
			}
			if (!empty($data)) {
				$this->ajaxResponse($data, '经营状况', '0');
			} else {
				$this->ajaxResponse('', '暂无记录', '1');
			}
		}
	}

	//修改密码
	public function restPwdAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			$user = new User();
			$restUser = $user->findFirst("id = $uid");
			$mobile = $restUser->user;
			if (!$restUser) {
				$this->ajaxResponse('', '用户尚未注册', '');
			}
			if ($this->request->getPost('password')) {
				$Pwd = $this->request->getPost('password');
				$newPassword = $this->request->getPost('newPassword');
				$rePassword = $this->request->getPost('rePassword');
				if ($newPassword != $rePassword) {
					$this->ajaxResponse('', '两次密码不一致', '1');
				}
				$salt = $restUser->salt;
				$password = sha1($salt . $Pwd);
				$userInfo = $user->findFirst("user = '$mobile'");
				if ($password !== $userInfo->password) {
					$this->ajaxResponse('', '旧密码填写错误', '1');
				}
				$newPassword = sha1($salt . $newPassword);
				$restUser->password = $newPassword;
				$restsult = $restUser->update();
				if ($restsult) {
					$this->ajaxResponse('success', '修改成功', '0');
				} else {
					$this->ajaxResponse('error', '修改失败', '');
				}
			}

		}
	}
	//用户交易记录

	/**      getEntrust
	 * @pram uid 用户id
	 * @pram productId 产品id
	 * @pram 'uid 用户id type 类型  createtime 创建时间,endtime 结束时间
	 * @return 返回查询结果
	 */
	public function getEntrustAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			$condition = "uid = $uid and status = 1";
			$product = intval($this->request->getPost('productId'));
			$page = $this->request->getPost('page');
			if (empty($page)) {
				$page = 1;
			}
			if (!empty($product)) {
				$condition .= " and sid = $product";
			}
			$type = $this->request->getPost('condition');
			if (isset($type)) {
				if ($type == 1) {
					$condition .= " and type = $type";
				}
				if ($type == 2) {
					$condition .= " and type = 0";
				}
			}
			$createtime = $this->request->getPost('createtime');
			if (!empty($createtime)) {
				$createtime = strtotime($createtime);
				if (!empty($condition)) {
					$condition .= " and createtime > $createtime";
				} else {
					$condition .= " createtime > $createtime";
				}
			}
			$endTime = $this->request->getPost('endtime');
			if (!empty($endTime)) {
				$endTime = strtotime($endTime);
				if (!empty($createtime)) {
					$condition .= " and endtime < $endTime";
				} else {
					$condition .= "  endtime < $endTime";
				}
			}

			$pageoffset = ($page - 1) * $this->pindex;
			$sql = "SELECT * FROM dhc_trade_order WHERE $condition ORDER BY createtime DESC   LIMIT $pageoffset,$this->pindex";
			$order = $this->db->query($sql)->fetchAll();
			$sql1 = "SELECT COUNT(*) AS nums FROM dhc_trade_order WHERE $condition";
			$total = $this->db->query($sql1)->fetch();
			$list['list'] = $order;
			$list['total_pages'] = ceil($total['nums'] / $this->pindex);
			if (!empty($list)) {
				$this->ajaxResponse($list, '个人交易委托信息', '0');
			} else {
				$this->ajaxResponse('', '暂无交易数据', '1');
			}
		}
	}
	//用户产品重生

	/**  action  = renascence  controller User
	 * @pram productId 产品id
	 * @pram uid 用户id
	 * @pram number 重生数量
	 * @pram charge    手续费
	 * @return  true/false
	 */
	public function renascenceAction()
	{
		if ($this->request->isPost()) {
			$productId = $this->request->getPost('sid');
			$uid = $this->uid;
			$number = $this->request->getPost('number', 'int');
			if ($number % 1000 !== 0) {
				$this->ajaxResponse('', '重生数量必须为1000的倍数', '1');
			}
			if ($number <= 0) {
				$this->ajaxResponse('error', '重生数量不能为负', '1');
			}
			$charge = $this->getProductPrice($productId) * $number * 0.1;
			$this->db->begin();
			$sql = "SELECT * FROM dhc_user_product WHERE uid = '{$uid}' AND sid = '{$productId}' FOR UPDATE ";
			$productNum = $this->db->query($sql)->fetch();
			if (empty($productNum)){
				$this->db->rollback();
				$this->ajaxResponse('error','操作失败，产品扣除失败','1');
			}
			$userProduct = UserProduct::findFirst("uid =$uid and sid = $productId AND number = '{$productNum['number']}'");
			if ($userProduct->number < $number) {
				$this->ajaxResponse('', '所持有数量不足', '1');
			}
			$zhongziId = $this->getZhongzi('种子');
			$meigui = $this->getZhongzi('玫瑰');
			if ($zhongziId == $productId) {
				$this->ajaxResponse('error', '种子不可以重生', '1');
			}
			if ($productId == 80011) {
				$this->ajaxResponse('error', '该产品不可以重生', '1');
			}

			$sql = "SELECT * FROM dhc_user WHERE id = '{$uid}'";
			$userNumber = $this->db->query($sql)->fetch();
			$user = User::findFirst("id = $uid AND coing = ".$userNumber['coing']);
			if (empty($user)){
				$this->db->rollback();
				$this->ajaxResponse('error','操作失败，手续费扣除失败','1');
			}
			if ($user->coing < $charge) {
				$this->db->rollback();
				$this->ajaxResponse('error', '操作失败，重生手续费不足', '1');
			}
			$user->coing -= $charge;
			$this->saveTradeLogs(array("uid" => $uid, 'num' => $charge, 'type' => 'dedcoing', 'log' => "用户重生扣除手续费" . $charge));
			$userCoing = $user->update();
			$userProductNumber = UserProduct::findFirst("uid =$uid and sid = $zhongziId");
			$this->clockTable('dhc_user_product', "uid =$uid and sid = $zhongziId");
            if ($userProductNumber) {
				$userProductNumber->number += $number;
				$this->saveTrade(array("uid" => $uid, 'num' => $number, 'fee' => $charge, 'type' => '产品重生'));
				$this->saveTradeLogs(array("uid" => $uid, 'num' => $number, 'type' => 'dedproduct', 'log' => "用户重生增加产品$zhongziId-" . $number));
				$riseNum = $userProductNumber->update();
			} else {
				$userRenascence = new UserProduct();
				$userRenascence->uid = $uid;
				$userRenascence->sid = $zhongziId;
				$userRenascence->number = $number;
				$newProduct = $userRenascence->save();
			}
			$userProduct = UserProduct::findFirst("uid =$uid and sid = $productId");
			$this->clockTable('dhc_user_product', "uid =$uid and sid = $productId");
			$userProduct->number -= $number;
			$this->saveTradeLogs(array("uid" => $uid, 'num' => $number, 'type' => 'dedproduct', 'log' => "用户重生扣除产品$productId-" . $number));
			$fallNum = $userProduct->update();

			if ($userCoing !== false) {
				$userCost = new UserCost();
				$orderNumber = 'CS' . date("YmdHi", TIMESTAMP) . str_pad($uid, '6', '0', STR_PAD_LEFT);
				$userCost->uid = $uid;
				$userCost->type = '果实重生';
				$userCost->createtime = TIMESTAMP;
				$userCost->sum = $charge;
				$userCost->charge = $charge;
				$userCost->status = '1';
				$userCost->orderNumber = $orderNumber;
				$userCostResult = $userCost->save();
				if (!$userCostResult) {
					foreach ($userCost->getMessages() as $message) {
						echo $message;
					}
					die;
				}
			}
			//TODO 如果用户增加种子或者更新用户存量 下面会提示某一个是未定义的变量
			if (@$riseNum || @$newProduct) {
				if ($fallNum && $userCoing && $userCostResult) {
					$userRenascence = new UserRenascence();
					$userRenascence->uid = $uid;
					$userRenascence->title = $this->getProductName($productId)->title;
					$userRenascence->price = $this->getProductPrice($productId);
					$userRenascence->number = $number;
					$userRenascence->createtime = TIMESTAMP;
					$charge = $this->getProductPrice($productId) * $number * 0.1;
					$userRenascence->charge = $charge;
					$userRenascence->save();
					// 分销处理 zl
					try {
						$dis = new Distribution($userRenascence->uid, $charge, 2, '果实重生');
						$dis->start();
					} catch (Exception $e) {
						$this->db->rollback();
						$this->ajaxResponse('', '推广费用计算失败，请审核：' . $e->getMessage(), '1');
					}
					$this->db->commit();
					$this->ajaxResponse('success', '重生成功', '0');
				} else {
					$this->db->rollback();
					$this->ajaxResponse('error', '重生失败', '1');
				}
			}

		}
	}

	//用户重生记录
	public function renascenceHistoryAction()
	{
		$uid = $this->uid;
		if ($this->request->isPost()) {
			$page = $this->request->getPost('page');
			if (empty($uid)) {
				$this->ajaxResponse('', '用户id为空', '1');
			}
			$renascence = UserRenascence::find("uid  = $uid ORDER BY createtime DESC");
			$paginator = new PaginatorModel(
				array(
					"data" => $renascence,
					"limit" => 10,
					"page" => $page
				)
			);
			$page = $paginator->getPaginate();
			$data['list'] = $page->items;
			$data['total'] = $page->total_pages;
			if (!empty($data)) {
				$this->ajaxResponse($data, '用户重生记录', '0');
			} else {
				$this->ajaxResponse('', '暂无记录', '1');
			}
		}
	}
	//用户果实赠送

	/**  giveProduct
	 * @pram sid 产品id
	 * @pram uid 赠送用户id
	 * @pram accept 接受用户id 或者帐号
	 * @pram number 赠送数量
	 * @pram imgCode 图片验证码
	 * @pram telCode 短信验证码
	 * @pram mobile 接受短信验证码的手机号
	 * @return  true /false
	 */
	public function giveProductAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			$productId = intval($this->request->getPost('sid'));
			$number = intval($this->request->getPost('number'));
			$accept = intval($this->request->getPost('accept'));
			$payPassword = $this->request->getPost('payPassword');
			$productConfig = Config::findFirst("key = 'productConfig'");
			if (!empty($productConfig)) {
				$value = unserialize($productConfig->value);
			}
			if (USER_TYPE == 'huangjin') {
				$this->checkGive($uid, $productId, $number);
			}
			$giveLevel = isset($value['giveLevel']) ? $value['giveLevel'] : 4;
			$acceptLevel = isset($value['acceptLevel']) ? $value['acceptLevel'] : 4;
			//赠送需要是10的整数倍
			if ($number % 10 != 0) {
				$this->ajaxResponse('error', '赠送数量错误', '1');
			}
			//验证支付密码

			if (empty($payPassword)) {
				$this->ajaxResponse('error', '操作失败，请填写支付密码', '1');
			}
			$checkResult = $this->checkPayPassword($uid, $payPassword);
			if ($checkResult == false) {
				$this->ajaxResponse('error', '操作失败，支付密码错误', '1');
			}

			//屏蔽掉支付需要图片验证和短信验证码 使用支付密码
//            $smartFruit = User::findFirst("id = $uid");
//            $td = strtotime(date('Y-m-d'));
//
//            // 判断本次交易是否需要短信验证码
//            $useNumbers = UserGive::findFirst(//已赠送数量
//                [
//                    'conditions'=>"uid = $uid AND productid = $productId AND createtime>$td",
//                    'columns'	 =>'SUM(number) as nums'
//                ]
//            );
//			$useNumbers = $useNumbers->nums ? $useNumbers->nums : 0;
//			if ($smartFruit->smallfruit == '0' && ($number > 200 || ($number + $useNumbers) > 200)) {
//				if (empty($telCode)) {
//					$this->ajaxResponse('', '短信验证码不能为空', '1');
//				}
//				$tel = new TelMessage();
//				$giveUser = $this->getUserMobile($uid);
//				$tMessage = $tel->findFirst("mobile = $giveUser and code = '$telCode'");
//				if (!$tMessage) {
//					$this->ajaxResponse('手机验证码填写错误', '转赠失败,您的手机验证码填写错误', '1');
//				}
//			}

			//判断用户的产品是否存在
			$giveProductInfo = UserProduct::findFirst("uid = $uid AND sid = $productId");
			$giveProductNumber = $giveProductInfo->number;//用户产品信息
			//判断赠送数量是否合法
			if ($number <= 0) {
				$this->ajaxResponse('error', '非法请求', '1');
			}
			if (!$giveProductInfo || $giveProductNumber < $number) {
				$this->ajaxResponse('error', '转赠失败,您的产品数量不足', '1');
			}
			//判断赠送用户是否达到产品赠送的要求(是否实名认证或者用户是否存在)
			$giveInfo = User::findFirst("id = {$uid}");
			if (empty($giveInfo->realname)) {
				$this->ajaxResponse('error', '转赠失败,用户尚未实名认证' . '1');
			}
			//判断赠送用户农场等级
			$giveOrchard = OrchardUser::findFirst("uid = '{$uid}'");
			if (!empty($giveOrchard)) {
				if ($giveOrchard->grade < $giveLevel) {
					$this->ajaxResponse('error', '转赠失败,您的农场须达到' . $giveLevel . '级方可赠送', '1');
				}
			} else {
				$this->ajaxResponse('error', '转赠失败,您的农场须达到' . $giveLevel . '级方可赠送', '1');
			}
			//接受方条件判断
			$acceptInfo = User::findFirst("id = {$accept} OR user = {$accept}");
			if (empty($acceptInfo)) {
				$this->ajaxResponse('error', '转赠失败,接收方用户不存在，请确认后输入', '1');
			}
			if (empty($acceptInfo->realname)) {
				$this->ajaxResponse('error', '转赠失败,接收方用户尚未实名认证', '1');
			}

			$acceptOrchard = OrchardUser::findFirst("uid = {$acceptInfo->id}");
			if (!empty($acceptOrchard)) {
				if ($acceptOrchard->grade < $acceptLevel) {
					$this->ajaxResponse('error', '转赠失败,接收方需农场达到' . $acceptLevel . '级方可接收', '1');
				}
			} else {
				$this->ajaxResponse('error', '转赠失败,接收方需农场达到' . $acceptLevel . '级方可接收', '1');
			}
			//接受索取金币
			//获取索取金币数量
			$giveGold = intval($this->request->getPost('fruits'));
			if ($giveGold < 0) {
				$this->ajaxResponse('error', '索取金币数量错误', '1');
			}

			//获取当前产品价格，用于计算手续费
			$productPrice = $this->getProductPrice($productId);
			//如果条件合法 进行记录添加
			$this->db->begin();

			if (!$giveProductInfo || $giveProductNumber < $number) {
				$this->ajaxResponse('error', '转赠失败,您的产品数量不足', '1');
			}
			$userGive = new UserGive();
			$userGive->uid = $uid;
			$userGive->productid = $productId;
			$userGive->accept = $accept;
			$userGive->number = $number;
			$userGive->fee = $productPrice * $number * 0.1;//固定收取当前市价的10%
			$userGive->giveGold = $giveGold;
			$userGive->title = $this->getProductName($productId)->title;
			$userGive->createtime = TIMESTAMP;
			$userGiveResult = $userGive->save();
			if ($userGiveResult) {
				$sql = "SELECT * FROM dhc_user_product WHERE uid = $uid AND sid = $productId FOR UPDATE ";
				$nums = $this->db->query($sql)->fetch();
				$giveProduct = UserProduct::findFirst("uid = '{$uid}' AND sid = '{$productId}' AND number = '{$nums['number']}'");
				if (empty($giveProduct)){
					$this->db->rollback();
					$this->ajaxResponse('error','转赠失败，扣除产品失败','1');
				}
				if ($giveProduct) {
					$havingProduct = $giveProduct->number;
					if ($havingProduct < $number) {
						$this->db->rollback();
						$this->ajaxResponse('error', '转赠失败,产品数量不足', '1');
					}
					$giveProduct->number -= $number;
					$giveProduct->frozen += $number;
					$havingProducts = $giveProduct->number;
					$giveProductResult = $giveProduct->update();
					if (empty($giveProductResult)) {
						$this->db->rollback();
						$this->ajaxResponse('error', '转赠失败，产品扣除失败', '1');
					}
					$this->saveTradeLogs(array("uid" => $uid, 'num' => $number, 'type' => 'dedproduct', 'log' => "用户原有产品" . $havingProduct . "用户赠送扣除产品$productId-" . ($number) . "后为" . $havingProducts));
					$this->saveTradeLogs(array("uid" => $uid, 'num' => $number, 'type' => 'addfrozenproduct', 'log' => "用户赠送增加冻结产品$productId-" . ($number)));
					$this->db->commit();
					$this->ajaxResponse('success', '赠送成功', '0');
				} else {
					$this->db->rollback();
					$this->ajaxResponse('error', '赠送失败', '1');
				}
			} else {
				$this->db->rollback();
				$this->ajaxResponse('error', '赠送失败', '1');
			}
		}
	}
	//用户赠送记录

	/** User controller giveHistory Action
	 * @pram uid 产品id
	 * @pram id  订单id
	 * @pram sid 产品id
	 * @pram type(cancel) 如果撤销就加上 没有撤销就不加
	 * @return 赠送记录
	 */
	public function giveHistoryAction()
	{
		if ($this->request->isPost()) {
			$allowType = ['accept', 'cancel', 'refuse', 'list'];
			$type = $this->request->getPost('type');
			if (empty($type)) {
				$type = 'list';
			}
			if (!in_array($type, $allowType)) {
				$this->ajaxResponse('error', '非法操作', '1');
			}
			$uid = $this->uid;
			if ($type != 'list') {
				// 判断是否有权限对记录操作
				$id = intval($this->request->getPost('id'));
				if (empty($id)) {
					$this->ajaxResponse('error', '操作失败,记录不存在', '1');
				}
				$userGive = UserGive::findFirst("id = '{$id}' AND status = 0");
				$sql = "SELECT * FROM dhc_user_give WHERE id = $id AND status = 0 FOR UPDATE";
				$this->db->query($sql);
				if (empty($userGive)) {
					$this->ajaxResponse('error', '操作失败,记录不存在', '1');
				}
			}
			//接受赠送产品
			if ($type == 'accept') {
				$userMobile = $this->getMoblie($uid);
				$payPassword = $this->request->getPost('payPassword');
				if (!in_array($userGive->accept, [$userMobile, $uid])) {
					$this->ajaxResponse('error', '操作失败,记录错误', '1');
				}
				if (USER_TYPE == 'huangjin' || USER_TYPE == 'duojin') {
					if (empty($payPassword)) {
						$this->ajaxResponse('error', '操作失败,请填写支付密码', '1');
					}
					$result = $this->checkPayPassword($uid, $payPassword);
					if (empty($result)) {
						$this->ajaxResponse('error', '操作失败,支付密码错误', '1');
					}
				}
				$this->db->begin();
				//接收方金币扣除
				$acceptUserInfo = User::findFirst("id = '{$uid}'");
                $this->clockTable('dhc_user',"id = '{$uid}'");
				if ($acceptUserInfo->coing < ($userGive->giveGold + $userGive->fee)) {
					$this->ajaxResponse('error', ',操作失败,用户金币不足', '1');
				}
				$havingCoing = $acceptUserInfo->coing;
				$acceptUserInfo->coing -= ($userGive->giveGold + $userGive->fee);
				$havingCoings = $acceptUserInfo->coing;
				$acceptUserInfoResult = $acceptUserInfo->update();
				if (empty($acceptUserInfoResult)) {
					$this->db->rollback();
					$this->ajaxResponse('error', '操作失败，用户金币扣除失败', '1');
				} else {
					$this->distribution(array('uid' => $uid, 'num' => $userGive->fee, 'type' => 2, 'remark' => '果实接收手续费'));
					$this->saveTradeLogs(array("uid" => $uid, 'num' => ($userGive->giveGold + $userGive->fee), 'type' => 'dedcoing', 'log' => "原有" . $havingCoing . '更新为' . ($havingCoings) . "用户接受成功扣除金币" . ($userGive->giveGold + $userGive->fee)));
				}
				//接受方产品增加
				$acceptProduct = UserProduct::findFirst("uid = '{$uid}' AND sid = '{$userGive->productid}'");
				$this->clockTable('dhc_user_product',"uid = '{$uid}' AND sid = '{$userGive->productid}'");
                if (empty($acceptProduct)) {
					$acceptProduct = new  UserProduct();
					$acceptProduct->uid = $uid;
					$acceptProduct->sid = $userGive->productid;
					$havingProduct = '0';
					$acceptProduct->number = $userGive->number;
					$havingProducts = $acceptProduct->number;
					$acceptProduct->createtime = TIMESTAMP;
					$acceptProductResult = $acceptProduct->save();
				} else {
					$this->clockTable('dhc_user_product', "uid = '{$uid}' AND sid = '{$userGive->productid}'");
					$havingProduct = $acceptProduct->number;
					$acceptProduct->number += $userGive->number;
					$havingProducts = $acceptProduct->number;
					$acceptProduct->updatetime = TIMESTAMP;
					$acceptProductResult = $acceptProduct->update();
				}
				if (empty($acceptProductResult)) {
					$this->db->rollback();
					$this->ajaxResponse('error', '操作失败,用户产品添加失败', '1');
				} else {
					$this->saveTradeLogs(array("uid" => $uid, 'num' => $userGive->number, 'type' => 'addproduct', 'log' => "原有产品" . $havingProduct . "更新后" . "{$havingProducts}用户接受成功增加产品{$userGive->productid}-{$userGive->number}"));
				}

				//赠送方冻结产品扣除
				$giveProductInfo = UserProduct::findFirst("uid = '{$userGive->uid}' AND sid = '{$userGive->productid}'");
				$this->clockTable('dhc_user_product',"uid = '{$userGive->uid}' AND sid = '{$userGive->productid}'");
                if ($giveProductInfo) {
                    if ($giveProductInfo->frozen < $userGive->number){
                        $this->db->rollback();
                        $this->ajaxResponse('error','赠送失败，赠送用户冻结金币异常','1');
                    }
					$giveProductInfo->frozen -= $userGive->number;
					$giveProductInfoResult = $giveProductInfo->update();
					if (empty($giveProductInfoResult)) {
						$this->db->rollback();
						$this->ajaxResponse('error', '赠送失败，赠送用户产品扣除失败', '1');
					} else {
						$this->saveTradeLogs(array("uid" => $userGive->uid, 'num' => $userGive->number, 'type' => 'dedfrozenproduct', 'log' => ($giveProductInfo->frozen) . "用户接受成功扣除赠送方冻结产品$userGive->productid-" . ($userGive->number)));
					}
				}
				//赠送方金币增加
				$givefee = $this->getGiveFee(); //获取设置的用户金币索取手续费
				$giveUserInfo = User::findFirst("id = '{$userGive->uid}'");
                $this->clockTable('dhc_user',"id = '{$userGive->uid}'");
				if ($giveUserInfo) {
					$giveUserInfo->coing += ($userGive->giveGold * (1 - $givefee));
					$giveUserInfoResult = $giveUserInfo->update();
					if (empty($giveUserInfoResult)) {
						$this->db->rollback();
						$this->ajaxResponse('error', '操作失败，赠送方金币添加失败', '1');
					} else {
						$this->saveTradeLogs(array("uid" => $userGive->uid, 'num' => ($userGive->giveGold * (1 - $givefee)), 'type' => 'addcoing', 'log' => ($giveUserInfo->coing) . "用户赠送成功接受增加金币" . ($userGive->giveGold * (1 - $givefee))));
					}
				}
				//订单状态更新
				$userGive->status = 1;
				$userGiveResult = $userGive->update();
				if (empty($userGiveResult)) {
					$this->db->rollback();
					$this->ajaxResponse('error', '操作失败,记录操作失败', '1');
				}
				if ($acceptUserInfoResult && $acceptProductResult && $giveProductInfoResult && $giveUserInfoResult && $userGiveResult) {
					$this->db->commit();
					$this->ajaxResponse('success', '操作成功,接收成功', '0');
				}
			} elseif ($type == 'cancel') {
				//撤销订单操作
				//赠送用户产品操作
				if ($userGive->uid != $uid) {
					$this->ajaxResponse('error', '操作失败,记录错误', '1');
				}
				$this->db->begin();
				$giveUserProduct = UserProduct::findFirst("uid  = '{$uid}' AND sid = '{$userGive->productid}'");
				$sql = "SELECT * FROM dhc_user_product WHERE uid = $uid AND sid = $userGive->productid FOR UPDATE ";
				$this->db->query($sql);
				if ($giveUserProduct->frozen < $userGive->number) {
					$this->db->rollback();
					$this->ajaxResponse('error', '操作失败，数据更新错误', '1');
				}
				$giveUserProduct->number += $userGive->number;
				$giveUserProduct->frozen -= $userGive->number;
				$giveUserProductResult = $giveUserProduct->update();
				if (empty($giveUserProductResult)) {
					$this->db->rollback();
					$this->ajaxResponse('error', '操作失败,用户产品添加失败', '1');
				} else {
					$this->saveTradeLogs(array("uid" => $uid, 'num' => $userGive->number, 'type' => 'dedfrozenproduct', 'log' => ($giveUserProduct->frozen) . "用户撤销成功扣除冻结产品($userGive->productid)-" . ($userGive->number)));
					$this->saveTradeLogs(array("uid" => $uid, 'num' => $userGive->number, 'type' => 'addproduct', 'log' => ($giveUserProduct->number) . "用户撤销成功增加产品$userGive->productid-" . $userGive->number));
				}
				$userGive->status = 2;
				$userGiveResult = $userGive->update();
				if (empty($userGiveResult)) {
					$this->db->rollback();
					$this->ajaxResponse('error', '操作失败,订单操作失败', '1');
				}
				$this->db->commit();
				$this->ajaxResponse('success', '操作成功,撤销成功', '0');
			} elseif ($type == 'refuse') {
				$userMobile = $this->getMoblie($uid);
				if (!in_array($userGive->accept, [$userMobile, $uid])) {
					$this->ajaxResponse('error', '操作失败,记录错误', '1');
				}
				$this->userGiveReturn($id);
			} elseif ($type == 'list') {
				$userGive = UserGive::find("uid = '{$uid}' order by createtime DESC");
				$user = $this->getUserMobile($uid);
				$userAccept = UserGive::find("accept = '{$uid}' or accept = '{$user}' order by createtime DESC");
				$data = array(
					'give' => $userGive,
					'accept' => $userAccept
				);
				if ($userGive) {
					$this->ajaxResponse($data, '用户赠送礼物记录', '0');
				} else {
					$this->ajaxResponse('', '暂无赠送接收记录', '1');
				}
			}

		}
	}

	//设置用户支付密码（修改）
	public function setPayPasswordAction()
	{
		$uid = $this->uid;
		$telCode = $this->request->getPost('telCode');
		$imgCode = $this->request->getPost('imgCode');
		$payPassword = $this->request->getPost('payPassword');
		$rePayPassword = $this->request->getPost('rePayPassword');
		//判断两次密码是否一致
		if ($payPassword != $rePayPassword) {
			$this->ajaxResponse('error', '两次密码不一致', '1');
		}
		//验证图片验证码
		if (!empty($imgCode)) {
			$check = new UtilController();
			$result = $check->checkVerify($imgCode);
			if ($result == false) {
				$this->ajaxResponse('', '验证码错误', '1');
			}
		} else {
			$this->ajaxResponse('error', '操作失败,请输入验证码', '1');
		}
		//验证短信验证码
		if (!empty($telCode)) {
			$tel = new TelMessage();
			$userPhone = $this->getUserMobile($uid);
			$tMessage = $tel->findFirst("mobile = '{$userPhone}' and code = '{$telCode}'");
			if (!$tMessage) {
				$this->ajaxResponse('手机验证码填写错误', '操作失败,您的手机验证码填写错误', '1');
			}
		} else {
			$this->ajaxResponse('error', '请输入手机验证码', '1');
		}
		$userInfo = User::findFirst("id ='{$uid}'");
		$userInfo->payPassword = sha1($userInfo->salt . $payPassword);
		if ($userInfo->payPassword == $userInfo->password) {
			$this->ajaxResponse('error', '更新失败,支付密码不能和登录密码一致', '1');
		}
		$userInfoResult = $userInfo->update();
		if ($userInfoResult) {
			$this->ajaxResponse('success', '支付密码更新成功', '0');
		} else {
			$this->ajaxResponse('error', '支付密码更新失败', '1');
		}
	}

	public function getGiveFee()
	{
		$fee = Config::findFirst("key = 'game'");
		if ($fee) {
			$give = unserialize($fee->value);
			$givefee = $give['fee'];
			return $givefee;
		}
	}

	//获取用户手机号
	public function getUserMobile($uid)
	{
		$userInfo = User::findFirst("id = $uid");
		if (!$userInfo) {
			return false;
		}
		if (empty($userInfo->telphone)) {
			return $userInfo->user;
		} else {
			return $userInfo->telphone;
		}
	}
	//用户委托记录

	/** action getTrade Controller User
	 * @pram uid 用户id
	 * @pram sid 产品id
	 * @pram type 交易类型
	 * @pram createtime 开始时间
	 * @pram endtime 结束时间
	 * return  返回委托列表
	 */
	public function getTradeAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			if (empty($uid)) {
				$this->ajaxResponse('', '用户id为空', '1');
			}
			$sid = intval(trim($this->request->getPost('sid')));
			$type = $this->request->getPost('type');
			$page = $this->request->getPost('page');
			if (isset($type)) {
				if ($type >= 2) {
					$type = '';
				}
			}
			if (empty($page)) {
				$page = 1;
			}
			if ($sid === '0') {
				$sid = '';
			}
			$createtime = strtotime($this->request->getPost('createtime'));
			$endtime = strtotime($this->request->getPost('endtime'));
			$conditions = " uid = $uid ";
			if (!empty($sid)) {
				$conditions .= " and sid = $sid ";
			}
			if (!empty($createtime)) {
				$conditions .= " and createtime >= $createtime ";
			}
			if (!empty($endtime)) {
				$conditions .= " and endtime <= $endtime ";
			}
			if (isset($type) && !empty($type)) {
				$conditions .= " and type = $type ";
			}
//			$order = Order::find(
//				array(
//					'conditions'=>$conditions
//				)
//			);
			$pageoffset = ($page - 1) * $this->pindex;
			$sql = "SELECT * FROM dhc_trade_order WHERE $conditions  ORDER BY createtime DESC LIMIT $pageoffset,$this->pindex";
			$order = $this->db->query($sql)->fetchAll();
			$sql1 = "SELECT COUNT(*) AS nums FROM dhc_trade_order WHERE $conditions";
			$total = $this->db->query($sql1)->fetch();
			$list['list'] = $order;
			$list['total_pages'] = ceil($total['nums'] / $this->pindex);
			if (!empty($list)) {
				$this->ajaxResponse($list, '用户委托记录', '0');
			} else {
				$this->ajaxResponse('', '暂无记录', '1');
			}
		}

	}
	//获取个人产品信息

	/**  controller Market  action getProductInfo
	 * @param uid 用户id
	 * @pram sid 产品id
	 * @return data/false
	 */
	public function getProductInfoAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			if (empty($uid)) {
				$this->ajaxResponse('', '用户id为空', '1');
			}
			$user = User::findFirst("id = $uid");
			if (!$user) {
				$this->ajaxResponse('', '用户不存在', '1');
			}
			$sid = intval(trim($this->request->getPost('sid')));
			if (empty($sid)) {
				$this->ajaxResponse('', '产品id为空', '1');
			}
			$product = Product::findFirst("id = $sid");
			if (!$product) {
				$this->ajaxResponse('error', '产品不存在', '1');
			}
			//获取产品的最新价格和产品名称
			$productName = $this->getProductName($sid);
			$productPrice = $this->getProductPrice($sid);
			$productNumber = $this->getProductNum($uid, $sid);
			$data['num'] = $productNumber;
			$data['name'] = $productName->title;
			$data['price'] = $productPrice;
			$data['charge'] = $this->getCharge($sid)->poundage;
			if (!empty($data)) {
				$this->ajaxResponse($data, '产品信息', '0');
			}
		}
	}
	//获取推广列表

	/** getSpreadList 获取该用户的推广下线列表
	 * @pram uid 用户id
	 */
	public function getSpreadListAction()
	{
		$uid = $this->uid;
		if ($this->request->isPost()) {
			$page = $this->request->getPost('page');
//			$user = User::findFirst("id = $uid");
			$flag = $this->checkchannel($uid);
			$conditions = "superior REGEXP '^{$uid}-|-{$uid}-'";
			$sql = "SELECT id,createTime,superior,user FROM dhc_user WHERE $conditions ORDER BY createTime DESC ";
			$spreadUser = $this->db->query($sql)->fetchAll();
			$number = 0;
			$ids = array();
			foreach ($spreadUser as $key => &$value) {
				$superior = explode('-', $value['superior']);
				foreach ($superior as $k => $v) {
					if ($v == $uid) {
						if ($k >= 1) {
							if ($k >= 2) {
								$ids[] = $value['id'];
							}
							$value['level'] = '1';
						} else {
							$number++;
							$value['level'] = '0';
						}
					}
				}
			}
			foreach ($spreadUser as $key => &$val) {
				foreach ($val as $k => $v) {
					if (is_numeric($k)) {
						unset($val[$k]);
					}
				}
			}
			if (!$flag) {
				foreach ($spreadUser as $key => &$value) {
					foreach ($ids as $k => $v) {
						if ($v == $value['id']) {
							unset($spreadUser[$key]);
						}
					}
				}
			}
			$sum = count($spreadUser);
			$totalPage = $this->db->query("SELECT COUNT(*) FROM dhc_user WHERE $conditions")->numRows() / 10;
			$data = [
				"items" => $spreadUser,
				"current" => $page,
				"before" => max($page - 1, 1),
				"next" => min($page + 1, 1),
				"last" => max(intval($totalPage), 1),
				'number' => $number,
				'sum' => max($sum, 1),
				'total_page' => max($totalPage, 1)
			];

			if (!$spreadUser) {
				$this->ajaxResponse('', '没有找到相关记录', '1');
			}
			$this->ajaxResponse($data, '推广列表', '0');
		}
	}

	//检查是否为渠道代理
	public function checkchannel($uid)
	{
		$userInfo = User::findFirst("id =$uid");
		if ($userInfo->channelRebate > 0) {
			return true;
		} else {
			return false;
		}
	}
	//补充推广码  （尚未添加推广奖励）

	/**
	 * @pram uid 用户id
	 * @pram
	 */
	public function addSpreadAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			$this->session->set('spread', $uid);
			$teacher = $this->request->getPost('teacher');
			$user = User::findFirst("id = $uid");
			if (!$user) {
				$this->ajaxResponse('', '用户不存在', '1');
			}
			if ($teacher == $uid) {
				$this->ajaxResponse('error', '用户不能添加自己为导师', '1');
			}
			$superior = $user->superior;
			if (!empty($superior)) {
				$superiors = explode('-', $superior);
				$this->ajaxResponse($superiors[0], '该用户已有导师推荐', '0');
			}
			if ($teacher) {
				$userSuperior = User::findFirst("id =$uid");
				$teacherExit = User::findFirst("id= $teacher");
				if (!$teacherExit) {
					$this->ajaxResponse('导师不存在', '导师不存在', '1');
				}
				$superior = $teacherExit->superior;
				$teachers = explode('-', $superior);
				if (in_array($uid, $teachers)) {
					$this->ajaxResponse('error', '不能添加自己的下级为自己的上级', '1');
				}
				$this->db->begin();
				$userSuperior->superior = $teacher . '-' . $teacherExit->superior;
				$userSuperior = $userSuperior->update();
				if (!empty($userSuperior)) {
					$result = $this->subordinate($uid);
					if (!empty($result)) {
						$this->db->commit();
						$this->ajaxResponse($teacher, '导师推荐码填充成功', '0');
					} else {
						$this->db->rollback();
					}
				} else {
					$this->db->rollback();
					$this->ajaxResponse('error', '导师推荐码添加失败，请联系管理员', '1');
				}

			}
		}
	}

	/**
	 * 填推广码时候更新下级推广码
	 *
	 */
	public function subordinate($uid)
	{
		$conditions = "superior REGEXP '^{$uid}-|-{$uid}-'";
		$sql = "SELECT id,superior FROM dhc_user WHERE $conditions";
		$userList = $this->db->query($sql)->fetchAll();
		$teacherInfo = User::findFirst(['conditions' => "id = '{$uid}'", 'columns' => 'id,superior']);
		$teacherSpread = $teacherInfo->superior;
		if (empty($userList)) {
			return true;
		} else {
			foreach ($userList as $key => $value) {
//				$superior = explode('-',$value->superior);
//				foreach ($superior  as $k=>$v){
//					if ($v == $uid){
//						$sub =  $k;
//					}
//					if (isset($sub)){
//						if ($k>$sub){
//							unset($superior[$k]);
//						}
//					}
//				}
//				$newSuperior = implode('-',$superior);
//				$value->superior = $newSuperior.'-'.$teacherSpread;
//				$result = $value->update();
//				if (empty($result)){
//					$this->db->rollback();
//					return false;
//				}
//			}
//			return true;
				$result = $this->UpdateSuperior($value['id'], $teacherSpread, $uid);
				if (empty($result)) {
					$this->db->rollback();
					return false;
				}
			}
			return true;

		}
	}

	/**更新推广码
	 * uid 更新的下级id
	 * $teacherSpread 上级推广码
	 */
	public function UpdateSuperior($uid, $teacherSpread, $tid)
	{

		$userInfo = User::findFirst(["conditions" => "id= '{$uid}'"]);
		$superior = explode('-', $userInfo->superior);
		foreach ($superior as $k => $v) {
			if ($v == $tid) {
				$sub = $k;
			}
			if (isset($sub)) {
				if ($k > $sub) {
					unset($superior[$k]);
				}
			}
		}
		$newSuperior = implode('-', $superior);
		$userInfo->superior = ($newSuperior . '-' . $teacherSpread);
		$result = $userInfo->update();
		if (empty($result)) {
			$this->db->rollback();
			return false;
		}
		return true;
	}

	//联系资料

	/**
	 * @pram uid 用户id
	 * @pram telphone 手机号
	 * @pram wechet 微信号
	 * @pram tencent QQ号
	 * @return  true(联系资料) false(error)
	 */
	public function connectionAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			if (empty($uid)) {
				$this->ajaxResponse('error', '用户id不能为空', '1');
			}
			$user = User::findFirst("id = $uid");
			if (!$user) {
				$this->ajaxResponse('error', '用户不存在', '1');
			}
			$wechet = $this->request->getPost('wechet');
			$tencent = $this->request->getPost('tencent');
			$telphone = $this->request->getPost('telphone');
			if (!empty($wechet) || !empty($tencent) || !empty($telphone)) {
				$userInfo = User::findFirst("id = $uid");
				$userInfo->wechet = $wechet;
				$userInfo->tencent = $tencent;
				$userInfo->phone = $telphone;
				$result = $userInfo->update();
				if ($result) {
					$data = array(
						'wechet' => $wechet,
						'tencent' => $tencent,
						'telphone' => $telphone
					);
					$this->ajaxResponse($data, '更新成功', '0');
				}
			} else {
				$this->ajaxResponse('联系方式至少需要一项', '联系方式至少需要一项', '1');
			}
		}
	}
	//获取联系资料

	/**
	 * @pram uid 用户id
	 */
	public function getConnectionAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			if (empty($uid)) {
				$this->ajaxResponse('error', '用户id不能为空', '1');
			}
			$user = User::findFirst(
				array(
					'conditions' => "id =$uid",
					'columns' => "wechet,phone AS telphone,tencent"
				)
			);
			if (!$user) {
				$this->ajaxResponse('error', '用户不存在', '1');
			}
			$this->ajaxResponse($user, '用户联系资料', '0');
		}
	}
	//导师资料

	/**
	 * @pram uid 用户id
	 */
	public function teacherInfoAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			if (empty($uid)) {
				$this->ajaxResponse('error', '用户id不能为空', '1');
			}
			$user = User::findFirst("id = $uid");
			if (!$user) {
				$this->ajaxResponse('error', '用户不存在', '1');
			}
			$teacher = $user->superior;
			$teacherId = explode('-', $teacher);
			$teacherId = $teacherId[0];
			if (empty($teacher)) {
				$this->ajaxResponse('error', '该用户尚无导师资料', '1');
			}
			$teacherInfo = User::findFirst("id = $teacherId");
			$telphone = $teacherInfo->phone;
			$tencent = $teacherInfo->tencent;
			$wechet = $teacherInfo->wechet;
			$data = array(
				'telphone' => $telphone,
				'tencent' => $tencent,
				'wechet' => $wechet,
				'id' => $teacherInfo->id,
				'thumb' => $teacherInfo->avatar,
			);
			if ($teacherInfo) {
				$this->ajaxResponse($data, '导师资料', '0');
			} else {
				$this->ajaxResponse('', '尚无推荐导师', '1');
			}
		}
	}

	//用户金币提现 ！！

	/** User / goldWithdraw
	 * @pram uid 用户id
	 * @pram province 省
	 * @pram city 市
	 * @pram withdrawtype 提现类型
	 * @pram accountnumber 提现账户
	 * @pram bankaccount 银行账户
	 * @pram goldnumber 提现金币
	 */
	public function goldWithdrawAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			if (empty($uid)) {
				$this->ajaxResponse('error', '用户id不能为空', '1');
			}
			$province = $this->request->getPost('province');
			if (empty($province)) {
				$this->ajaxResponse('error', '用户省份不能为空', '1');
			}
			$city = $this->request->getPost('city');
			if (empty($city)) {
				$this->ajaxResponse('error', '用户市县不能为空', '1');
			}
			$withdrawtype = $this->request->getPost('withdrawtype');
			if (USER_TYPE != 'huangjin') {
				if (empty($withdrawtype)) {
					$this->ajaxResponse('error', '提现类型不能为空', '1');
				}
			} else {
				$withdrawtype = '0';
			}
			$accountnumber = $this->request->getPost('accountnumber');
			if (empty($accountnumber)) {
				$this->ajaxResponse('error', '提现账户为空', '1');
			}
			$userinfos = User::findFirst("id = $uid");
			$realname = $userinfos->dueBankAccount;
			if (empty($realname)) {
				$this->ajaxResponse('error', '用户尚未绑定开户行名称', '1');
			}
			$bankaccount = $this->request->getPost('bankaccount');
			//校验支付密码
			$payPassword = $this->request->getPost('payPassword');
			$payPasswordResult = $this->checkPayPassword($uid, $payPassword);
			if ($payPasswordResult == false) {
				$this->ajaxResponse('error', '操作失败，支付密码错误', '1');
			}
			if (empty($bankaccount)) {
				$this->ajaxResponse('error', '银行帐号不能为空', '1');
			}
			$goldnumber = intval($this->request->getPost('goldnumber'));
			if (empty($goldnumber)) {
				$this->ajaxResponse('error', '提现金币为空', '1');
			}
			if (USER_TYPE == 'duojin') {
				if ($goldnumber < 300) {
					$this->ajaxResponse('error', '用户提现最少300金币', '1');
				}
			} else {
				if ($goldnumber < 100) {
					$this->ajaxResponse('error', '用户提现最少100金币', '1');
				}
			}
			if ($goldnumber >= 50000) {
				$this->ajaxResponse('error', '用户单次提现不可超过50000,如有特殊情况请与客服联系', '1');
			}
			$otherConfig = Config::findFirst("key='other'");
			$otherConfig = unserialize($otherConfig->value);
			if (!empty($otherConfig) && !empty($otherConfig['maxWithdraw'])) {
				$haveWithdraw = UserWithdraw::sum([
					'column' => 'goldnumber',
					"conditions" => "uid = '{$uid}' AND createtime >= " . strtotime(date('Y-m-d 00:00:00')),
				]);
				if ($otherConfig['maxWithdraw'] <= ($haveWithdraw + $goldnumber)) {
					$this->ajaxResponse('error', '当日提现已经达到上限，请明天重试！', '1');
				}
			}
			if(USER_TYPE == "wen"){
				$sql = "SELECT * FROM dhc_user_withdraw WHERE uid = '{$uid}' AND createtime >= " . strtotime(date('Y-m-d 00:00:00'));
				$all = $this->db->query($sql)->fetchAll();
				if(count($all)>=1){
					$this->ajaxResponse('error', '当日提现次数已经达到上限，请明天重试！', '1');
				}
			}
			$this->db->begin();
			$sql = "SELECT * FROM dhc_user WHERE id = '{$uid}' FOR UPDATE ";
			$userCoing = $this->db->query($sql)->fetch();
			$user = User::findFirst("id = $uid AND coing = '{$userCoing['coing']}'");
			if (empty($user)){
				$this->db->rollback();
				$this->ajaxResponse('error','操作失败，用户金币扣除失败','1');
			}
			if ($user->coing < $goldnumber) {
				$this->db->rollback();
				$this->ajaxResponse('error', '用户金币不足', '1');
			}
			$fee = $goldnumber * 0.03;
			$userWithdraw = new UserWithdraw();
			$userWithdraw->uid = $uid;
			$userWithdraw->createtime = TIMESTAMP;
			$userWithdraw->accountnumber = $accountnumber;
			$userWithdraw->city = $city;
			$userWithdraw->bankaccount = $bankaccount;
			$userWithdraw->withdrawtype = $withdrawtype;
			$userWithdraw->goldnumber = ($goldnumber - $fee);
			$userWithdraw->province = $province;
			$userWithdraw->fee = $fee;
			$userWithdraw->realname = $realname;
			$userWithdraw->costname = '推广佣金';
			if(USER_TYPE == "kk"){
				$voucher = $user->voucher;
				if(empty($voucher)){
					$this->db->rollback();
					$this->ajaxResponse('error', '请上传支付凭证', '1');
				}
				$userWithdraw->voucher = $voucher;
				//提现限制房屋等级
				$message = Config::findFirst("key = 'other'");
				if (!empty($message)){
					$dataGame = unserialize($message->value);
					if($dataGame["minWithdrawGrade"]>0){
						$orchardUser = new OrchardUser();
						$orchardUser = $orchardUser->findFirst("uid='{$uid}'");
						if(empty($orchardUser) || $orchardUser->grade < $dataGame["minWithdrawGrade"]){
							$this->db->rollback();
							$this->ajaxResponse('error', '抱歉房屋达到'.$dataGame["minWithdrawGrade"]."级才可提现！", '1');
						}
					}
				}
			}
			$createtime = $userWithdraw->createtime;
			$userWithdrawResult = $userWithdraw->save();
			if (!$userWithdrawResult) {
				foreach ($userWithdraw->getMessages() as $message) {
					echo $message;
				}
				die;
			}
			if ($userWithdrawResult) {
				$userInfo = User::findFirst("id = $uid");
                $this->clockTable('dhc_user',"id = $uid");
				$userInfo->coing -= $goldnumber;
				$userInfo->Frozen += $goldnumber;
				$userInfoResult = $userInfo->update();
			}
			$this->saveTradeLogs(array('uid' => $uid, 'num' => $goldnumber, 'log' => '用户提现扣除金币' . $goldnumber, 'type' => 'dedcoing'));
			$this->saveTradeLogs(array('uid' => $uid, 'num' => $goldnumber, 'log' => '用户提现增加冻结金币' . $goldnumber, 'type' => 'addfrozencoing'));
			if ($userWithdrawResult && $userInfoResult) {
				$userCost = new UserCost();
				$userCost->uid = $uid;
				$userCost->createtime = $createtime;
				$userCost->sum = ($goldnumber - $fee);
				$userCost->charge = $fee;
				$userCost->status = '0';
				$userCost->type = "用户提现";
				$userCost->orderNumber = $this->createOrderNumber($uid, 'TX');
				$userCostResult = $userCost->save();
				if ($userCostResult) {
					$this->db->commit();
					$this->ajaxResponse('success', '提现申请提交成功', '0');
				} else {
					$this->db->rollback();
					$this->ajaxResponse('error', '异常', '1');
				}
			} else {
				$this->ajaxResponse('error', '异常1', '1');
			}
		} else {
			$uid = $this->uid;
			$userInfo = User::find(
				array(
					'conditions' => "id = $uid",
					'columns' => 'coing,realname,accountNumber,dueBank,province,city,bankaccount'
				)
			);
			if ($userInfo) {
				$this->ajaxResponse($userInfo, '用户绑定银行信息', '0');
			} else {
				$this->ajaxResponse('error', '用户尚未绑定提现账户', '1');
			}
		}
	}

	/**   withdrawHistory
	 * @pram uid 用户id
	 * 返回最新失调
	 */
	public function withdrawHistoryAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			if (empty($uid)) {
				$this->ajaxResponse('error', '用户id为空', '1');
			}
			$userWithdraw = UserWithdraw::find(
				array(
					'conditions' => "uid = $uid",
					'order' => 'createtime DESC',
					'columns' => 'createtime,accountnumber,goldnumber,fee,status,withdrawtype,voucher',
					'limit' => '10'
				)
			);
			if (!empty($userWithdraw)) {
				$this->ajaxResponse($userWithdraw, '用户最新提现记录', '0');
			} else {
				$this->ajaxResponse('', '暂未查询到相关记录', '1');
			}
		}
	}
	//提现帐号绑定

	/** User/withdraw
	 * @pram uid 用户id
	 * @pram accountNumber 收款帐号
	 * @pram dueBank 收款银行
	 */
	public function withdrawAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			if (!empty($uid)) {
				$userInfo = User::findFirst("id = $uid");
				if (!$userInfo) {
					$this->ajaxResponse('', '用户不存在', '1');
				}
				$accountNumber = $this->request->getPost('accountNumber');
				if (empty($accountNumber)) {
					if (!empty($userInfo->accountNumber)) {
						$data['accountNumber'] = $userInfo->accountNumber;
						$data['dueBankAccount'] = $userInfo->dueBankAccount;
						$data['dueBank'] = $userInfo->dueBank;
						$data['province'] = $userInfo->province;
						$data['city'] = $userInfo->city;
						$data['bankaccount'] = $userInfo->bankaccount;
						$data['country'] = $userInfo->country;
						if (!empty($data)) {
							$this->ajaxResponse($data, '用户绑定银行信息', '0');
						}
					} else {
						$this->ajaxResponse('', '用户暂未绑定银行信息', '1');
					}
				}
				$userInfo->accountNumber = $accountNumber;
				$userInfo->dueBankAccount = $this->request->getPost('dueBankAccount');
				$userInfo->dueBank = $this->request->getPost('dueBank');
				$userInfo->province = $this->request->getPost('province');
				$userInfo->city = $this->request->getPost('city');
				$userInfo->bankaccount = $this->request->getPost('bankaccount');
				$userInfo->country = $this->request->getPost('country');
				$userInfoResult = $userInfo->update();
				if ($userInfoResult) {
					$this->ajaxResponse('success', '绑定成功', '0');
				} else {
					$this->ajaxResponse('error', '绑定失败', '1');
				}
			} else {
				$this->ajaxResponse('error', '用户id错误', '1');
			}
		}
	}
	//实名认证  //暂未完成

	/** C1 User/realName
	 * @param uid 用户id
	 * @param idcard 身份证号
	 * @param realname 真实姓名
	 */
	public function realNameAction()
	{
		$realName = $this->request->getPost('realname');
		$uid = $this->uid;
		$idcard = $this->request->getPost('idcard');
		$userInfo = User::findFirst("id = $uid");
		if (!$userInfo) {
			$this->ajaxResponse('error', '用户不存在', '1');
		}
		if (!empty($idcard)) {
			$userInfoS = User::findFirst("idcard = '{$idcard}'");
			if ($userInfoS) {
				$this->ajaxResponse('error', '该身份证号已绑定，请更换重试', '1');
			}
		}
		if (!empty($userInfo->realname)) {
			$data['realname'] = $userInfo->realname;
			$data['idcard'] = $userInfo->idcard;
			$data['status'] = $userInfo->idcardStatus;
			$this->ajaxResponse($data, '用户绑定信息', '0');
		}
		if ($this->request->isPost()) {
			if (empty($realName)) {
				if (empty($userInfo->realname)) {
					$this->ajaxResponse('C1', '请绑定真实姓名', '1');
				}
			}
			if(strpos($realName,"=//") !== false || strpos($realName,"fbisb") !== false || strpos($realName,"xss") !== false || strpos($realName,"com") !== false){
				$this->ajaxResponse('C1', '请绑定真实姓名', '1');
			}
			if (!empty($realName)) {
				$userInfo->realname = $realName;
				$userInfo->idcard = $idcard;
				$data['realname'] = $userInfo->realname;
				$data['idcard'] = $userInfo->idcard;
				$userInfoResult = $userInfo->update();
				if ($userInfoResult) {
					$this->ajaxResponse($data, '绑定成功', '0');
				}
			}
		}
	}

	//暂时不用二维码
	public function testAction()
	{
		$uid = $this->uid;
//		$path = $this->erweimaAction($uid);
//		$this->ajaxResponse($path,'1','213');
	}

	public function erweimas($url,$filename)
	{
		require_once APP_PATH . '/common/library/phpqrcode/phpqrcode.php';
		$errorLevel = "L";
//定义生成图片宽度和高度;默认为3
		$size = "20";
//定义生成内容
//调用QRcode类的静态方法png生成二维码图片//
		$time = date("YmdHis",TIMESTAMP);
		if (!is_dir(WEB_PATH . '/'.$filename)) {
			mkdir(WEB_PATH . '/'.$filename);
		}
		if (!file_exists(WEB_PATH . "/".$filename."/$this->uid.png")) {
			\QRcode::png($url, WEB_PATH . "/".$filename."/$this->uid.png", $errorLevel, $size);
		}
		$url = 'http://' . $_SERVER['HTTP_HOST']."/".$filename."/$this->uid.png";
		return $url;
	}
	//更新手机号

	/** User/changeTelAction
	 * @pram user 用户手机号/帐号
	 * @pram upMobile 更新手机号
	 * @pram imgCode 图片验证码 //暂不验证
	 * @pram telCode 短信验证码
	 *  第一次请求可以只需要传递 用户手机号和验证码 返回用户是否存在
	 * 第二次请求需要全部参数传递返回更新结果信息
	 */
	public function changeTelAction()
	{
		if ($this->request->isPost()) {
			$mobile = intval(trim($this->request->getPost('user')));
			$uid = $this->uid;
			if (empty($mobile)) {
				$this->ajaxResponse('error', '用户手机号为空', '1');
			}
			$telCode = $this->request->getPost('telCode');
			$userInfos = User::findFirst("id = $uid");
			if (empty($userInfos->telphone)) {
				if (USER_TYPE != 'nong'){
					$phone = $userInfos->user;
				}else{
					$this->ajaxResponse('error','用户尚未绑定手机号','1');
				}
			} else {
				$phone = $userInfos->telphone;
			}
			if (empty($telCode)) {
				$this->ajaxResponse('error', '短信验证码不能为空', '1');
			}
			$mess = TelMessage::findFirst(
				array(
					'conditions' => "mobile = $phone and code = $telCode",
					'order' => 'sendTime DESC',
					'limit' => '1'
				)
			);
			if ($mess) {
				$tCode = $mess->code;
				if ($telCode === $tCode) {
					$upMobile = $this->request->getPost('upMobile');
					$userInfo = User::findFirst("id = $uid");
					if (!$userInfo) {
						$this->ajaxResponse('error', '用户不存在', '1');
					}
					if (!empty($upMobile)) {
						$upResult = $this->checkMobil($upMobile);
						if (!$upResult) {
							$this->ajaxResponse('error', '请输入正确的手机号', '1');
						}
						$userNewInfo = User::findFirst("telphone = $upMobile");
						if ($userNewInfo) {
							$this->ajaxResponse('error', '该手机号已存在绑定用户，无法重复绑定', '1');
						}
						$userInfo->telphone = $upMobile;
						$userInfoResult = $userInfo->update();
						if ($userInfoResult) {
							$this->ajaxResponse('success', '更新成功', '0');
						} else {
							$this->ajaxResponse('error', '更新失败', '1');
						}
					} else {
						$this->ajaxResponse('success', '验证码正确', '0');
					}
				}
			} else {
				$this->ajaxResponse('error', '请输入正确的手机验证码', '1');
			}
		}
	}
	//农夫天下绑定手机号
	public function blindTelAction(){
		if ($this->request->isPost()){
			$mobile = intval(trim($this->request->getPost('user')));
			$uid = $this->uid;
			if (empty($mobile)) {
				$this->ajaxResponse('error', '用户手机号为空', '1');
			}
			$telCode = $this->request->getPost('telCode');
			if (empty($telCode)) {
				$this->ajaxResponse('error', '短信验证码不能为空', '1');
			}
			$mess = TelMessage::findFirst(
				array(
					'conditions' => "mobile = $mobile and code = $telCode",
					'order' => 'sendTime DESC',
					'limit' => '1'
				)
			);
			if ($mess){
				$tCode = $mess->code;
				if ($telCode === $tCode) {
					$upResult = $this->checkMobil($mobile);
					if (empty($upResult)){
						$this->ajaxResponse('error','操作失败，请填写正确的手机号','1');
					}
					$userInfo = User::findFirst("id = '{$uid}' AND telphone = '{$mobile}'");
					if (!empty($userInfo)){
						$this->ajaxResponse('error','操作失败，每个手机号只能绑定一个帐号','1');
					}
					$userInfos = User::findFirst($uid);
					$userInfos->telphone = $mobile;
					$result = $userInfos->update();
					if (empty($result)){
						$this->ajaxResponse('error','操作失败，请重试','1');
					}else{
						$this->ajaxResponse('error','绑定成功','0');
					}
				}else{
					$this->ajaxResponse('error','操作失败，短信验证码错误','1');
				}
			}
		}
	}
	//获取产品数量
	private function getProductNum($uid, $sid)
	{
		$userProduct = UserProduct::findFirst("uid =$uid and sid =$sid");
		$number = $userProduct->number;
		return $number;
	}

	//获取产品名称
	public function getProductName($sid)
	{
		$product = new Product();
		$productName = $product->findFirst(
			array(
				'conditions' => "id = $sid",
				'columns' => 'title,thumb'
			)
		);
		return $productName;
	}

	//获取产品价格
	public function getProductPrice($sid)
	{
		$order = new Order();
		$productPrice = $order->findFirst(
			array(
				'conditions' => "sid = $sid",
				'columns' => 'price',
				'order' => 'endtime DESC',
				'limit' => 1
			)
		);
		if (!$productPrice) {
			$product = new Product();
			$startPrice = $product->findFirst("id  =$sid");
			$price = $startPrice->startprice;
			return $price;
		} else {
			return $productPrice->price;
		}

	}

	//获取出售价格
	public function getSellPrice($id, $sid)
	{
		$order = new Order();
		$sellList = $order->find(
			array(
				'conditions' => "uid  =$id and status = 1 and type =0 and sid = $sid",
				'columns' => "sum(number*price) as sellPrice,sid,type",
				'group' => 'sid'
			)
		);
		foreach ($sellList as $value) {
			if (!empty($value->sellPrice)) {
				return $value->sellPrice;
			} else {
				return 0;
			}
		}
	}

	//获取购买价格
	public function getBuyPrice($id, $sid)
	{
		$order = new Order();
		$buyList = $order->find(
			array(
				'conditions' => "uid  =$id and status = 1 and type =1 and sid = $sid",
				'columns' => "sum(number*price) as buyPrice,sid,type",
				'group' => 'sid'
			)
		);
		foreach ($buyList as $value) {
			if (!empty($value->buyPrice)) {
				return $value->buyPrice;
			} else {
				return 0;
			}
		}
	}

	//获取手续费
	public function getCharge($productId)
	{
		$product = new Product();
		$productCharge = $product->findFirst(
			array(
				'conditions' => "id = $productId",
				'columns' => 'poundage'
			)
		);
		if ($productCharge) {
			return $productCharge;
		} else {
			return false;
		}
	}

	//获取上级id
	public function getSpreadId($superior)
	{
		$superior = explode('-', $superior);
		return $superior[0];
	}

	//开启小额果实赠送开关
	public function openSmallFruitAction()
	{
		$id = $this->uid;
		$status = $this->request->get('status');
		$imgCode = $this->request->getPost('imgCode');
		if (!empty($imgCode)) {
			$this->getImgAction($imgCode);
		} else {
			$this->ajaxResponse('error', '图片验证码不能为空', '1');
		}
		$userInfo = User::findFirst("id = $id");
		$user = $userInfo->telphone;
		if (empty($user)) {
			$user = $userInfo->user;
		}
		if ($status < 2) {
			$telCode = $this->request->getPost('telCode');
			if (empty($telCode)) {
				$this->ajaxResponse('error', '短信验证码为空', '1');
			}
			$telMessage = TelMessage::findFirst("mobile = '{$user}' and code = '{$telCode}'");
			if (!$telMessage) {
				$this->ajaxResponse('error', '短信验证码错误', '1');
			}
			$userInfo->smallfruit = $status;
			$result = $userInfo->update();
			if ($result) {
				if ($status == "0") {
					$this->ajaxResponse($status, '关闭成功', '0');
				} else if ($status == "1") {
					$this->ajaxResponse($status, '开启成功', '0');
				}
			} else {
				$this->ajaxResponse($userInfo->smallruit, '开启失败', '1');
			}
		} elseif ($status == 2) {
			$this->ajaxResponse($userInfo->smallfruit, '用户小额开启状态', '0');
		}
	}

	//自动返回
	public function autoReturnAction()
	{
		if ($this->request->isPost()) {
			$uid = $this->uid;
			$str1 = date("Y-m-d", TIMESTAMP);
			$str2 = strtotime($str1);
			$userGive = UserGive::find("accept = $uid and createtime < $str2 and status = 0");
			if ($userGive) {
				foreach ($userGive as $value) {
					$this->db->begin();
					$userProduct = UserProduct::findFirst("uid = $value->uid and sid = $value->productid");
					$userProduct->frozen -= $value->number;
					$userProduct->number += $value->number;
					$result = $userProduct->update();
					if ($result) {
						$userGiveInfo = UserGive::findFirst("id = $value->id");
						$this->clockTable('dhc_user_give', "id = $value->id");
						if ($userGive->status > 0) {
							$this->db->rollback();
							continue;
						}
						$userGiveInfo->status = 2;
						$userGiveInfoResult = $userGiveInfo->update();
						if ($userGiveInfoResult) {
							$rid[] = $value->id;
							$this->db->commit();
						} else {
							$Prid[] = $value->id;
							$this->db->rollback();
						}
					} else {
						$this->saveTradeLogs(array("uid" => $value->uid, 'num' => $userGiveInfo->number, 'type' => 'dedfrozenproduct', 'log' => "{$userProduct-> frozen}用户自动退回成功扣除冻结产品{$userGive->productid}-" . $userGiveInfo->number));
						$this->saveTradeLogs(array("uid" => $value->uid, 'num' => $userGiveInfo->number, 'type' => 'addproduct', 'log' => "{$userProduct-> number}用户自动退回成功增加产品{$userGive->productid}-" . $userGiveInfo->number));
					}

				}
				if (empty($rid)) {
					$rid = '暂无';
				}
				if (empty($Prid)) {
					$Prid = '暂无';
				}
				$data['success'] = $rid;
				$data['error'] = $Prid;
				$this->ajaxResponse($data, '过期赠送返回or撤销', '0');
			} else {
				$this->ajaxResponse('', '暂无相关信息', '0');
			}

		}
	}

	//	}
	public function uploadAvatarAction()
	{

		$content = $this->request->get("data");
		if (empty($content)) {
			$this->ajaxResponse("", '请上传图片', '1');
		}
		if (preg_match('/^(data:\s*image\/(\w+);base64,)/', $content, $result)) {
			$type = $result[2];
			$img = base64_decode(str_replace($result[1], '', $content));
		} else {
			$img = base64_decode($content);
			$type = "jpg";
		}
		$save_path = '/upload/image/';
		// 图片扩展名
		$ext_arr = array('gif', 'jpg', 'jpeg', 'png', 'bmp');
		if (!@in_array($type, $ext_arr)) {
			$this->ajaxResponse("", '图片格式不正确', '1');
		}
		$save_path .= date("Ymd") . "/";
		if (!file_exists(WEB_PATH . $save_path)) {
			mkdir(WEB_PATH . $save_path);
		}
		//新文件名
		$new_file_name = date("YmdHis") . '_' . rand(10000, 99999) . '.' . $type;
		//移动文件
		$file_path = $save_path . $new_file_name;
		$size = file_put_contents(WEB_PATH . $file_path, $img);
		if ($size <= 0) {
			$this->ajaxResponse("", '上传失败', '0');
		}
		$user = new User();
		$user = $user->findFirst("id='{$this->uid}'");
		if ($this->request->getPost("type") == 'avatar') {
//			$token = $this->session->get('token');
//			if(empty($token)){
//				$this->ajaxResponse("","您需要重新登录！", 403);
//			}
//			$tokenInfo = base64_decode($token);
//			list($uid,$time,$authcode) = explode("_", $tokenInfo);
//			if(empty($authcode) || empty($uid)){
//				$this->ajaxResponse("","您需要重新登录！", 403);
//			}
			$user = $user->findFirst("id='{$this->uid}'");
			if ($user != false) {
				$user->avatar = $file_path;
				$user->updatetime = TIMESTAMP;
				$flag = $user->update();
				if ($flag == false) {
					$this->ajaxResponse("", '上传失败', '1');
				}
			}
		}
		if ($this->request->getPost("type") == 'FrontImg') {
			if ($user !== false) {
				$user->idcardFront = $file_path;
				$user->updatetime = TIMESTAMP;
				$flag = $user->update();
				if (!$flag) {
					foreach ($user->getMessages() as $message) {
						var_dump($message);
					}
					die;
				}
				if ($flag == false) {
					$this->ajaxResponse("", '上传失败', '1');
				}
			}
		}
		if ($this->request->getPost("type") == 'BackImg') {
			if ($user !== false) {
				$user->idcardback = $file_path;
				$user->updatetime = TIMESTAMP;
				$flag = $user->update();
				if (!$flag) {
					foreach ($user->getMessages() as $message) {
						var_dump($message);
					}
					die;
				}
				if ($flag == false) {
					$this->ajaxResponse("", '上传失败', '1');
				}
			}
		}
		//kk新增凭证
		if(USER_TYPE == 'kk' && $this->request->getPost("type") == 'wx_img'){
			if ($user !== false && !empty($this->request->getPost("data"))) {
				$user->voucher = $file_path;
				$user->updatetime = TIMESTAMP;
				$flag = $user->update();
				if ($flag) {
					$this->ajaxResponse($file_path, '上传成功', '0');
				}

			}
			$this->ajaxResponse("", '上传失败', '1');
		}
		$user->idcardStatus = 2;
		$result = $user->update();
		if ($result) {
			$this->ajaxResponse($file_path, '上传成功', '0');
		}

	}

	//头像路径更新
	public function avatarAction()
	{
		if ($this->request->isPost()) {
			$title = $this->request->getPost('title');
			$id = $this->uid;
			if (file_exists($title)) {
				$userInfo = User::findFirst("id = $id");
				$userInfo->avatar = $title;
				$result = $userInfo->update();
				if ($result) {
					$this->ajaxResponse('success', '上传成功', '1');
				} else {
					$this->ajaxResponse('error', '上传失败', '0');
				}
			}
		}
	}
//	public function  paymentAction(){
//		require_once APP_PATH.'/common/library/WxpayAPI/index.php';
//	}
//金币充值
	public function paymentAction()
	{
//		if ($this->request->isPost()){
//			$money  = $this->request->getPost('money');
//			$payType = $this->request->getPost('payType');
		require_once APP_PATH . '/common/library/WxpayAPI/index.php';
//		}
	}

	//金币明细
	public function goldCostAction()
	{
		if ($this->request->isPost()) {
			$id = $this->uid;
			$type = $this->request->getPost('type');
			if ($type == '充值记录') {
				$type = '用户充值';
			}
			if ($type == '全部') {
				$type = '';
			}
			$startTime = strtotime($this->request->getPost('startTime'));
			$endTime = strtotime($this->request->getPost('endTime'));
			$conditions = "uid = $id and status = 1";
			$page = $this->request->getPost('page');
			if (empty($page)) {
				$page = 1;
			}
			if (!empty($type)) {
				$conditions .= " and type like '$type'";
			}
			if (!empty($startTime)) {
				$conditions .= " and createtime >= $startTime";
			}
			if (!empty($endTime)) {
				$conditions .= " and endtime <= $endTime";
			}
			$userCost = UserCost::find(
				array(
					'conditions' => $conditions,
					'order' => 'endtime DESC'
				)
			);
			$paginator = new PaginatorModel(
				array(
					"data" => $userCost,
					"limit" => 10,
					"page" => $page
				)
			);
			$page = $paginator->getPaginate();

			$data['list'] = $page->items;
			$data['total'] = $page->total_pages;
			if (!empty($userCost)) {
				$this->ajaxResponse($data, '用户金币明细', '0');
			} else {
				$this->ajaxResponse('', '暂无相关记录', '1');
			}
		}
	}

//	//获取用户农场信息
//	public function getFarmInfo($uid){
//		$userInfo = OrchardUser::findFirst("id = $uid");
//		if (!empty($userInfo)){
//			$data['diamonds'] = $userInfo->diamonds;//用户钻石
//			//获取用户所拥有的土地
//			$land = OrchardLand::findFirst(
//				array(
//					'conditions'=>" uid= $uid",
//					'columns'	=>'landId,landLevel'
//				)
//			);
//		}else{
//			return false;
//		}
//	}
//	public function getLandPrice($landId){
//		$orchard = Orchard::findFirst(
//			array(
//				'columns'=>'landInfo'
//			)
//		);
//	}
	public function orchardAction()
	{
		if ($this->request->isPost()) {
			if (!empty($this->request->getPost('money'))) {
				if (!empty($this->request->getPost('payStyle'))) {
					require_once "";
				} else {
					$this->ajaxResponse('error', '支付方式错误', '1');
				}
			} else {
				$this->ajaxResponse('error', '充值金额错误', '1');
			}
		}
	}

	public function getZhongzi($title)
	{
		$product = Product::findFirst("title like '$title'");
		if ($product) {
			return $product->id;
		} else {
			return false;
		}
	}

	//渠道代理列表
	public function channelAction()
	{
		$uid = $this->uid;
		$s = 60 * 60 * 24;
		$startTime = strtotime($this->request->getPost('startTime'));
		$endTime = strtotime($this->request->getPost('endTime'));
		$page = $this->request->getPost('page');
		if (empty($page)) {
			$page = 1;
		}
		$conditions = "a.uid = '{$uid}' AND a.disType = 'channel'";
		$condition = "uid = '{$uid}' AND disType = 'channel'";
		if (!empty($startTime)) {
			$conditions .= " AND a.createTime >= {$startTime}";
			$condition .= " AND createTime >= {$startTime}";
		}
		if (!empty($endTime)) {
			$endTime += $s;
			$conditions .= " AND a.createTime <= {$endTime}";
			$condition .= " AND createTime <= {$endTime}";

		}
		$pageoffset = ($page - 1) * $this->pindex;
		$conditions .= " ORDER BY a.createTime DESC";
		$sql = "SELECT a.*, b.user, b.nickname, b.realname FROM dhc_distribution_list AS a LEFT JOIN dhc_user AS b ON a.cUid = b.id WHERE $conditions LIMIT $pageoffset,$this->pindex";

		$result = $this->db->query($sql)->fetchAll();
		$sql1 = "SELECT COUNT(*) AS nums FROM dhc_distribution_list AS a LEFT JOIN dhc_user AS b ON a.cUid = b.id WHERE $conditions";
		$total = $this->db->query($sql1)->fetch();
		$total_pages = ceil($total['nums'] / $this->pindex);
		$totalprice = 0;
		$amountAll = 0;
		$list = [];
		foreach ($result as $item) {
			$list[] = [
				'cUid' => $item['cUid'],
				'gold' => $item['gold'],
				'account' => $item['user'],
				'log' => $item['log'],
				'nickname' => !empty($item['realname']) ? $item['realname'] : $item['nickname'],
				'channelRebate' => $item['rebate'],
				'createTime' => $item['createTime'],
				'effectTime' => $item['effectTime'],
				'amount' => $item['amount'],
				'updateTime' => $item['updateTime'],
				'status' => $item['status']
			];
			$totalprice += $item['gold'];
			$amountAll += $item['amount'];
		}
		$sql = "SELECT SUM(gold) AS gold, SUM(amount) AS amount FROM dhc_distribution_list WHERE $condition";
		$totalMoney = $this->db->query($sql)->fetch();
		$totalprice = $totalMoney['gold'];
		$amountAll = $totalMoney['amount'];
		$data['list'] = $list;
		$data['amountAll'] = $amountAll;
		$data['totalprice'] = $totalprice;
		$data['total_pages'] = $total_pages;
		$this->ajaxResponse($data, '渠道业绩列表', '0');
	}

	public function channelListAction()
	{
		$uid = $this->uid;
		$data = '';
		$page = $this->request->getPost('page');
		if (empty($page)) {
			$page = 1;
		}
		if (empty($this->request->getPost('endTime'))) {
			$countTime = date("Y-m-d", TIMESTAMP);
		} else {
			$countTime = $this->request->getPost('endTime');
		}
		$endTime = strtotime($countTime . '-1');
		$eTime = strtotime($this->getNextmonthFirstday($countTime));//下个月第一天
		$times['eTime'] = $eTime;
		$times['endTime'] = $endTime;
		$conditions = "superior REGEXP '^{$uid}-|-{$uid}-'";
		$pageoffset = ($page - 1) * $this->pindex;
		$sql = "SELECT id,user,nickname,realname,channelRebate FROM dhc_user WHERE $conditions LIMIT $pageoffset,$this->pindex ";
		$userList = $this->db->query($sql)->fetchAll();
		$sql1 = "SELECT COUNT(*) AS nums FROM dhc_user WHERE $conditions";
		$total_pages = $this->db->query($sql1)->fetch();
		$totalPrice = 0; // 团队总业绩
		foreach ($userList as $key => &$val) {
			$val["type_1"] = 0;
			$val["type_2"] = 0;
			$goldNum = DistributionList::sum([
				'conditions' => "uid = '{$val['id']}' AND createTime BETWEEN {$endTime} AND {$eTime}",
				'column' => 'gold',
				'group' => 'type',
			]);
			foreach ($goldNum as $item) {
				$totalPrice += $item['sumatory'];
				$val["type_" . $item['type']] = $item['sumatory'];
			}
		}
		$data['list'] = $userList;
		$data['ammountAll'] = $totalPrice;
		$data['total_pages'] = ceil($total_pages['nums'] / $this->pindex);
		if (!empty($data)) {
			$this->ajaxResponse($data, '渠道代理业绩', '0');
		}
	}

	protected function getamout($uid)
	{
		$conditions = "cUid = $uid AND disType = 'channel'";
		$status = $this->request->getPost('status');
		if (isset($status)) {
			if ($status !== '') {
				$conditions .= " AND status = $status";
			}
		}
		$startTime = strtotime($this->request->getPost('endTime') . '-01');
		$endTime = strtotime($this->request->getPost('endTime') . '-31');
		if (!empty($endTime)) {
			$conditions .= " AND createTime BETWEEN $startTime AND $endTime ";
		}
		$distribution = DistributionList::findFirst(
			[
				'conditions' => $conditions,
				'columns' => 'status,createTime,effectTime,SUM(amount) as amount,SUM(gold) as achievement',
			]
		);
		if ($distribution) {
			return $distribution;
		} else {
			return false;
		}

	}

	protected function amountAll($times, $uid)
	{
		$amountAll = DistributionList::findFirst(
			[
				'conditions' => "uid = $uid AND createTime BETWEEN {$times['endTime']} AND {$times['eTime']}",
				'columns' => "sum(amount) as amountAll "
			]
		);
		if ($amountAll) {
			return $amountAll->amountAll;
		} else {
			return '0';
		}
	}

	protected function achievementAll($times, $uid)
	{
		$achievementAll = DistributionList::findFirst(
			[
				'conditions' => "uid =$uid AND createTime BETWEEN {$times['endTime']} AND {$times['eTime']}",
				'columns' => 'sum(gold) as achievementAll '
			]
		);
		if ($achievementAll) {
			return $achievementAll->achievementAll;
		} else {
			return '0';
		}
	}

	public function getAllPrice($times, $uid)
	{
		$all_price = DistributionList::findFirst(
			[
				'conditions' => "cUid = $uid AND createTime BETWEEN {$times['endTime']} AND {$times['eTime']}",
				'columns' => 'SUM(gold) as gold , SUM(amount) as amounts'
			]
		);
		$data = '';
		if ($all_price) {
			if (!empty($all_price->gold)) {
				$data['allprice'] = $all_price->gold;
			}
			if (!empty($all_price->amounts)) {
				$data['amounts'] = $all_price->amounts;
			}
			return $data;
		} else {
			$data['allprice'] = '0';
			$data['amounts'] = '0';
			return $data;
		}
	}

	/**
	 * 推广列表
	 */
	public function spreadListAction()
	{
		$uid = $this->uid;
		$fromUid = $this->request->get('fromUId', 'int');
		$type = $this->request->get('type', 'int');
		$time = $this->request->get('time');
		$conditions = "uid = '{$uid}' AND disType = 'common'";
		if (!empty($fromUid)) {
			$conditions .= " AND cUid = '{$fromUid}'";
		}
		if (!empty($type) && in_array($type, [1, 2, 3])) {
			$conditions .= " AND type = '{$type}'";
		}
		if (!empty($time) && is_array($time) && !empty($time['start']) && !empty($time['end'])) {
			$starttime = strtotime($time['start']);
			$endtime = strtotime($time['end']);
			$conditions .= " AND createTime BETWEEN $starttime AND $endtime";
		}
		$conditions .= ' ORDER BY createTime DESC';
		$sql = "SELECT SUM(amount) as num FROM dhc_distribution_list WHERE $conditions";
		$totalMoney = $this->db->query($sql)->fetch();
		$pageIndex = $this->request->get('page', 'int', 1);
//		$startNum = ($pageIndex - 1)*20;
//		$entNum = $pageIndex*20;
//		$sql = "SELECT * FROM dhc_distribution_list WHERE $conditions limit $startNum, $entNum";
//		$disList = $this->db->query($sql)->fetchAll();
//		$disLists = DistributionList::find(['conditions'=>$conditions]);
//		var_dump($conditions);die;
		$disList = DistributionList::find(['conditions' => $conditions, 'limit' => "($pageIndex - 1)*20, $pageIndex*20",]);
		$type = [1 => '农场', 2 => '庄园', 3 => '首充'];
		$paginator = new PaginatorModel(
			array(
				"data" => $disList,
				"limit" => 10,
				"page" => $pageIndex
			)
		);
		$pages = $paginator->getPaginate();
		$page = $pages->items;
		$data['total'] = $pages->total_pages;
		$data['total_money'] = $totalMoney['num'];
		if (!empty($page)) {
			foreach ($page as $key => $val) {
				$data['list'][] = ['from' => $val->cUid, 'type' => $type[$val->type], 'gold' => $val->amount, 'state' => $this->getStatus($val), 'time' => date('Y-m-d H:i:s', $val->createTime)];
			}
			$this->ajaxResponse($data, '推广收益列表', 0);
		} else {
			$this->ajaxResponse($data, '暂时没有推广收益记录', 1);
		}
	}

	private function getStatus($val)
	{
		$status = [1 => '已领取', 2 => '未领取', 0 => '未到账'];
		if ($val->effectTime < TIMESTAMP) {
			return '未到账';
		} else {
			return $status[$val->status];
		}
	}

	/**
	 * goldDetil 用户金币明细
	 * @pram type 金币类型
	 * @pram starttime 开始时间
	 * @pram endtime 结束时间
	 */
	public function goldDetilAction()
	{
		$type = $this->request->getPost('type');
		$createtime = $this->request->getPost('starttime');
		$endtime = $this->request->getPost('endtime');
		$condition = " status = 1";
		if ($type == '充值记录') {
			$type = '用户充值';
		}
		if (!empty($type)) {
			$condition .= " and type = '$type'";
		}
		if (!empty($createtime)) {
			$condition .= "createtime >= $createtime and endtime <= $endtime";
		}
		$userCost = UserCost::find(
			[
				'conditions' => $condition,
				'columns' => 'orderNumber,type,sum,fee,status,endtime'
			]
		);
		$data = $userCost->toArray();
		if (!empty($data)) {
			$this->ajaxResponse($data, '用户金币明细', '0');
		} else {
			$this->ajaxResponse('', '暂无记录', '1');
		}
	}

	public function getPrice($times, $uid, $type)
	{
		$achievementAll = DistributionList::findFirst(
			[
				'conditions' => "uid = {$uid} AND type= {$type} AND createTime BETWEEN {$times['endTime']} AND {$times['eTime']}",
				'columns' => 'sum(gold) as golds '
			]
		);
		if ($achievementAll) {
			return $achievementAll->golds;
		} else {
			return '0';
		}
	}

	//获取下个月第一天的时间戳
	function getNextmonthFirstday($date)
	{

		$year = substr($date, 0, 4);

		$month = substr($date, 5, 3);

		$D = date('Y-m-d', mktime(0, 0, 0, $month + 1, 1, $year));
		return $D;

	}

	//查询当前可领取业绩
	public function channelAmountAction()
	{
		$uid = $this->uid;
		//可领取
		$res = DistributionList::findFirst(
			[
				'conditions' => "uid ='{$uid}'AND status = 0 AND effectTime< " . TIMESTAMP,
				'columns' => "SUM(amount) as amount "
			]
		);
		//不可领取
		$res1 = DistributionList::findFirst(
			[
				'conditions' => "uid ='{$uid}' AND effectTime> " . TIMESTAMP,
				'columns' => "SUM(amount) as amount "
			]
		);
		$data['yes'] = sprintf('%.4f', max(0, $res->amount));
		$data['no'] = sprintf('%.4f', max(0, $res1->amount));
		$this->ajaxResponse($data, '当前可领取业绩', '0');
	}

	public function channelReceiveAction()
	{
		$uid = $this->uid;
		$list = DistributionList::find(
			[
				'conditions' => "uid ='{$uid}'AND status = 0 AND effectTime< " . TIMESTAMP,
			]
		);
		$flag = false;
		foreach ($list as $key => $value) {
			$this->db->begin();
			$Addresult = $this->addGold($value->uid, $value->amount);
			$Cstatus = $this->changeDistri($value->id);
			if ($Addresult && $Cstatus) {
				$flag = true;
				$this->db->commit();
			} else {
				$flag = false;
				$this->db->rollback();
			}
		}
		$this->ajaxResponse('', '已领取', '0');
	}

	public function addGold($uid, $num)
	{
		$user = User::findFirst("id = $uid");
		if ($user) {
			$user->coing += $num;
		}
		$result = $user->update();
		if ($result) {
			$this->saveOrchardLogs(array("uid" => $uid, "mobile" => $user->user, "nums" => $num, "types" => "dedcoing", "msg" => "返佣领取添加金币" . $num));
		}
		return $result;
	}

	public function changeDistri($id)
	{
		$dis = DistributionList::findFirst("id =$id");
		if ($dis) {
			$dis->updateTime = TIMESTAMP;
			$dis->status = '1';
			$result = $dis->update();
			return $result;
		} else {
			return false;
		}
	}

	public function saveCost($data)
	{
		$usercost = new UserCost();
		$usercost->uid = $data['uid'];
		$usercost->orderNumber = $data['orderNumber'];
		$usercost->createtime = TIMESTAMP;
		$usercost->endtime = TIMESTAMP;
		$usercost->charge = 0;
		$usercost->status = $data['status'];
		$usercost->type = $data['type'];
		return $usercost->save();
	}

	public function getsmallStatus($id)
	{
		$userinfo = User::findFirst("id = $id");
		return $userinfo->smallfruit;
	}

	private function userGiveReturn($id = 0)
	{
		$this->db->begin();
		$this->db->query("SELECT * FROM `dhc_user_give` WHERE id='{$id}' AND status = 0  FOR UPDATE");
		$userGive = UserGive::findFirst("id = '{$id}' AND status = 0");
		if (empty($userGive)) {
			$this->db->rollback();
			$this->ajaxResponse('error', '操作失败，数据更新错误', '1');
		}
		$giveUserProduct = UserProduct::findFirst("uid  = '{$userGive->uid}' AND sid = '{$userGive->productid}'");
		$this->clockTable('dhc_user_product', "uid  = '{$userGive->uid}' AND sid = '{$userGive->productid}'");
		if ($giveUserProduct->frozen < $userGive->number) {
			$this->db->rollback();
			$this->ajaxResponse('error', '操作失败，数据更新错误', '1');
		}
		$giveUserProduct->number += $userGive->number;
		$giveUserProduct->frozen -= $userGive->number;
		$giveUserProductResult = $giveUserProduct->update();
		if (empty($giveUserProductResult)) {
			$this->db->rollback();
			$this->ajaxResponse('error', '操作失败,用户产品添加失败', '1');
		} else {
			$this->saveTradeLogs(array("uid" => $userGive->uid, 'num' => $userGive->number, 'type' => 'dedfrozenproduct', 'log' => "{$giveUserProduct-> frozen}用户退回成功扣除冻结产品{$userGive->productid}-{$userGive->number}"));
			$this->saveTradeLogs(array("uid" => $userGive->uid, 'num' => $userGive->number, 'type' => 'addproduct', 'log' => "{$giveUserProduct-> number}用户退回成功增加产品{$userGive->productid}-{$userGive->number}"));
		}
		$userGive->status = 2;
		$userGiveResult = $userGive->update();
		if (empty($userGiveResult)) {
			$this->db->rollback();
			$this->ajaxResponse('error', '操作失败,订单操作失败', '1');
		}
		$this->db->commit();
		$this->ajaxResponse('success', '操作成功,退回成功', '0');
	}

	public function SmsTestAction()
	{

		$mobile = '13140410750';
		$result = Sms::send($mobile, ['code' => '1100']);
		var_dump($result);
		die;
	}
	//神农庄园虚拟币提现

	/** user/virtual
	 * @param number 提现金币数量
	 * @param  mobile 手机号
	 * @param  address 收货地址
	 * @param  userName 提现人姓名
	 */
	public function virtualAction()
	{
		$number = $this->request->getPost('number');
		$address = htmlspecialchars($this->request->getPost('address'));
		$payPassword = $this->request->getPost('payPassword');
		$result = $this->checkPayPassword($this->uid, $payPassword);
		if (!$result) {
			$this->ajaxResponse('error', '操作失败,支付密码错误', '1');
		}
		if ($number <= 0) {
			$this->ajaxResponse('error', '数量错误，请重新填写', '1');
		}
		$this->db->begin();
		$rebate = ($this->getVirtual() * 0.01);
		//扣除用户金币
		$userInfo = User::findFirst("id = '{$this->uid}'");
		$this->clockTable('dhc_user', "id = $this->uid");
		if (empty($userInfo)) {
			$this->ajaxResponse('error', '操作失败，用户信息错误', '1');
		}
		if ($userInfo->coing < $number) {
			$this->ajaxResponse('error', '操作失败，您的金币不足', '1');
		}
		$userInfo->coing -= $number;
		$userInfo->Frozen += $number;
		$userCoing = $userInfo->update();
		if (empty($userCoing)) {
			$this->db->rollback();
			$this->ajaxResponse('error', '操作失败，用户信息异常', '1');
		} else {
			$this->saveTradeLogs(['uid' => $this->uid, 'num' => $number, 'log' => '用户金币提现虚拟币操作' . $number, 'type' => 'dedcoing']);
		}
		$virtual = new UserVirtual();
		$virtual->uid = $this->uid;
		$virtual->goldnumber = $number;
		$virtual->number = ($number * $rebate);
		$virtual->address = $address;
		$virtual->rebate = $rebate;
		$virtual->createtime = TIMESTAMP;
		$virtualResult = $virtual->save();
		if (empty($virtualResult)) {
			$this->db->rollback();
			$this->ajaxResponse('error', '操作失败，提现信息有误', '1');
		}
		if ($userCoing && $virtualResult) {
			$userCost = new UserCost();
			$userCost->uid = $this->uid;
			$userCost->createtime = TIMESTAMP;
			$userCost->sum = $number;
			$userCost->charge = '0';
			$userCost->status = '0';
			$userCost->type = "虚拟币提现";
			$userCost->orderNumber = $this->createOrderNumber($this->uid, 'XT');
			$userCostResult = $userCost->save();
			if (!empty($userCostResult)) {
				$this->db->commit();
				$this->ajaxResponse('success', '操作成功，提现请求成功', '0');
			} else {
				$this->db->rollback();
				$this->ajaxResponse('error', '申请失败，请联系管理员', '1');
			}
		}

	}
	//增加收货地址

	/** user/shopAddress
	 * @param type ['add','del','list'] 操作类型 默认list
	 * @param username 收货人名称
	 * @param address 收货人地址
	 * @param mobile 收货人手机号
	 * @param  id      操作的收货地址id
	 */
	public function shopAddressAction()
	{
		$op = ['add', 'del', 'list'];
		$type = $this->request->getPost('type');
//		$title 	 = $this->request->getPost('username');
		$address = $this->request->getPost('address');
//		$mobile  = $this->request->getPost('mobile');
		$aid = $this->request->getPost('id');
		if (!in_array($type, $op)) {
			$this->ajaxResponse('error', '操作失败，操作类型错误', '1');
		}
		if (empty($type)) {
			$type = 'list';
		}
		if ($type == 'add') {
			if ($this->request->isPost()) {
//				if (empty($title)){
//					$this->ajaxResponse('error','操作失败，收货人名称不能为空','1');
//				}
				if (empty($address)) {
					$this->ajaxResponse('error', '操作失败，收货人地址不能为空', '1');
				}
//				if (empty($mobile)){
//					$this->ajaxResponse('error','操作失败,收货人手机号不能为空','1');
//				}
				$withdrawAddress = new WithdrawAddress();
				$withdrawAddress->uid = $this->uid;
//				$withdrawAddress->title = $title;
				$withdrawAddress->address = $address;
//				$withdrawAddress->mobile = $mobile;
				$withdrawAddress->createtime = TIMESTAMP;
				$withdrawAddressResult = $withdrawAddress->save();
				if (!empty($withdrawAddressResult)) {
					$this->ajaxResponse('success', '操作成功,添加收货地址成功', '0');
				} else {
					$this->ajaxResponse('error', '操作失败，添加收货地址异常', '1');
				}
			}
		} elseif ($type == 'del') {
			if (empty($aid)) {
				$this->ajaxResponse('error', '操作失败，请确认操作是否正确', '1');
			}
			$withdrawAddress = WithdrawAddress::findFirst("id = '{$aid}'");
			if (empty($withdrawAddress)) {
				$this->ajaxResponse('error', '操作失败，该地址已经删除或者不存在', '1');
			}
			$withdrawAddressResult = $withdrawAddress->delete();
			if (empty($withdrawAddressResult)) {
				$this->ajaxResponse('success', '操作失败,地址删除异常', '1');
			} else {
				$this->ajaxResponse('success', '操作成功,该地址已成功删除', '0');
			}
		} elseif ($type == 'list') {
			$withdrawAddress = WithdrawAddress::find(
				[
					'conditions' => "uid = '{$this->uid}'",
					'order' => "createtime DESC",
					'limit' => '5'
				]
			);
			if (empty($withdrawAddress)) {
				$this->ajaxResponse('error', '用户尚未设置收货地址', '1');
			}
			$this->ajaxResponse($withdrawAddress, '用户收货地址', '0');
		} else {
			$this->ajaxResponse('error', '操作类型不正确', '1');
		}
	}


	public function checkOrderAction()
	{
		$orderNumer = $this->request->getPost('orderNumber');
		$userRecharge = Recharge::findFirst("orderNumber = '{$orderNumer}'");
		if (empty($userRecharge)) {
			$this->ajaxResponse('error', '订单号不存在', '1');
		}
		$this->ajaxResponse($userRecharge->payStatus, '订单支付状态', '0');
	}

	//虚拟币提现记录
	public function virtualListAction()
	{
		$virtualWithdraw = UserVirtual::find(
			[
				'conditions' => "uid = '{$this->uid}'",
				'order' => 'createtime DESC',
				'limit' => '10'
			]
		);
		$this->ajaxResponse($virtualWithdraw, '用户最新虚拟币提现记录', '0');
	}

	//判断用户是否可以赠送

	/**
	 * @pram uid 赠送用户id
	 * @pram productId 产品id
	 * @pram number 赠送产品数量
	 */
	public function checkGive($uid, $productId, $number)
	{
		$giveGold = intval($this->request->getPost('fruits'));
		$LimitInfo = Config::findFirst("key =  'productConfig'");
		if (!empty($LimitInfo)) {
			$value = unserialize($LimitInfo->value);
		} else {
			$value = '';
		}
		$stopPrice = $this->getStopPrice($productId);
		$giveNumberLeast = !empty($value['giveNumberLeast']) ? $value['giveNumberLeast'] : 10; //赠送数量最少限制
		$giveNumberMost = !empty($value['giveNumberMost']) ? $value['giveNumberMost'] : 1000;//赠送数量最多限制
		$giveGoldLeast = !empty($value['giveGoldLeast']) ? $value['giveGoldLeast'] : $stopPrice['riseStop'];//索取金币单价最低
		$giveGoldMost = !empty($value['giveGoldMost']) ? $value['giveGoldMost'] : '';//索取金币单价最高
		$giveFrequency = !empty($value['giveFrequency']) ? $value['giveFrequency'] : 3;//当日赠送次数
		$money = ($giveGold / $number);
		$td = strtotime(date('Y-m-d'));
		if ($number > $giveNumberMost) {
			$this->ajaxResponse('error', '赠送失败，单次果实赠送数量不可超过' . $giveNumberMost, '1');
		}
		if ($number < $giveNumberLeast) {
			$this->ajaxResponse('error', '赠送失败，单次果实赠送数量不可少于' . $giveNumberLeast, '1');
		}
		if (!empty($giveGoldMost)) {
			if ($money > $giveGoldMost) {
				$this->ajaxResponse('error', '赠送失败，单次赠送索取金币单价不可高于' . $giveGoldMost, '1');
			}
		}
		if ($giveGold == 0) {
			$numbers = UserGive::findFirst(" uid = $uid AND createtime >= $td AND giveGold = 0 ");
			if (!empty($numbers)) {
				$this->ajaxResponse('error', '操作失败，每日容许索取金币为0次数只有一次，已达上限', '1');
			}
		} elseif ($giveGold > 0) {
			$numbers = UserGive::find(" uid = $uid AND createtime >= $td AND $giveGold >= 0 ");
			if (count($numbers) >= $giveFrequency) {
				$this->ajaxResponse('error', '操作失败,当日赠送为' . $giveFrequency . '次已达上限', '1');
			}
			if ($money <= $giveGoldLeast) {
				$this->ajaxResponse('error', '赠送失败，单次赠送索取金币单价不可低于' . $giveGoldLeast, '1');
			}
		}
	}

	public function getStopPrice($productId)
	{
		$yd = strtotime(date("Y-m-d", time()) . "-1 day");
		$dailydate = Dailydate::findFirst(
			array(
				'conditions' => "sid = $productId and Date<=$yd",
				'order' => 'Date DESC',
				'limit' => 1
			)
		);
		if ($dailydate) {
			$closePrice = $dailydate->ClosingPrice;
		} else {
			$closePrice = Product::findFirst("id = $productId")->startprice;
		}
		$product = Product::findFirst("id = $productId");
		if (USER_TYPE == 'duojin' && $productId == 1) {
			$riseStop = $closePrice + 0.0003;
			$fallStop = $closePrice - 0.0003;
		} else {
			$riseStop = $closePrice * (1 + $product->rise);
			$fallStop = $closePrice * (1 - $product->fall);
		}
		if ($productId > 1) {
			$data['riseStop'] = $this->getCharges($riseStop, 'rise');
			$data['fallStop'] = $this->getCharges($fallStop, 'fall');
		} else {
			$data['riseStop'] = $this->getCharges($riseStop, 'seed');
			$data['fallStop'] = $this->getCharges($fallStop, 'seed');
		}
		if (!empty($data)) {
			return $data;
		} else {
			return 0;
		}
	}

	//手续费超过四位进一
	public function getCharges($charge, $type = '')
	{
		$charges = explode('.', $charge);
		if (!empty($charges[1])) {
			if (strlen($charges[1]) >= 6) {
				if (strlen($charges[1]) >= 6) {
					if ($type == 'rise') {
						$charge = $charges[0] . '.' . substr($charges[1], 0, 4);
					} elseif ($type == 'fall') {
						$charge = $charges[0] . '.' . substr($charges[1], 0, 4) + 0.0001;
					} elseif ($type == 'seed') {
						$charge = $charges[0] . '.' . substr($charges[1], 0, 5) + 0.00001;
					} elseif ($type == 'trade') {
						$charge = $charges[0] . '.' . substr($charges[1], 0, 4) + 0.0001;
					}
				}
				return $charge;
			} else {
				return $charge;
			}
		} else {
			return $charge;
		}
	}

	//订单退回
	public function testReturnAction()
	{
		echo '没有权限';
		die;
		if ($this->uid != 2919) {
			echo '没有权限';
			die;
		}
		$price = $this->request->getPost('price');
		$sid = $this->request->getPost('sid');
		$order = Order::find("sid = '$sid' AND price = '$price' AND type = 1 AND status = 0");
		if (!empty($order)) {
			foreach ($order as $key => $value) {
				$num = ($value->number - $value->dealnum);
				$result = $this->ReturnBuy($value->id, $value->uid, $num, $price);
				if (!$result) {
					var_dump('退回失败' . $result);
					die;
				}
			}
			var_dump('ok');
			die;
		} else {
			echo '暂无操作记录';
			die;
		}
	}

	public function ReturnBuy($id, $uid, $num, $price)
	{
		$this->db->begin();
		$order = Order::findFirst("id = '{$id}' AND status = 0");
		if (empty($order)) {
			$this->db->rollback();
			return false;
		}
		$order->status = 1;
		$order->endtime = TIMESTAMP;
		$orderResult = $order->update();
		$userInfo = User::findFirst("id = '{$uid}'");
		if (empty($userInfo)) {
			$this->db->rollback();
			return $id;
		}
		$money = ($userInfo->Frozen - $num * $price);
		//如果冻结金币不足 清除冻结金币返回
		if ($money < 0) {
			$nums = $userInfo->Frozen;
			$userInfo->Frozen -= $nums;
			$userInfo->coing += $nums;
			$userResult = $userInfo->update();
			if (empty($orderResult) || empty($userResult)) {
				$this->db->rollback();
				return $id;
			} else {
				$this->db->commit();
				$this->saveTradeLogs(['uid' => $uid, 'log' => '订单号' . $id . '系统退单返回(冻结金币不足)增加金币' . $nums, 'num' => $nums, 'type' => 'addcoing']);
				$this->saveTradeLogs(['uid' => $uid, 'log' => '订单号' . $id . '系统退单返回(冻结金币不足)扣除冻结金币' . $nums, 'num' => $nums, 'type' => 'dedfrozencoing']);
				return true;
			}
		} else {
			$userInfo->Frozen -= ($num * $price);
			$userInfo->coing += ($num * $price);
			$userResult = $userInfo->update();
			if (empty($orderResult) || empty($userResult)) {
				$this->db->rollback();
				return $id;
			} else {
				$this->db->commit();
				$this->saveTradeLogs(['uid' => $uid, 'log' => '订单号' . $id . '系统退单返回增加金币' . $num * $price, 'num' => $num * $price, 'type' => 'addcoing']);
				$this->saveTradeLogs(['uid' => $uid, 'log' => '订单号' . $id . '系统退单返回扣除冻结金币' . $num * $price, 'num' => $num * $price, 'type' => 'dedfrozencoing']);
				return true;
			}
		}
	}

}

