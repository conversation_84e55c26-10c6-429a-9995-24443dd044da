<?php
namespace Dhc\Modules\Webapi\Controllers;
use Dhc\Models\Config;

/**
 * 发送短信验证
 * @param $data 发送内容
 * @param $to  发送地址
 * return  返回状态
 * Time: 9:36
 */
class MessageController extends ControllerBase
{
	/**
	 * 发送post请求
	 * @param string $url 请求地址
	 * @param array $post_data post键值对数据
	 * @return string
	 */
	public function sendAction($mobile, $content) {
		if (empty($content)) {
			$this->ajaxResponse('error', '请求类型错误', '1');
		}
		$post_data = array();
		$messageInfo = Config::findFirst("key = 'message'");
		if (!$messageInfo){
			$this->ajaxResponse('error','短信尚未配置','1');
		}
		$messageInfos = unserialize($messageInfo->value);
		$post_data['userid'] = $messageInfos['userid'];
		$post_data['account'] = $messageInfos['account'];
		$post_data['password'] = $messageInfos['password'];
		$post_data['mobile'] = $mobile;
		$post_data['content'] = $content;
		$url = $messageInfos['url'];
		$o='';
		foreach ($post_data as $k=>$v)
		{
			$o.="$k=".urlencode($v).'&';
		}
		$post_data=substr($o,0,-1);
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_URL,$url);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
		//curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //Èç¹ûÐèÒª½«½á¹ûÖ±½Ó·µ»Øµ½±äÁ¿Àï£¬ÄÇ¼ÓÉÏÕâ¾ä¡£
		$result = curl_exec($ch);
		$result = $this->xmlToArray($result);
		if ($result['returnstatus'] == "Success") {
			return true;
		} else {
			return $result['message'];
		}
	}
	private function xmlToArray($xml) {
		//禁止引用外部xml实体
		libxml_disable_entity_loader(true);
		$values = json_decode(json_encode(@simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
		return $values;
	}

}
