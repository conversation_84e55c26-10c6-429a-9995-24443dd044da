<?php
namespace Dhc\Modules\Webapi\Controllers;

use Dhc\Library\Distribution;
use Dhc\Models\Config;
use Dhc\Models\Order;
use Dhc\Models\Product;
use Dhc\Models\Reward;
use Dhc\Models\OrchardGoods;
use Dhc\Models\OrchardUser;
use Dhc\Models\TradeLogs;
use Dhc\Models\User;
use Dhc\Models\UserCost;
use Dhc\Models\OrchardLogs;
use Dhc\Models\UserLog;
use Phalcon\Mvc\Controller;
use function PHPSTORM_META\elementType;

class ControllerBase extends Controller
{
	public $uid;
	//对用户权限进行判断
	protected function initialize() {
		$this->view->disable();
		$controller = $this->dispatcher->getControllerName();
		$this->isAllow($controller);
	}

	public function ajaxResponse($data = '', $msg = '', $code = '0'){
		$response = [
			'data'  => $data,
			'msg'   => $msg,
			'code'  => $code
		];
		echo json_encode($response,JSON_UNESCAPED_UNICODE);
		exit;
	}
	public function create_salt( $length = '' ) {
		$chars = '0123456789';
		$salt = '';
		for ( $i = 0; $i < $length; $i++ )
		{
			$salt .= $chars[ mt_rand(0, strlen($chars) - 1) ];
		}
		return $salt;
		}
	public function checkuserlogin($uid = ''){
//		$token = $this->request->getPost('token');
//		if (!$token){$this->ajaxResponse('','非法登录，没有登录令牌','1');}
		if (empty($uid)){return false;};
		$uid = $this->session->get('uid');
		if (empty($uid)){
			return false;
		}else{
			return true;
		}
	}
	public function createToken($uid,$timestamp){
		$user = new User();
		$salt = $user->findFirst(
			array(
				'conditions'	=>	"id = $uid",
				'columns'		=>	'salt'
			)
		);
		$userSalt = $salt->salt;
		$cipherText = $this->encrypt($uid.$timestamp.$userSalt,'E','dj');
		$token = base64_encode($uid.'_'.$timestamp.'_'.$cipherText);
		return $token;
	}
	public function checkToken($token){
		$auth = $token;
		$tokens = base64_decode($auth);
		$checkToken = explode('_',$tokens);
		$uid = $checkToken[0];
		if (!is_numeric($uid)){
			$uid = $checkToken[1];
			//如果用户在不同的浏览器登录失效会报错
			if (!empty($checkToken[0])){
				@$tokenGame = $checkToken[0];
			}else{
				return false;
			}

		}

		$user = new User();
		$salt = $user->findFirst("id = $uid AND status = 1");
		if (empty($salt)){
			return false;
		}
		if ($auth == $salt->token){
			$this->uid = $uid;
			return true;
		}else{
			if (isset($tokenGame)){
				if($salt->token == $tokenGame){
					$this->uid = $uid;
					return true;
				}
			}
			return false;
		}
	}
	//获取产品手续费
	public function getRenascence($productId){
		$product = Product::findFirst($productId);
		if (!empty($product->poundage)){
			return $product->poundage;
		}
		return 0;
	}
	public function checkMobil($mobile)
	{
		$isMatched = preg_match('/^0?(13|14|15|17|18)[0-9]{9}$/', $mobile, $matches);
		if ($isMatched) {
			return true;
		} else {
			return false;
		}
	}
	/*********************************************************************
	函数名称:encrypt
	函数作用:加密解密字符串
	使用方法:
	加密   :encrypt('str','E','nowamagic');
	解密   :encrypt('被加密过的字符串','D','nowamagic');
	参数说明:
	$string  :需要加密解密的字符串
	$operation:判断是加密还是解密:E:加密  D:解密
	$key   :加密的钥匙(密匙);
	 *********************************************************************/
	protected function encrypt($string,$operation,$key='')
	{
		$key=md5($key);
		$key_length=strlen($key);
		$string=$operation=='D'?base64_decode($string):substr(md5($string.$key),0,8).$string;
		$string_length=strlen($string);
		$rndkey=$box=array();
		$result='';
		for($i=0;$i<=255;$i++)
		{
			$rndkey[$i]=ord($key[$i%$key_length]);
			$box[$i]=$i;
		}
		for($j=$i=0;$i<256;$i++)
		{
			$j=($j+$box[$i]+$rndkey[$i])%256;
			$tmp=$box[$i];
			$box[$i]=$box[$j];
			$box[$j]=$tmp;
		}
		for($a=$j=$i=0;$i<$string_length;$i++)
		{
			$a=($a+1)%256;
			$j=($j+$box[$a])%256;
			$tmp=$box[$a];
			$box[$a]=$box[$j];
			$box[$j]=$tmp;
			$result.=chr(ord($string[$i])^($box[($box[$a]+$box[$j])%256]));
		}
		if($operation=='D')
		{
			if(substr($result,0,8)==substr(md5(substr($result,8).$key),0,8))
			{
				return substr($result,8);
			}
			else
			{
				return'';
			}
		}
		else
		{
			return str_replace('=','',base64_encode($result));
		}
	}

	private function getApiResources (){
		if(USER_TYPE == "kkS"){
			//kk 信息修改
			$data = array(
				"private"=>array(
					'market','user','recharge','Raiders','Yrecharge,article',
					'Market','User','Recharge','raiders','yrecharge,Article'
				),
				'public'=>array(
					'auth','config','raiders','Util','Notify',
					'Auth','Config','Raiders','util','notify',
				)
			);
		}else{
			$data = array(
				"private"=>array(
					'market','user','recharge','Raiders','Yrecharge',
					'Market','User','Recharge','raiders','yrecharge'
				),
				'public'=>array(
					'article','auth','config','raiders','Util','Notify',
					'Article','Auth','Config','Raiders','util','notify',
				)
			);
		}
		return $data;
	}

	private function isAllow($controller) {
		$resources = $this->getApiResources();
		$token = @$_SERVER['HTTP_TOKEN'];
		if (empty($token)){
			$token = $this->request->getPost('token');
			if (empty($token)){
				$token = $this->request->get('token');
			}
		}

		if (!empty($token)) {
			$result = $this->checkToken($token);
			if ($result) {
				if (empty($this->session->get('logintime'))){
					$this->session->set('logintime', TIMESTAMP);
				}
				$uid = $this->uid;
				if (USER_TYPE == 'jindao'){
					$this->olineUser($uid);
				}
				//文章权限 kk 单独修改
				if($controller == "article" && USER_TYPE == "kk"){
					return true;
				}
				if (!in_array($controller, $resources['private'])) {
					$this->ajaxResponse('error', '没有权限访问,请登录', '403');
				}
				if (in_array($controller, $resources['private'])) {
					if (!empty($this->session->get('logintime'))) {
						$logintime = $this->session->get('logintime');
						$this->session->set('logintime', TIMESTAMP);
						if ($this->session->get('logintime') - $logintime > 3600) {
							$this->ajaxResponse('error', '登录超时，请重新登录', '403');
						}
					}
				}
			} else {
				if (!in_array($controller, $resources['public'])) {
					$this->ajaxResponse('error', '未登录,请登录2', '403');
				}
			}
		} else {
			if (!in_array($controller, $resources['public'])) {
				$this->ajaxResponse('error', '未登录,请登录1', '403');
			}
		}
	}

	public function olineUser($uid){
		$user = User::findFirst("id = '$uid'");
		$info = json_encode($user->toArray());
		$userInfo = UserLog::findFirst("uid = '{$uid}'");
		if (empty($userInfo)){
			$userInfo = new UserLog();
			$userInfo->uid = $uid;
			$userInfo->user = $this->getMoblie($uid);
			$userInfo->ip = $this->request->getClientAddress();
			$userInfo->logintime = TIMESTAMP;
			$userInfo->info = $info;
			$result = $userInfo->save();
		}else{
			if ($userInfo->logintime + 180 > TIMESTAMP){
				return true;
			}
			$userInfo->logintime = TIMESTAMP;
			$userInfo->info = $info;
			$result = $userInfo->update();
		}
		return $result;
	}
	public function createCost($data){
//		$uid,$number,$fee,$type
		$userCost = new UserCost();
		$userCost->uid = $data['uid'];
		$userCost->orderNumber = $this->createOrderNumber($data['uid'],$data['type']);
		$userCost->sum = $data['number'];
//		$userCost->
	}
	//生成订单号
	public function createOrderNumber($uid,$type){
		return $orderNumber = $type.date("YmdHis",TIMESTAMP).str_pad($uid,'6','0',STR_PAD_LEFT);
	}
	//上级返利
	public function rebate($uid,$fee){
		$userInfo = User::findFirst("id = $uid");
		$spreads =explode('-',$userInfo->superior);
		unset($spreads[0]);
		foreach ($spreads as $value){
			if ($fee > 0.01){
				$users = User::findFirst("id = $value");
				$spreadfee =$users->spreadfee;
				$users->coing += ($fee*(1+$spreadfee));
				$result = $users->update();
				if ($result){
					$userCost = new UserCost();
					$userCost->orderNumber = $this->createOrderNumber($value,'TG');
					$userCost->uid =$value;
					$userCost->createtime = TIMESTAMP;
					$userCost->fee = $fee;
					$userCost->sum = ($fee*(1+$spreadfee));
					$userCost->type = '推广返利';
					$userCostResult = $userCost->save();
					if ($userCostResult){
						$fee = ($fee*(1-$spreadfee));
					}else{
						foreach ($userCost->getMessages() as $message){
							echo $value.$message;
						}die;
					}
				}else{
					return false;
				}
			}
		}
	}

	public function getImgAction(){
		if ($this->request->getPost('imgCode')){
			$code = $this->request->getPost('imgCode');
			if(!empty($code)){
				$check = new UtilController();
				$result =  $check->checkVerify($code);
				if ($result == false){
					$this->ajaxResponse('','验证码错误','1');
				}
			}else{
				$this->ajaxResponse('','验证码不可为空','1');
			}

		}else{
			$verify = new VerifyImage();
			return $verify->entry();
		}

	}

	/**
	 * 日志记录函数
	 * @param max $message 日志内容，建议为数组包含操作函数和原因
	 * @param string $level 日志重要程度
	 */
	public function PayLog($message , $level = "info") {
		$file = WEB_PATH . '/logs/';
		if(!file_exists($file)){
			@mkdir($file);
		}
		$filename = $file . date('Ymd') . '.txt';
		$content = date('Y-m-d H:i:s') . " {$level} :\n------------\n";
		if (is_string($message)) {
			$content .= "String:$message";
		}
		if (is_object($message)) {
			$content .= "Object:\n" . var_export($message, TRUE) . "\n";
		}
		if (is_array($message)) {
			$content .= "Array:\n";
			foreach ($message as $key => $value) {
				$content .= sprintf("%s : %s ;\n", $key, $value);
			}
		}
		$content .= "\n";
		$fp = fopen($filename, 'a+');
		fwrite($fp, $content);
		fclose($fp);
	}

	//操作日志记录
	function saveOrchardLogs($data = array()){
		$logs = new OrchardLogs();
		if(empty($data["uid"])){
			$data["uid"] = $this->userid;
		}
		$logs->uid = $data["uid"];
		if(!empty($data["disUid"])){
			$logs->disUid = $data["disUid"];
		}
		if(isset($data["landId"]) && $data["landId"] !== ""){
			$logs->landId = $data["landId"];
		}
		$logs->mobile = $data["mobile"];
		$logs->types = $data["types"];
		$logs->nums = $data["nums"];
		$logs->msg = $data["msg"];
		if(!empty($data["dataInfo"])){
			$logs->dataInfo = $data["dataInfo"];
		}
		$logs->createtime = TIMESTAMP;
		$logs->status = 1;
		return $logs->save();
	}
	public function getSqlError($flag,$obj){
		if($flag == false){
			foreach ($obj->getMessages() as $message) {
				echo $message . '<br>';
			}die;
		}
	}

	public function distribution($data){
		// 返佣类型: 兑换钻石=1 大盘手续费=2
		$uid = $data['uid'];
		$type = $data['type'];
		$num = $data['num'];
		$remark = $data['remark'];
		try{
			$dis = new Distribution($uid,$num,$data['type'],$data['remark']);
			$flag = $dis->start();//成功返回true 失败会会抛出异常 不影响后面的代码执行
		}catch (Exception $e){
			// echo $e->getFile() . ' on line ' . $e->getLine() . ':' . $e->getMessage();
			$this->db->rollback();
			echo $e->getMessage(); // 错误信息
		}
	}
	//首次充值为上级加俩个金币
	public function teacherAddGold($uid){
		$userInfo = User::findFirst("id = $uid");
		$teachers = explode('-',$userInfo->superior);
		if (!empty($teachers)){
			$this->db->begin();
				$teacherInfo = User::findFirst("id = $teachers[0]");
				if ($teacherInfo){
					$teacherInfo->coing += 2 ;
					$result = $teacherInfo->update();
					if ($result){
						$userCost = new UserCost();
						$userCost->orderNumber = $this->createOrderNumber($teacherInfo->id,'SC');
						$userCost->uid = $teacherInfo->id;
						$userCost->createtime = TIMESTAMP;
						$userCost->endtime = TIMESTAMP;
						$userCost->sum = 2;
						$userCost->charge = '0';
						$userCost->status = 1;
						$userCost->type = '首充奖励';
						$userCostResult = $userCost->save();
					}
				}
				if ($result&&$userCostResult){
					$this->db->commit();
				}else{
					$this->db->rollback();
				}

		}
	}

	public function getMoblie($uid){
		$userinfo = User::findFirst("id = $uid");
		if ($userinfo){
			$mobile = $userinfo->user;
		}else{
			$mobile = '';
		}
		return $mobile;
	}

	public function saveTradeLogs($data = array()){
		$logs = new TradeLogs();
		$logs->uid = $data["uid"];
		$logs->mobile = $this->getMoblie($data["uid"]);
		$logs->num = $data["num"];
		$logs->logs = $data["log"];
		$logs->type = $data["type"];
		$logs->createtime = TIMESTAMP;
		$logs->status = 1;
		$result =  $logs->save();
		return $result;
	}

	public function getfruitPrice(){
		$order = Order::findFirst(
			[
				'conditions'=>"sid = 1 and status = 1",
				'order'		 =>'endtime DESC',
				'limit'		 =>'1'
			]
		);
		return $order->price;
	}

	public function saveTrade($data){
		$userCost = new UserCost();
		$userCost->uid = $data['uid'];
		$userCost->orderNumber = $this->createOrderNumber($data['uid'],'XF');
		$userCost->createtime = TIMESTAMP;
		$userCost->endtime = TIMESTAMP;
		if ($data['type'] =='出售产品'){
			$userCost->sum = $data['num'];
		}else{
			$userCost->sum = -$data['num'];
		}

		$userCost->charge = $data['fee'];
		$userCost->status = 1;
		$userCost->type = $data['type'];
		return $userCost->save();
	}

	public function getReward($rid)
    {
        $reward = Reward::findFirst(
            [
                'conditions'=>"id = {$rid}",
                'columns'   =>"SUM(productnum) as number"
            ]
        );
        if ($reward){
            return $reward->number;
        }else{
            return 0;
        }
    }
    //淘金乐园推荐会员礼包
    public function getRecommondGift($uid){
        if(!empty($uid)){
            $user = new User();
            $userInfo = $user->findFirst("id='{$uid}' AND status=1");
            $goods = new OrchardGoods();
            $recommondGoods = $goods->findFirst("tId=22 AND status=1 AND isDel=0");
            $giftInfo = json_decode($recommondGoods->giftDetail,true);
            $recommond = $giftInfo["recommond"];
            $userInfo->spreadCount +=1;
            $flagA = $userInfo->update();
            if($flagA == false){
                return false;
            }
            $userInfo = $user->findFirst("id='{$uid}' AND status=1");
            if($userInfo->spreadCount>=$recommond['people']){
                $orchardUser = new OrchardUser();
                $orchardUserInfo = $orchardUser->findFirst("uid='{$userInfo->id}' AND status=1");
                $count = floor($userInfo->spreadCount/$recommond['people']);
                $orchardUserInfo->gifts +=$count;
                $orchardUserInfo->updatetime = TIMESTAMP;
                $flagB = $orchardUserInfo->update();
                $userInfo->spreadCount -= intval($count*$recommond['people']);
                $userInfo->updatetime = TIMESTAMP;
                $flagC = $userInfo->update();
                $flagD = $this->saveOrchardLogs(array("uid"=>$uid,"mobile"=>$userInfo->user,"types"=>"addgift","nums"=>$count,"msg"=>"推荐用户获得推荐礼包".$count."个"));
                if($flagB ==false || $flagC == false || $flagD == false){
                    return false;
                }
            }
        }
        return true;
    }
    //支付密码校验
    public function checkPayPassword($uid,$PayPassword){
		$userInfo = User::findFirst("id = '{$uid}'");
		if (empty($userInfo)){
			return false;
		}else{
			$salt = $userInfo->salt;
			$rePayPassword = sha1($salt.$PayPassword);
			if ($rePayPassword == $userInfo->payPassword){
				return true;
			}else{
				return false;
			}
		}
	}
	//交易剩余金币返还
	public function ReturnGold($oid){
    	if (!empty($oid)){
    		$order = Order::findFirst(
    			[
    				'conditions'=>"id = $oid AND status = 1",
					'order'		=>'createtime DESC',
					'limit'		=>'1'
				]
			);
    		if (empty($order)){
    			return false;
			}
			if ($order->dealMoney<($order->number*$order->price)){
    			$money = (($order->number*$order->price) - $order->dealMoney);
    			$userInfo = User::findFirst("id = '{$order->uid}'");
    			$userInfo->coing += $money;
    			$userInfo->Frozen -= $money;
    			$result = $userInfo->update();
    			if (!empty($result)){
    				$order->dealMoney = ($order->number*$order->price);
    				$orderResult = $order->update();
    				if (!empty($orderResult)){
						$this->saveTradeLogs(['uid'=>$userInfo->id,'num'=>$money,'log'=>'大盘交易系统提交和交易订单价格不同返还'.$money,'type'=>'addcoing']);
					}
				}
			}
		}
	}

	public function test($id){
		$user = User::findFirst("id = $id");
		var_dump($user->coing,$user->Frozen);die;
	}

	//获取虚拟币提现比率
	public function getVirtual(){
		$withdraw = Config::findFirst("key = 'withdraw'");
		if (!empty($withdraw)){
			$withdrawValue = unserialize($withdraw->value);
			return $withdrawValue['rebate'];
		}else{
			return 0 ;
		}
	}

	//获取启用的短信接口
	public function getOpenMessage(){
		$config = Config::find("key = 'jh' OR key = 'message'");
		if (empty($config)){
			return false;
		}
		foreach ($config as $key=>$value){
			$data[] = unserialize($value->value);
		}
		foreach ($data as $k=>$v){
			if ($v['status'] == 1){
				$info = $data[$k];
				return $info;
			}
		}
		return false;
	}

	//数据库记录加锁
	public function clockTable($table,$condition){
		$sql = "SELECT * FROM `$table` WHERE ".$condition." FOR UPDATE";
		$this->db->query($sql);
	}

    /**
     * xml转数组
     * @param $xml
     * @return bool|mixed
     */
    public function xml_to_data($xml){
        if(!$xml){
            return false;
        }
        //将XML转为array
        //禁止引用外部xml实体
        libxml_disable_entity_loader(true);
        $data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        return $data;
    }

    /**
     * 验签
     * @param $key
     * @param $data
     * @return string
     */
    public function checkSign($key,$data)
    {
        //签名步骤一：按字典序排序参数
        ksort($data);
        $string = $this->ToUrlParams($data);
        //签名步骤二：在string后加入KEY
        $string = $string . "&key=".$key;

        //签名步骤三：MD5加密
        $string = md5($string);
        //签名步骤四：所有字符转为大写
        $result = strtoupper($string);
        return $result;
    }

    /**
     * 拼接参数
     * @return string
     */
    public function ToUrlParams($data)
    {
        $buff = "";
        foreach ($data as $k => $v)
        {
            if($k != "sign" && $v != "" && !is_array($v)){
                $buff .= $k . "=" . $v . "&";
            }
        }
        $buff = trim($buff, "&");
        return $buff;
    }
    /**
     * 数组转xml
     * @param $params
     * @return bool|string
     */
    public function data_to_xml( $params ){
        if(!is_array($params)|| count($params) <= 0)
        {
            return false;
        }
        $xml = "<xml>";
        foreach ($params as $key=>$val)
        {
            if (is_numeric($val)){
                $xml.="<".$key.">".$val."</".$key.">";
            }else{
                $xml.="<".$key."><![CDATA[".$val."]]></".$key.">";
            }
        }
        $xml.="</xml>";
        return $xml;
    }
}
