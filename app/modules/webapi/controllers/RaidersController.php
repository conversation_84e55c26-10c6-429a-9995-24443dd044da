<?php
namespace Dhc\Modules\Webapi\Controllers;

use Dhc\Models\Product;
use Dhc\Models\Raiders;
use Dhc\Models\Reward;
use Dhc\Models\User;
use Dhc\Models\UserProduct;
use Phalcon\Paginator\Adapter\Model as PaginatorModel;

class RaidersController extends ControllerBase
{
	//攻略列表
	public function raiderlistAction() {
		$condition = $this->createCondition();
		$pagenow = $this->request->getPost('page');
		if (!isset($condition['condition'])) {
			$conditions = 'status = 1';
		} else {
			$conditions = ($condition['condition'] . 'and status = 1');
		}
		if (empty($pagenow)){$pagenow = 1;}

		if (!empty($condition['order'])) {
			$order = $condition['order'];
		} else {
			$order = ' createtime DESC';
		}
		$raiderlist = Raiders::find(
			array(
				'conditions' => $conditions,
				'order' => $order,
			)
		);
        $paginator = new PaginatorModel(
            array(
                "data" =>$raiderlist,
                "limit"=>10,
                "page" =>$pagenow
            )
        );
        $page = $paginator->getPaginate();
		if ($page->items) {
			$user = new User();
			$userlist = $user->find(
				array(
					'columns' => 'id,level,authLevel'
				)
			);
			$data = '';
			foreach ($userlist as $value) {
				foreach ($page->items as $v) {
					if ($v->auth == $value->id) {
						$level = $value->level;
						$authLevel = $value->authLevel;
						$v->level = $level;
						$v->authLevel = $authLevel;
						if (!empty($v->rewardproduct)) {
							$rewardproduct = $v->rewardproduct;
						} else {
							$rewardproduct = '';
						}
						$data['list'][] = array(
							'id' => $v->id,
							'auth' => $v->auth,
							'title' => $v->title,
							'content' => $v->content,
							'status' => $v->status,
							'createtime' => $v->createtime,
							'readtimes' => $v->readtimes,
							'reward' => $this->getReward($v->id),
							'praise' => $v->praise,
							'comments' => $v->comments,
							'bad' => $v->bad,
							'type' => $v->type,
							'level' => $v->level,
							'authLevel' => $v->authLevel,
							'rewardproduct' => $rewardproduct
						);
					}

				}
			}
			$data['total_page'] = $page->total_pages;
			$this->ajaxResponse($data, '攻略列表', '0');
		} else {
			$this->ajaxResponse('', '攻略列表暂无', '1');
		}
	}

	//获取攻略打赏数量

	public function getReward($id) {
		if (!empty($id)) {
			$reward = Reward::findFirst(
				array(
					'conditions' => "rid = $id ",
					'columns' => "sum(productnum) as number "
				)
			);
			if ($reward) {
				if ($reward->number) {
					return $reward->number;
				} else {
					return '0';
				}
			} else {
				return '0';
			}
		}
	}

	//发稿等级作者
	public function authLsitAction() {
		$userList = User::find(
			array(
				'conditions' => 'authLevel>0',
				'columns' => 'id,authLevel'
			)
		);
		if (!empty($userList)) {
			$this->ajaxResponse($userList, '已认证等级作者', '0');
		} else {
			$this->ajaxResponse('', '目前尚无认证等级作者', '1');
		}
	}

	public function createCondition() {
		$type = $this->request->getPost('type');
		$auth = $this->request->getPost('auth');
		$reward = $this->request->getPost('reward');
		$praise = $this->request->getPost('praise');
		$comments = $this->request->getPost('comments');
		$bad = $this->request->getPost('bad');
		if ($type !== '1' && $type !== '0') {
			$type = '';
		}
		if (!empty($praise)) {
			if ($praise !== 'DESC' && $praise !== 'ASC') {
				$praise = '';
			} else {
				$praise = "praise $praise";
			}

		}
		if (!empty($reward)) {
			if ($reward !== 'DESC' && $reward !== 'ASC') {
				$reward = '';
			} else {
				$reward = "reward $reward";
			}
		}
		if (!empty($comments)) {
			if ($comments !== 'DESC' && $comments !== 'ASC') {
				$comments = '';
			} else {
				$comments = " comments $comments";
			}
		}
		if (!empty($bad)) {
			if ($bad !== 'DESC' && $bad !== 'ASC') {
				$bad = '';
			} else {
				$bad = "bad $bad";
			}
		}
		$condition = " 1=1 ";
		if ($type === '1' || $type === '0') {
			$condition .= "and type =$type ";
			if (!empty($auth)) {
				$condition .= "and auth =$auth ";
			}
		} else {
			if (!empty($auth)) {
				$condition = " auth = $auth ";
			}
		}
		if ($praise || $comments || $bad || $reward) {
			$order = $praise . $comments . $bad . $reward;
		} else {
			$order = '';
		}
		$data = array(
			'condition' => $condition,
			'order' => $order
		);
		return $data;
	}

	//投稿
	public function SubmissionAction() {
		if ($this->request->isPost()) {
			$title = $this->request->getPost('title');
			$auth = $this->uid;
			$content = $this->request->getPost('content');
			$type = $this->request->getPost('type');
			if (empty($type)){
				$type = '0';
			}
			$rewardproduct = $this->request->getPost('rewardproduct');
			$user = new User();
			$userauth = $user->findFirst("id = $auth");
			if (!$userauth) {
				$this->ajaxResponse('', '作者不存在', '1');
			}
			$raider = new Raiders();
			$raider->auth = $auth;
			$raider->title = $title;
			$raider->content = $content;
			$raider->type = $type;
			$raider->createtime = TIMESTAMP;
			$raider->rewardproduct = $rewardproduct;
			$result = $raider->save();
			if (!$result) {
				foreach ($raider->getMessages() as $message) {
					echo $message;
				}
				die;
			}
			if ($result) {
				$this->ajaxResponse('success', '投稿成功，已提交审核', '0');
			} else {
				$this->ajaxResponse('error', '投稿失败', '1');
			}
		}
	}

	//攻略详情
	public function raiderDetialAction() {
		$this->uid = $this->session->get('uid');
		$uid = $this->uid;
		if (empty($uid)){$this->ajaxResponse('请登录后查看','请登录后查看','1');}
		if ($this->request->get()) {
			$id = $this->request->get('id');
			$type = $this->request->get('type');
			$raider = Raiders::findFirst(
				array(
					'conditions' => "id = $id",
					'columns' => 'id,auth,title,status,createtime,readtimes,reward,praise,comments,bad,type,rewardproduct'
				)
			);
			$auth = $raider->auth;
			$aboutRaider = Raiders::find(
				[
					'conditions'=>"auth = $auth and id <> $id",
					'columns'   =>'id,auth,title,status,createtime,readtimes,reward,praise,comments,bad,type,rewardproduct',
					'limit'		 => '4'
				]
			);
			$datas['raider'] = $raider;
			$datas['aboutRaider'] = $aboutRaider;
			if (!empty($type) && $type === '1') {
				$UserReward = Raiders::findFirst("id = $id and status = 1");
				if (!$UserReward) {
					$this->ajaxResponse('error', '该攻略已经撤销或者被管理员删除', '1');
				}
				if (!empty($UserReward->rewardproduct)) {
					$sid = $UserReward->rewardproduct;
                        $RewardInfo = Reward::findFirst("uid =$uid and rid = $id and productid =$sid");
                        if (!$RewardInfo) {
                            $this->ajaxResponse($datas, '用户尚未打赏付费攻略，不可查看', '0');
                        }
				} else {
					$RewardInfo = Reward::findFirst("uid = $uid and  rid = $id");
					if (!$RewardInfo) {
						$this->ajaxResponse($datas, '攻略详情1', '0');
					}
				}
			} elseif ($type > '1' || $type < '0') {
				$this->ajaxResponse('error', '攻略类型错误', '1');
			}
			$data['raider'] = $this->getone($id);
			$data['aboutRaider'] =  $aboutRaider;
			if (empty($data)) {
				$this->ajaxResponse('error', '暂无相关信息', '1');
			}
			$raiders = Raiders::findFirst("id = $id");
			$raiders->readtimes +=1;
			$raiders->update();
			$this->ajaxResponse($data, '攻略详情2', '0');
		}
	}

	//评价
	public function commentAction() {
		if ($this->request->isGet()) {
			$comment = $this->request->get('type');
			$id = $this->request->get('id');
			$raider = new Raiders();
			$raiderone = $raider->findFirst("id = $id");
			if (!empty($comment)) {
				if ($comment === 'praise') {
					$raiderone->praise += 1;
				} elseif ($comment === 'comments') {
					$raiderone->comments += 1;
				} elseif ($comment === 'bad') {
					$raiderone->bad += 1;
				}
			}
			$result = $raiderone->update();
			if ($result) {
				$this->ajaxResponse('success', '评价成功', '0');
			} else {
				$this->ajaxResponse('error', '评价失败', '1');
			}
		}
	}

	//打赏
	public function rewardAction() {
		$uid = $this->uid;
		if ($this->request->isPost()) {
			$rid = $this->request->getPost('rid');
			$raider = Raiders::findFirst("id = $rid");
			$auth = $raider->auth;
			if (!$raider) {
				$this->ajaxResponse('error', '打赏攻略不存在', '1');
			}
			if (empty($uid)) {
				$this->ajaxResponse('error', '打赏用户错误', '1');
			}
			if (empty($auth)) {
				$this->ajaxResponse('error', '当前用户错误', '1');
			}
			$productId = $this->request->getPost('productid');
			if (empty($productId)) {
				$this->ajaxResponse('error', '打赏产品错误', '1');
			}
			$productNum = $this->request->getPost('productnum');
			if (empty($productNum)) {
				$this->ajaxResponse('error', '打赏数量不能为空', '1');
			}
			$discuss = $this->request->getPost('discuss');
			$this->db->begin();
			$productNums = UserProduct::findFirst("uid = $uid and sid =$productId");
			if (!$productNums) {
				$this->ajaxResponse('', '打赏的产品不存在', '1');
			}
			$raider = Raiders::findFirst("id = $rid");
			if (!empty($raider->rewardproduct)) {
				if ($productId !== "$raider->rewardproduct") {
					$this->ajaxResponse('error', '该攻略指定打赏', '1');
				}
			}
			if ($productNum > $productNums->number) {
				$this->ajaxResponse('', '用户没有充足的数量打赏', '1');
			}
//			$productNums->sid = $productId;
//			$productNums->uid  =$uid;
			$productNums->number -= $productNum;
			$this->saveTradeLogs(array("uid" =>$uid, "num" =>$productNum, "type" => "dedproduct", "log" => "攻略打赏扣除产品.$productId-" .$productNum ));
			$result = $productNums->update();
			$userProduct1 = new UserProduct();
			$userProduct1Info = $userProduct1->findFirst("uid = $auth and sid =$productId");
			if ($userProduct1Info) {
				$userProduct1Info->number += ($productNum / 2);
				$this->saveTradeLogs(array("uid" =>$auth, "num" =>($productNum / 2), "type" => "addproduct", "log" => "攻略打赏增加产品.$productId-" .($productNum / 2) ));
				$userProduct1Info->updatetime = TIMESTAMP;
				$result1 = $userProduct1Info->update();
			} else {
				$userProduct1->uid = $auth;
				$userProduct1->number = ($productNum / 2);
				$userProduct1->sid = $productId;
				$this->saveTradeLogs(array("uid" =>$auth, "num" =>($productNum / 2), "type" => "addproduct", "log" => "攻略打赏增加产品.$productId-" .($productNum / 2) ));
				$userProduct1->createtime = TIMESTAMP;
				$userProduct1->updatetime = TIMESTAMP;
				$result1 = $userProduct1->save();
			}
			$reward = new Reward();
			$reward->rid = $rid;
			$reward->uid = $uid;
			$reward->auth = $auth;
			$reward->productid = $productId;
			$reward->productnum = $productNum;
			$reward->createtime = TIMESTAMP;
			$reward->discuss = $discuss;
			$result2 = $reward->save();
			if ($result && $result1 && $result2) {
				$this->db->commit();
				$this->ajaxResponse('success', '打赏成功', '0');
			} else {
				$this->db->rollback();
				$this->ajaxResponse('error', '失败', '1');
			}

		}
	}

	//打赏记录
	public function rewardListAction() {
		if ($this->request->isPost()) {
			$id = $this->request->getPost('id');
			$reward = new Reward();
			$rewardList = $reward->find(
				array(
					'conditions' => "rid =$id",
					'order' => 'createtime DESC'
				)
			);
			$data = $rewardList->toArray();
			foreach ($data as $key => $value) {
				$data[$key]['thumb'] = $this->getProductThumb($value['productid']);
			}
			$this->ajaxResponse($data, '打赏记录', '0');
		}
	}

	public function getone($id) {
		$raider = new Raiders();
		$raiderlist = $raider->findFirst(
			array(
				'conditions' => "status = 1 and id = $id ",
			)
		);
		if ($raiderlist) {
			$user = new User();
			$userlist = $user->find(
				array(
					'columns' => 'id,level'
				)
			);
			$data = '';
			foreach ($userlist as $v) {
				if ($v->id == $raiderlist->auth) {
					$data = array(
						'id' => $raiderlist->id,
						'auth' => $raiderlist->auth,
						'title' => $raiderlist->title,
						'content' => $raiderlist->content,
						'status' => $raiderlist->status,
						'createtime' => $raiderlist->createtime,
						'readtimes' => $raiderlist->readtimes,
						'reward' => $this->getReward($raiderlist->id),
						'praise' => $raiderlist->praise,
						'comments' => $raiderlist->comments,
						'bad' => $raiderlist->bad,
						'type' => $raiderlist->type,
						'level' => $v->level,
						'rewardProduct' => $raiderlist->rewardproduct
					);
				}

			}
			return $data;
		} else {
			return false;
		}
	}

	public function getProductThumb($sid) {
		$product = Product::findFirst("id = $sid");
		if (!empty($product)) {
			$thumb = $product->thumb;
			return $thumb;
		} else {
			return false;
		}

	}
}
