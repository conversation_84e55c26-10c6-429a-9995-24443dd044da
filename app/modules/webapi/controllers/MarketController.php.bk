<?php
namespace Dhc\Modules\Webapi\Controllers;

use Dhc\Models\Config;
use Dhc\Models\Dailydate;
use Dhc\Models\OrchardUser;
use Dhc\Models\Order;
use Dhc\Models\Product;
use Dhc\Models\TelMessage;
use Dhc\Models\User;
use Dhc\Models\UserCost;
use Dhc\Models\UserGive;
use Dhc\Models\UserProduct;
use Dhc\Models\UserWithdraw;

class MarketController extends ControllerBase
{
	public $productId;
	public $coing;
	//大盘日数据
	public function productAction() {
//		$uid = intval(trim($this->request->getPost('uid')));
//		$checklogin = $this->checkuserlogin($uid);
//		if (!$checklogin) {
//			$this->ajaxResponse('', '用户未登录', '');
//		}

		$pid = intval($this->request->get('pid'));
		$this->productId = $pid;
		if (empty($pid)) {
			$this->ajaxResponse('', "产品ID不存在", '1');
		}
		$daliydata = new Dailydate();
		$daliydatalist = $daliydata->find(
			array(
				'conditions'=>"sid = $pid",
			)
		);
		if (!empty($daliydatalist)){
			foreach ($daliydatalist as $key => $value) {
				if ($value->Volume == 50){
					$value->Volume = 100;
				}
				$data['KLineList'][] = array(
					'Date' => $value->Date,
					'OpeningPrice' => $value->OpeningPrice,
					'ClosingPrice' => $value->ClosingPrice,
					'HighestPrice' => $value->HighestPrice,
					'LowestPrice' => $value->LowestPrice,
					'Volume' => $value->Volume,
				);
			}
		}
		if (empty($data)){
			$data['KLineList'][] =array(
				'Date' => TIMESTAMP,
				'OpeningPrice' => 0,
				'ClosingPrice' => 0,
				'HighestPrice' => 0,
				'LowestPrice' => 0,
				'Volume' => 0,
			);
		}
		$response = [
			"data" => $data,
			'msg' => '大盘数据',
			'code' => 0
		];
		$this->ajaxResponse($response['data'], $response['msg'], $response['code']);
	}
	//md5 线数据
	public function timeshareAction(){
		if ($this->request->isPost()){
			if (!empty($this->request->getPost('sid'))){
				$time = TIMESTAMP;
				$sid = $this->request->getPost('sid');
				$dateStr = date('Y-m-d',$time);
				$timestamp24 = 	TIMESTAMP;
				$timestamp9 = strtotime($dateStr)+9*3600;
				for ($i=$timestamp9;$i<$timestamp24;$i+=60){
					$orderInfo =  Order::findFirst(
						array(
							'conditions'	=>	"endtime >$i-60 and endtime<$i and sid = $sid",
							'columns'		=>	'sum(dealnum) as number,price',
							'order'			=>	'endtime DESC',
							'limit'			=>	'1'
						)
					);
					if (!empty($orderInfo->number)){
						$number = $orderInfo->number;
					}else{
						$number = 0;
					}
					if (!empty($orderInfo->price)){
						$price = $orderInfo->price;
					}else{
						$price = $this->getstarPrice($sid);
//						$price = 0.93;
					}
					$data[]=array(
						'Volume'=>$number,
						'Price'=>$price,
						'Time'	=> $i
					);

//					if ($orderInfo->number === null){
//						$data[]['Volume'] = '0';
//					}else{
//						$data[]['Volume'] = $orderInfo->number;
//					}
//					if($orderInfo->price === null){
//						$data[]['Price'] = '0';
//					}else{
//						$data[]['Price'] =$orderInfo->price;
//					}
//					$data['Time'] = date("Y-m-d H:i:s",TIMESTAMP);
				}
				$this->ajaxResponse($data,'产品信息','0');
			}
		}
	}
	//返回最新订单
	public function newEntrustAction() {
		//获取产品id
		$sid = intval($this->request->get('sid'));
		if (empty($sid)) {
			$this->ajaxResponse('', '产品id不存在');
		}
		if ($sid) {
			$order = new Order();
			$buylists = $order->find(
				array(
					'conditions' => "sid = $sid and status = 0 and type = 1",
					'columns' => "id,sid,SUM(number-dealnum) as num,status,type,price",
					'group' => 'price',
					'order' => 'price DESC,createtime DESC',
					'limit' => '5'
				)
			);
			$selllists = $order->find(
				array(
					'conditions' => "sid = $sid and status = 0 and type = 0",
					'columns' => "id,sid,SUM(number-dealnum) as num,status,type,price",
					'group' => 'price',
					'order' => 'price ASC,createtime DESC',
					'limit' => '5'
				)
			);
//			echo '<pre>';
//			var_dump($selllists);die;
			$buylist = [];
			foreach ($buylists as $key=>$val){
				if($val->num > 0){
					$buylist[] = [
						'price' => $val->price,
						'enum' => intval($val->num),
					];
				}
			}

			$selllist = [];
			foreach ($selllists as $key=>$val){
				if($val->num > 0){
					$selllist[] = [
						'price' => $val->price,
						'enum' => intval($val->num),
					];
				}
			}
			$data['buy'] = $buylist;
			$data['sell'] = $selllist;
			if (empty($data)){$data = '';}
			$this->ajaxResponse($data, '最新委托列表', '0');

		}
	}
	//提交表单
	public function orderAction() {
	    $tradeType = ['sell','buy'];
		//交易时间判断
		$starttime = date("H");
		$uid = $this->uid;
		if (empty($uid)) {
			$this->ajaxResponse('', '用户id不存在', '1');
		}
		$allowuser = User::findFirst(['conditions'=>"id= $uid",'columns'=>'trade_limit_level']);
		$orchard = OrchardUser::findFirst(["conditions"=>"uid = $uid",'columns'=>'grade']);
		$type = $this->request->getPost('state');
        if (!@in_array($type,$tradeType)){
            $this->ajaxResponse('error','操作失败','1');
        }
		$orchardGrade = isset($orchard->grade) ? $orchard->grade : 1;
		$levelLimit = Config::findFirst("key = 'productConfig'");
		if (!empty($levelLimit)){
			$value =  unserialize($levelLimit->value);
		}
		$sellLevel = isset($value['sellLevel']) ? $value['sellLevel'] : 4;
		$buyLevel = isset($value['buyLevel']) ? $value['buyLevel'] : 4;
		$tradeStatus = isset($value['tradeStatus']) ? $value['tradeStatus'] : 1;
		$openTime = isset($value['openTime']) ? $value['openTime'] : 9;
		$closeTime = isset($value['closeTime']) ? $value['closeTime'] : 24;
		if ($tradeStatus > 1){
			$this->ajaxResponse('error','交易市场正在升级，已暂时关闭','1');
		}
		if ($starttime<$openTime||$starttime > $closeTime){
			$this->ajaxResponse('error','果园开市时间为'.$openTime.'-'.$closeTime.'点，尚未开市请等待','1');
		}
		$trade_limit_level = !empty($allowuser->trade_limit_level) ? $allowuser->trade_limit_level : 0;
		if ($trade_limit_level > 0 ){
			if ($type == 'buy'){
				if ($orchardGrade < $buyLevel){

					$this->ajaxResponse('error','用户领取推广礼包后须农场达到'.$buyLevel.'级方可购买产品','1');
				}
			}elseif ($type == 'sell'){
				if ($orchardGrade < $sellLevel){
					$this->ajaxResponse('error','用户领取推广礼包后须农场达到'.$sellLevel.'级方可出售产品','1');
				}
			}
		}
		// 交易产品ID
		$sid = intval(trim($this->request->getPost('sid')));
		if (empty($sid)) {
			$this->ajaxResponse('', '产品id不存在', '1');
		}
		// 交易数量
		$num =  intval($this->request->getPost('num'));
		$totalNumber  = $num;
		if (empty($num)||$num <= 0) {
			$this->ajaxResponse('', '数量不正确', '1');
		}
		if ($sid == 80011){
			if ($num%10 !== 0){
				$this->ajaxResponse('error','出售需要为10的倍数','1');
			}
		}else{
			if ($num%100 !== 0){$this->ajaxResponse('error','数量应为100的整数倍','1');}
		}
		$tradeStatus = Product::findFirst("id = $sid");
		if ($tradeStatus->tradeStatus<1 ){
			$this->ajaxResponse('error','该产品暂停交易','1');
		}
		// 交易单价
		$webPrice = $this->request->getPost('price');
		$price = ($this->checkPrice(floatval($webPrice),$sid));
		if (empty($price) || $price <= 0) {
			$this->ajaxResponse('', '价格不正确', '1');
		}
		$dealprice = $price; //获取购买成交的价格
		// 交易总金额
		$money = ($price*$num);
		$sumOrderPrice = 0;
		$stopPrice = $this->getStopPrice($sid);

		$onePrice = $this->getCharge($money / $num);

//		$PriceNum = strlen("'$onePrice'");
		//		if ($stopPrice['riseStop']<0 || $stopPrice['fallStop'] < 0 ){$this->ajaxResponse('error','涨跌停价格有误','1');}
		if ($sid<100){
			if(bccomp(floatval($onePrice),floatval($stopPrice['riseStop']),5) === 1) {
				$this->ajaxResponse($stopPrice['riseStop'], '购买价格不能超过涨停价');
			} elseif (bccomp(floatval($onePrice),floatval($stopPrice['fallStop']),5)<0) {
				$this->ajaxResponse($stopPrice['fallStop'], '购买价格不能低于跌停价');
			}
		}else{

			if(bccomp(floatval($onePrice),floatval($stopPrice['riseStop']),4) === 1) {
				$this->ajaxResponse($stopPrice['riseStop'], '购买价格不能超过涨停价');
			} elseif (bccomp(floatval($onePrice),floatval($stopPrice['fallStop']),4)<0) {
				$this->ajaxResponse($stopPrice['fallStop'], '购买价格不能低于跌停价');
			}
		}

		//判断订单类型
		if ($type == 'buy') { //判断当前订单是那种类型
			$o = '0';
			$p = '1';
		}
		if ($type == 'sell') {
			$o = '1';
			$p = '0';
		}
		$productNum = UserProduct::findFirst(array('conditions' => "uid = $uid and sid = $sid", 'columns' => 'number'));
		if ($type == 'sell') {
			if ($productNum) {
				if ($productNum->number < $num) {
					$this->ajaxResponse('', '用户产品数量不足', '1');
				}
			} else {
				$this->ajaxResponse('', '用户产品数量不足', '1');
			}
		}
		$fee = $this->getRenascence($sid);
		$charge = $fee * $money;
		if ($sid == '1'){
			$charge = $this->getCharges($charge);
		}else{
			$charge = $this->getCharge($charge,'trade');
		}
		$user = new User();
		$item = $user->findFirst(
			array(
				'conditions' => "id = $uid",
				'columns' => 'coing,id'
			)
		);
		$this->coing = $item->coing;
		if ($item->coing < $money && $type != "sell") {
			$this->ajaxResponse('', '用户金币不足', '1');
		}
		$this->db->begin();//开启事物          ！！！！！！！！！！！！！！！！！！！！！！！！！
		$productNum = UserProduct::findFirst(array('conditions' => "uid = $uid and sid = $sid", 'columns' => 'number'));
		$this->clockTable('dhc_user_product',"uid = $uid AND sid = $sid");
		if ($type == 'sell') {
			if ($productNum) {
				if ($productNum->number < $num) {
					$this->db->rollback();
					$this->ajaxResponse('', '用户产品数量不足', '1');
				}
			} else {
				$this->db->rollback();
				$this->ajaxResponse('', '用户产品数量不足', '1');
			}
		}elseif ($type == 'buy'){
			$userCoing = User::findFirst("id = $uid");
			$this->clockTable('dhc_user',"id = $uid");
			if ($userCoing->coing < $money) {
				$this->db->rollback();
				$this->ajaxResponse('', '用户金币不足', '1');
			}
		}
		$startnum = $num;
		$sql = "SELECT * FROM `dhc_user` WHERE id = $uid FOR UPDATE ";
		$userInfos = $this->db->query($sql)->fetch();
		$usersCoing  = $userInfos['coing'];
			//用户金币冻结
			if ($type === 'buy') {
			    if ($userInfos['coing'] < $money){
                    $this->db->rollback();
                    $this->ajaxResponse('error','用户金币不足','1');
                }
				$userFrozen  = User::findFirst("id = $uid AND coing = ". $usersCoing);
			    if (empty($userFrozen)){
			    	$this->db->rollback();
			    	$this->ajaxResponse('error','操作失败,金币扣除失败','1');
				}
				$havingCoing = $userFrozen->coing;
				$userFrozen->Frozen += $money;
				$userFrozen->coing -= $money;
				$havingCoings =  $userFrozen->coing;
				$userFrozenResult = $userFrozen->update();
				if (!empty($userFrozenResult)){
					if ($type == 'buy'){
						$this->saveTradeLogs(array("uid" =>$uid, "num" =>$money, "type" => "dedcoing", "log" => "原有金币".$havingCoing."大盘交易系统匹配购买扣除可用金币" . $money."后为".$havingCoings));
						$this->saveTradeLogs(array("uid" =>$uid, "num" =>$money, "type" => "addfrozencoing", "log" => "大盘交易系统匹配购买增加冻结扣除金币" . $money ));
					}
				}
				if (empty($userFrozenResult)) {
					$this->db->rollback();
					$this->ajaxResponse('', '金币冻结失败', '1');
				}
			}
			if ($type === 'sell') {
				//用户产品冻结
				$sql = "SELECT * FROM dhc_user_product  WHERE uid = '{$uid}' AND sid = '{$sid}'";
				$infos = $this->db->query($sql)->fetch();
                $userProductNumbers = $infos['number'];
				$userProduct = UserProduct::findFirst("uid =$uid and sid  =$sid AND number = '{$userProductNumbers}'");
				if ($type == 'sell') {
				    if ($userProduct->number <$num){
				        $this->db->rollback();
                        $this->ajaxResponse('error','用户产品不足','1');
                    }
					$havingProduct = $userProduct->number;
					$userProduct->frozen += $num;
					$userProduct->number -= $num;
					$havingProducts = $userProduct->number;
					$userProductResult = $userProduct->update();
					if (!empty($userFrozenResult)){
						$this->saveTradeLogs(array("uid" =>$userProduct->uid, "num" =>$num, "type" => "addfrozenproduct", "log" => "原有数量".$havingProduct."大盘交易系统匹配出售产品冻结$sid-" . $num."后为".$havingProducts ));
						$this->saveTradeLogs(array("uid" =>$userProduct->uid, "num" =>$num, "type" => "dedfrozenproduct", "log" => "大盘交易系统匹配出售扣除产品$sid-" . $num ));
					}
//					$this->saveOrchardLogs(array("uid" => $uid, "mobile" => $userProduct->user, "nums" => $num, "types" => "dedproduct", "msg" => "大盘交易出售冻结产品$sid-" . $num ));
					if (!$userProductResult) {
						$this->db->rollback();
						$this->ajaxResponse('', '提交失败', '1');
					}
				}
				if (!$userFrozen) {
					$this->db->rollback();
					$this->ajaxResponse('', '提交失败', '1');
				}
			}
		if (($type == 'buy' && $userFrozenResult) || ($type == 'sell' && $userProductResult)){
            $order = new Order();
            $order->uid = $uid;
            $order->sid = $sid;
            $order->number = $num;
            $order->goods = $this->getProductName($sid);
            $order->type = $p;
            $order->status = 0;
            $order->price = $price;
            if ($type == 'sell'){
                $order->fee = $charge;
            }
            $order->createtime = TIMESTAMP;
            $re3 = $order->save();//生成用户提交订单
            if ($re3){
                $this->db->commit();
            }else{
                $this->db->rollback();
                $this->ajaxResponse('error','操作失败','1');
            }
		} else {
			$this->db->rollback();
			$this->ajaxResponse('','用户信息交易异常','1');
		}

		// 撮合交易开始
		$this->db->begin();
		if (!empty($type)) {
			//提交的购买订单
			if ($type == 'buy') {
				$resprice = $order->find(
					array(
						'conditions' => " price <= $price and type = $o and sid = $sid and status =0",
						'columns' => 'id,sid,number,price,dealnum,uid',
						'order' => 'price ASC, createtime ASC'
					)
				);
				$sql = "SELECT id,sid,number,price,dealnum,uid FROM `dhc_trade_order` WHERE  price <= $price and type = $o and sid = $sid and status =0 ORDER BY price ASC, createtime ASC FOR UPDATE ";
				$this->db->query($sql);
			} elseif ($type == 'sell'){
				$resprice = $order->find(
					array(
						'conditions' => " price >= $price and type = $o and sid =$sid and status =0",
						'columns' => 'id,sid,number,price,dealnum,uid',
						'order' => 'price DESC, createtime ASC'
					)
				);
				$sql = "SELECT id,sid,number,price,dealnum,uid FROM `dhc_trade_order` WHERE  price >= $price and type = $o and sid =$sid and status =0 ORDER BY price DESC, createtime ASC FOR UPDATE ";
				$this->db->query($sql);
			} else {
				$this->ajaxResponse('', '未知的操作', '1');
			}
			$order = array();
			foreach ($resprice as $k => $v) {
				$order[] = [
					'id' => $v->id,
					'uid'=>$v->uid,
					'sid' => $v->sid,
					'number' => $v->number,
					'price' => $v->price,
					'dealnum' => $v->dealnum
				];
			}
			$surplusMoney = $money;//总的金币
			$lnum = 0;
			$Xmoney = 0; //应该花的金币
			for ($i = 0; $i < count($order); $i++) {
				$fnum = ($order[$i]['number'] - $order[$i]['dealnum']);
				if ($fnum <= 0 || $num <=0) {
					continue;
				}
				$id = $order[$i]['id'];
				//获取挂单用户的id
				$ordersinfo = Order::findFirst("id = $id");
				$ordersUser = $ordersinfo->uid;
				if ($num >= $fnum) {//如果提交数量大于找到的订单余量
					$lnum += $fnum;
					$num -= $fnum;
					$buser = new Order();
					$result1 = $buser->findFirst("id = $id");
					$sum = $result1->number;
					$nums = ($result1->number-$result1->dealnum);
					$result1Number = intval($result1->number);
					$result1Dealnum = intval($result1->dealnum);
					$result1->dealnum = $sum;
					$orderPrice = (($result1Number-$result1Dealnum)*$result1->price);
					$orderType = $result1->type;
					$result1->bid = $uid;
					$result1->endtime = TIMESTAMP;
					$result1->status = '1';
					//下面对挂单用户进行数据更新 1买 0卖
					$user = new User();
					$userInfo = $user->findFirst("id =$result1->uid");
					//1买 0卖
					if ($orderType === '0') {
						if ($price>$result1->price){
							$dealprice = $result1->price;
						}else{
							$dealprice = $price;
						}
						$sumprice = ($fnum * $dealprice);
//						$userInfo->Frozen -= $orderPrice;
						$havingCoing = $userInfo->coing;
						$userInfo->coing += ($sumprice-$fee*$sumprice);
						$havingCoings = $userInfo->coing;
						$Xmoney += $sumprice;
						$surplusMoney -= $orderPrice;
						$selluserInfoResult = $userInfo->update();
						// 返佣类型: 兑换钻石=1 大盘手续费=2
						if (USER_TYPE == 'duojin' && $sid != 1){
							$this->distribution(array('uid'=>$result1->uid,'num'=>$fee*$sumprice,'type'=>2,'remark'=>'挂单出售手续费'));
						}
						if (USER_TYPE != 'duojin'){
							$this->distribution(array('uid'=>$result1->uid,'num'=>$fee*$sumprice,'type'=>2,'remark'=>'挂单出售手续费'));
						}
						$buyUserInfo = User::findFirst("id = $uid ");
						$buyUserInfo->Frozen -= $orderPrice;
						$flag = $buyUserInfo->update();
						$this->saveTradeLogs(array("uid" =>$userInfo->id, "num" =>($sumprice-$fee*$sumprice), "type" => "addcoing", "log" => "原有金币".$havingCoing."大盘交易系统匹配卖出增加金币" . ($sumprice-$fee*$sumprice)."后为".$havingCoings ));
						$this->saveTradeLogs(array("uid" =>$uid, "num" =>$sumprice, "type" => "dedfrozencoing", "log" => "大盘交易系统匹配购买扣除冻结金币" . $sumprice ));
					} elseif ($orderType === '1') {
						if ($price<$result1->price){
							$dealprice = $result1->price;
						}else{
							$dealprice = $price;
						}
						$sumprice = ($fnum * $dealprice);

						$userInfo->Frozen -= $sumprice;

						//$userInfo->Frozen -= $sumprice;////////////!!!!!!!
						$this->saveTradeLogs(array("uid" =>$result1->uid, "num" =>$sumprice, "type" => "dedfrozencoing", "log" => "大盘交易系统匹配购买扣除冻结金币" . $sumprice ));
						$selluserInfoResult = $userInfo->update();
						$userBuy = User::findFirst("id = $uid");
						if (USER_TYPE == 'duojin' && $sid != 1){
							$this->distribution(array('uid'=>$result1->uid,'num'=>$fee*$sumprice,'type'=>2,'remark'=>'挂单出售手续费'));
						}
						if (USER_TYPE != 'duojin'){
							$this->distribution(array('uid'=>$result1->uid,'num'=>$fee*$sumprice,'type'=>2,'remark'=>'挂单出售手续费'));
						}
						$havingCoing =$userBuy->coing;
						$userBuy->coing += ($orderPrice-$fee*$sumprice);
						$havingCoings =$userBuy->coing;
						$flag =$userBuy->update();
						$this->saveTradeLogs(array("uid" =>$uid, "num" =>($sumprice-$fee*$sumprice), "type" => "addcoing", "log" => "原有金币".$havingCoing."大盘交易系统匹配卖出增加金币" .($sumprice-$fee*$sumprice)."后为".$havingCoings ));
					}

					$surplusMoney -= $sumprice;
					//根据订单类型对用户金币进行更新1买 0卖
					if ($flag){
						$userCost = new UserCost();
						$userCost->uid = $userInfo->id;
						$userCost->orderNumber = 'XF'.date("YmdHis",TIMESTAMP).TIMESTAMP.$userInfo->id;
						$userCost->createtime = TIMESTAMP;
						if ($orderType === '0'){
							if (USER_TYPE == 'huangjin'){
								$userCost->sum = +($sumprice);
							}else{
								$userCost->sum = +($sumprice-$fee*$sumprice);
							}
							$userCost->charge = $fee*$sumprice;
							$userCost->type = '出售产品';
							$this->saveTrade(array('uid'=>$uid,'num'=>$sumprice,'fee'=>0,'type'=>'购买产品'));
						}
						if ($orderType === '1'){
							$userCost->sum = -$sumprice;
							$userCost->type = '购买产品';
							if (USER_TYPE == 'huangjin'){
								$this->saveTrade(array('uid'=>$uid,'num'=>$sumprice,'fee'=>$result1->fee,'type'=>'出售产品'));
							}else{
								$this->saveTrade(array('uid'=>$uid,'num'=>$sumprice-$result1->fee,'fee'=>$result1->fee,'type'=>'出售产品'));
							}

						}
						$userCost->endtime = TIMESTAMP;
						$userCost->status = 1;
						$userCostResult = $userCost->save();
						if (!$userCostResult){
							foreach ($userCost->getMessages() as $message){
								echo $message;
							}die;
						}
					}
					$result1->dealprice = $dealprice;
//					$result1->dealMoney = $sumprice;
					$re1 = $result1->update();//如果提交数量大于数据库数量进行更新
					$userProduct = UserProduct::findFirst(
						array(
							'conditions'	=>"uid = $result1->uid and sid = $sid"
						)
					);//判断当前订单是那种类型
					if ($orderType ==='0'){//如果当前订单是卖
						if ($userProduct){
							$userProduct->frozen -= $nums;
							$this->saveTradeLogs(array("uid" =>$userProduct->uid, "num" =>$nums, "type" => "dedfrozenproduct", "log" => "大盘交易扣除冻结产品$sid.-" .$nums ));
							$buyUserResult  =$userProduct->update();
							$userMyProduct = UserProduct::findFirst("uid = $uid and sid =$sid"); //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
							if ($userMyProduct){
								$havingProduct = $userMyProduct->number;
								$userMyProduct->number += $nums;
								$havingProducts = $userMyProduct->number;
								$this->saveTradeLogs(array("uid" =>$userMyProduct->uid, "num" =>$nums, "type" => "addproduct", "log" => "原有产品".$havingProduct."大盘交易增加产品$sid.-" .$nums."后为".$havingProducts ));
								$userMyProduct->updatetime = TIMESTAMP;
								$userMyProductResult = $userMyProduct->update();
							}else{
								$userProducts = new UserProduct();
								$userProducts ->uid = $uid;
								$userProducts->sid = $sid;
								$userProducts->number = $nums;
								$userProducts->createtime = TIMESTAMP;
								$userMyProductResult = $userProducts->save();
								$this->saveTradeLogs(array("uid" =>$uid, "num" =>$nums, "type" => "addproduct", "log" => "原有产品0大盘交易增加产品$sid.-" .$nums."后为".$nums ));
							}
						}else{
							$buyUser = new UserProduct();
							$buyUser->uid =$result1->uid;
							$buyUser->sid =$sid;
							$buyUser->number = $nums;
							$buyUser->createtime = TIMESTAMP;
							$buyUserResult  = $buyUser->save();
							$this->saveTradeLogs(array("uid" =>$result1->uid, "num" =>$nums, "type" => "addproduct", "log" => "原有产品0大盘交易增加产品$sid.-" .$nums."后为".$nums ));
						}//1买 0卖
					}elseif($orderType === '1'){//如果当前订单是买
						if($nums>0){
							if (!$userProduct){
								$userProduct = new  UserProduct();
								$userProduct->sid = $sid;
								$userProduct->uid = $result1->uid;
								$userProduct->number = $nums;
								$userProduct->createtime = TIMESTAMP;
								$selluserInfo = 	$userProduct->save();
								$this->saveTradeLogs(array("uid" =>$uid, "num" =>$nums, "type" => "addproduct", "log" => "原有产品0大盘交易增加产品$sid.-" .$nums."后为".$nums ));
							}else{
								$userProduct->updatetime = TIMESTAMP;
								$havingProduct = $userProduct->number;
								$userProduct->number += $nums;
								$havingProducts = $nums;
								$selluserInfo = $userProduct->update();
								$this->saveTradeLogs(array("uid" =>$userProduct->uid, "num" =>$nums, "type" => "addproduct", "log" => "原有产品".$havingProduct."大盘交易增加产品$sid.-" .$nums."后为".$havingProducts ));
							}
							$userFrozenProduct = UserProduct::findFirst("uid = $uid and sid =$sid");
							$userFrozenProduct->frozen -= $nums;
							$selluserInfo = $userFrozenProduct->update();
							$this->saveTradeLogs(array("uid" =>$uid, "num" =>$nums, "type" => "dedfrozenproduct", "log" => "大盘交易冻结产品扣除$sid.-" .$nums ));

						}
						//当前用户为买 把当前冻结的金币扣除
				}
					if ($selluserInfoResult&&$re1&&@$buyUserResult&&$flag||$selluserInfoResult&&$re1&&@$buyUserResult&&$userMyProductResult&&$flag||$selluserInfo&&$selluserInfoResult&&$flag){
						$flag2 = true;
					}
				} elseif ($num < $fnum) {//如果提交数量小于订单中的剩余数量
					$lnum += $num;
					$buser = new Order();
					$result2 = $buser->findFirst("id = $id");
					$result2->dealnum = ($result2->dealnum + $num);
					$orderType = $result2->type;
					$result1Number = intval($result2->number);
					$result1Dealnum = intval($result2->dealnum);
					$orderPrice = (($result1Number-$result1Dealnum)*$result2->price);
					$sumOrderPrice += $orderPrice;
					$user = new User();
					$userInfo = $user->findFirst("id =$result2->uid");
					//0 卖 1 买!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
					if ($orderType === '0') {
						if ($price > $result2->price){
							$dealprice = $result2->price;
						}else{
							$dealprice = $price;
						}
						$sumprice = ($num * $dealprice);
						$Xmoney += $sumprice;
						$surplusMoney -= $orderPrice;
						$havingCoing = $userInfo->coing;
						$userInfo->coing +=($sumprice-$fee*$sumprice);
						$havingCoings = $userInfo->coing;
						if (USER_TYPE == 'duojin' && $sid != 1){
							$this->distribution(array('uid'=>$result2->uid,'num'=>$fee*$sumprice,'type'=>2,'remark'=>'挂单出售手续费'));
						}
						if (USER_TYPE != 'duojin'){
							$this->distribution(array('uid'=>$result2->uid,'num'=>$fee*$sumprice,'type'=>2,'remark'=>'挂单出售手续费'));
						}
						$flag = $userInfo->update();
						$buyUserInfo = User::findFirst("id = $uid ");
						$buyUserInfo->Frozen -= $sumprice;

						$buyUserInfoResult  = $buyUserInfo->update();
						$this->saveTradeLogs(array("uid" =>$uid, "num" =>$sumprice, "type" => "dedfrozencoing", "log" => "大盘交易扣除冻结金币" .$sumprice ));
						$this->saveTradeLogs(array("uid" =>$userInfo->id, "num" =>($sumprice-$fee*$sumprice), "type" => "addcoing", "log" => "原有金币".$havingCoing."大盘交易系统匹配卖出增加金币" .($sumprice-$fee*$sumprice)."后为".$havingCoings ));
//						$this->saveOrchardLogs(array("uid"=>$userInfo->id,"mobile"=>$userInfo->user,"nums"=>($money-$fee*$money),"types"=>"addcoing","msg"=>"大盘交易系统匹配卖出增加金币".($orderPrice-$result2->fee)));
//						$this->saveOrchardLogs(array("uid"=>$uid,"mobile"=>$buyUserInfo->user,"nums"=>$money,"types"=>"dedcoing","msg"=>"大盘交易系统匹配购买扣除冻结金币".$orderPrice));
					} elseif ($orderType === '1') {
						if ($price<$result2->price){
							$dealprice = $result2->price;
						}else{
							$dealprice = $price;
						}
						$sumprice = ($num * $dealprice);
						$userInfo->Frozen -= $sumprice;
						$flag = $userInfo->update();
						$userBuy = User::findFirst("id = $uid");
						$havingCoing = $userBuy->coing;
						$userBuy->coing += ($sumprice-$fee*$sumprice);
						$havingCoings = $userBuy->coing;
						//var_dump($fee*$orderPrice,$charge,$money,$num);die;
						if (USER_TYPE == 'duojin' && $sid != 1){
							$this->distribution(array('uid'=>$result2->uid,'num'=>$fee*$sumprice,'type'=>2,'remark'=>'挂单出售手续费'));
						}
						if (USER_TYPE != 'duojin'){
							$this->distribution(array('uid'=>$result2->uid,'num'=>$fee*$sumprice,'type'=>2,'remark'=>'挂单出售手续费'));
						}
						$userBuyResult = $userBuy->update();
						$this->saveTradeLogs(array("uid" =>$userInfo->id, "num" =>$sumprice, "type" => "dedfrozencoing", "log" => "大盘交易系统匹配买入扣除冻结金币" .$sumprice ));
						$this->saveTradeLogs(array("uid" =>$uid, "num" =>($sumprice-$fee*$sumprice), "type" => "addcoing", "log" => "原有金币".$havingCoing."大盘交易系统匹配出售增加金币" .($sumprice-$fee*$sumprice)."后为".$havingCoings ));
					}

					if ($flag){
						$userCost = new UserCost();
						$userCost->uid = $userInfo->id;
						$userCost->orderNumber = 'XF'.date("YmdHis",TIMESTAMP).TIMESTAMP.$userInfo->id;
						$userCost->createtime = TIMESTAMP;
						if ($orderType === '0'){
							if (USER_TYPE == 'huangjin'){
								$userCost->sum = +($sumprice);
							}else{
								$userCost->sum = +($sumprice-$fee*$sumprice);
							}
							$userCost->charge = $fee*$sumprice;
							$userCost->type = '出售产品';
							$this->saveTrade(array('uid'=>$uid,'num'=>($sumprice-$fee*$sumprice),'fee'=>0,'type'=>'购买产品'));
						}
						if ($orderType === '1'){
							$userCost->sum = -$sumprice;
							$userCost->type = '购买产品';
							if (USER_TYPE =='huangjin'){
								$this->saveTrade(array('uid'=>$uid,'num'=>($sumprice),'fee'=>$fee,'type'=>'出售产品'));
							}else{
								$this->saveTrade(array('uid'=>$uid,'num'=>($sumprice-$fee*$sumprice),'fee'=>$fee,'type'=>'出售产品'));
							}
						}
						$userCost->endtime = TIMESTAMP;
						$userCost->status = 1;
						$userCostResult = $userCost->save();
						if (!$userCostResult){
							foreach ($userCost->getMessages() as $message){
								echo $message;
							}die;
						}
					}
					$surplusMoney -= $sumprice;
					$result2->dealprice = $dealprice;
//					$result2->dealMoney = $sumprice;
					$re2 = $result2->update();//如果提交数量小于数据库数量进行更新
					$userProduct = UserProduct::findFirst("uid = $result2->uid AND sid = $sid");
					//0 卖 1 买!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
					if ($orderType ==='0'){//根据当前提交订单类型对数据库中成交订单产品数量进行更新
						if ($userProduct){
							$havingFrozenProduct = $userProduct->frozen;
							$userProduct->frozen -=$num;
							$havingFrozenProducts = $userProduct->frozen;
							$userProductResult= $userProduct->update();
//							$userProduct->number +=$num;
							$this->saveTradeLogs(array("uid" =>$userProduct->uid, "num" =>$num, "type" => "dedfrozenproduct", "log" => "原有冻结产品".$havingFrozenProduct."大盘交易系统匹配扣除冻结产品$sid-" .$num."后为".$havingFrozenProducts ));
							$userProductInfo = UserProduct::findFirst("uid = $uid and sid = $sid");
							if ($userProductInfo){
								$havingProduct = $userProductInfo->number;
								$userProductInfo->number += $num;
								$havingProducts = $userProductInfo->number;
								$this->saveTradeLogs(array("uid" =>$uid, "num" =>$num, "type" => "addproduct", "log" => "原有产品".$havingProduct."大盘交易系统匹配增加产品$sid-" .$num."后为".$havingProducts ));
								$userProductInfo->updatatime = TIMESTAMP;
								$userProductInfoResult = $userProductInfo->update();
							}else{
								$userProductInfo = new UserProduct();
								$userProductInfo->uid = $uid;
								$userProductInfo->sid = $sid;
								$userProductInfo->number = $num;
								$userProductInfo->createtime = TIMESTAMP;
								$this->saveTradeLogs(array("uid" =>$uid, "num" =>$num, "type" => "addproduct", "log" => "原有产品0大盘交易系统匹配增加产品$sid-" .$num."后为".$num ));
								$userProductInfoResult = $userProductInfo->save();
							}
						}
						//$userProduct->frozen -=$num;
					}elseif ($orderType ==='1'){
						if ($userProduct){
							if($num>0){
								$havingProduct = $userProduct->number;
								$userProduct->number += $num;
								$havingProducts = $userProduct->number;
								$this->saveTradeLogs(array("uid" =>$userProduct->uid, "num" =>$num, "type" => "addproduct", "log" => "原有产品".$havingProduct."大盘交易系统匹配增加产品$sid-" .$num."后为".$havingProducts ));
								$userProductInfoResult = $userProduct->update();
							}
						}else{
							$userProductInfo = new  UserProduct();
							$userProductInfo->uid = $result2->uid;
							$userProductInfo->sid = $sid;
							$userProductInfo->number = $num;
							$userProductInfoResult = $userProductInfo->save();
							$this->saveTradeLogs(array("uid" =>$result2->uid, "num" =>$num, "type" => "addproduct", "log" => "原有产品0大盘交易系统匹配增加产品$sid-" .$num."后为".$num ));
						}
						$userProduct = UserProduct::findFirst("uid = $uid AND sid = $sid");
						$havingFrozenProduct  = $userProduct->frozen;
						$userProduct->frozen -= $num;
						$havingFrozenProducts = $userProduct->frozen;
						$userProduct->update();
						$this->saveTradeLogs(array("uid" =>$uid, "num" =>$num, "type" => "dedfrozenproduct", "log" => "原有冻结产品".$havingFrozenProduct."大盘交易系统匹配扣除冻结产品$sid-" .$num."后为".$havingFrozenProducts ));

					}
//					var_dump($Xmoney);
					//如果单次成交 可能有些变量不存在会报错 屏蔽
					if ($flag&&@$buyUserInfoResult&&$re2&&$userProductResult||$flag&&$userBuyResult&&$re2&&$userProductInfoResult){
						$flag3 = true;
					}else{
						$flag3 = false;
					}
					$num = 0;
				}
				if ($num <= 0) {
					break;
				}
			}
			$order = new Order();
			$result4 = $order->find(
				array(
					'conditions' => "uid = $uid ",
					'order' => 'createtime ASC',
					'limit' => 1
				)
			);
			foreach ($result4 as $v) {
				$id = $v->id;
			}
			//更新用户id
			$order = new Order();
			$lastorder = $order->findFirst(
				array(
					'conditions' => "sid = $sid and uid = $uid",
					'order' => 'createtime DESC',
					'limit' => 1
				)
			);
			$oid = $lastorder->id;
			$result5 = $order->findFirst("id = $oid");
			if ($num == 0) {
				$result5->dealnum = $startnum;
			} else {
				$result5->dealnum = ($result5->number - $num);
			}
//			$result5->dealMoney = $Xmoney;
			if ($result5->number == $result5->dealnum){
				$result5->status = 1;
				$result5->bid = $ordersUser;
				$result5->endtime = TIMESTAMP;
			}
			if (!empty($result5->dealnum)) {
				$result5->bid = $ordersUser;
			}
			$result5->dealprice = $dealprice;
			$result6 = $result5->update();
//			var_dump($surplusMoney,$money);die;
			if ($type === 'buy'&&(@$flag2||@$flag3)){
				$userOrder = Order::findFirst(
					array(
						'conditions'	=> "id = $oid",
						'order'		=> 'createtime DESC',
						'limit'		=> '1'
					)
				);
				$users = User::findFirst("id = $uid");
				if ($userOrder&&$num>=0) {
					$returnnum = ($totalNumber - $num) * $price - $Xmoney;
					if ($returnnum > 0) {
						$users->coing += $returnnum;
						$users->Frozen -= $returnnum;
						$usersResult = $users->update();
						if (!empty($usersResult)) {
							$this->saveTradeLogs(array("uid" => $uid, "num" => $returnnum, "type" => "addcoing", "log" => "大盘交易系统提交和交易订单价格不同返还" . $returnnum));
						}
					}
				} elseif($surplusMoney!=$sumprice){
					$costMoney = ($money-$surplusMoney - $Xmoney);
					$users -> coing += $costMoney;
					$users ->Frozen -= $costMoney;
					$usersResult = $users ->update();
					if (!empty($usersResult)){
						$this->saveTradeLogs(array("uid" =>$uid, "num" =>$costMoney, "type" => "addcoing", "log" => "大盘交易系统提交和交易订单价格不同返还" .$costMoney ));
					}
				}
			}
			//大盘日数据
			$d = strtotime(date('Y-m-d'));
			$dailydata = new Dailydate();
			$today = $dailydata->findFirst("Date = $d and sid =$sid");
			$dailydata = new Dailydate();
			$yestoday = $dailydata->findFirst(
				array(
					'conditions' =>"Date <$d and sid =$sid",
					'order'		=>'Date DESC',
					'limit'		=>1
				)
			);
			if (@$flag3||@$flag2){
				$volumn = $lnum;
				$closePrice = $dealprice;
				if ($yestoday) {
					$yClosingPrice = $yestoday->ClosingPrice;
				} else {
					$orderinfo = new Order();
					$orderinfos = $orderinfo->findFirst(
						array(
							'conditions' => "status =1 and sid =$sid",
							'columns' => 'price',
							'order' => 'endtime DESC',
							'limit' => '1'
						)
					);
					if ($orderinfos) {
						$yClosingPrice = $orderinfos->price;
					} else {
						$product = Product::findFirst("id = $sid");
						$yClosingPrice = $product->startprice;
					}
				}

				if (!$today) {
					$today = new Dailydate();
					$today->sid = $sid;
					$today->Date = $d;
					$today->OpeningPrice = $yClosingPrice;
					$today->ClosingPrice = $yClosingPrice;
					@$today->Volume += max($volumn,0);
					$today->HighestPrice = $dealprice;
					$today->LowestPrice  = $dealprice;
					$res = $today->save();
				} else {
					if ($dealprice > $today->HighestPrice) {
						$today->HighestPrice = $dealprice;
					}
					if ($dealprice < $today->LowestPrice) {
						$today->LowestPrice = $dealprice;
					};
//				$orderInfoPrice = Order::Findfirst(
//				    array(
//				        'conditions'=>"sid = $sid AND status = 1 AND type = 1",
//                        'columns'=>'price',
//                        'order' =>'endtime DESC',
//                        'limit' =>'1'
//                    )
//                );
//                if($orderInfoPrice){
//				}
					if (isset($closePrice)){
						$today->ClosingPrice = $closePrice;
					}
					$today->Volume += max(@$volumn,0);
					$res = $today->update();
				}
				if (!$res) {
					foreach ($today->getMessages() as $message) {
						echo $message;
					}
					die;
				}
			}
			//大盘日数据结束
			if (($type == 'buy' && $userFrozenResult) || ($type == 'sell' && $userProductResult)){
				$this->db->commit();
				$this->ajaxResponse('success', '订单提交成功', '0');
			}else{
				$this->db->rollback();
				$this->ajaxResponse('error', '订单提交异常1', '1');
			};
		} else {
			$this->db->rollback();
			$this->ajaxResponse('error', '交易类型不存在', '1');
		}

	}

	public function getpriceAction($uid, $productId) {
		$sid = $productId;
		if (empty($sid)) {
			$this->ajaxResponse('', '产品id不存在', '1');
		};
		$order = new Order();
		//获取最新购买价格
		$buyPrice = $order->find(
			array(
				'columns' => "sid,type,price as buyprice",
				'conditions' => "sid = $sid and  type = 1 and dealnum > 0",
				'order' => 'endtime DESC',
				'limit' => '1'
			)
		);
		foreach ($buyPrice as $k => $v) {
			if ($v->sid == $sid) {
				if (!isset($v->buyprice)) {
					$data['buyprice'] = $v->buyprice;
				} else {
					$data['buyprice'] = '0.00000';
				}
			}
		}
		//获取最新出售价格
		$sellPrice = $order->find(
			array(
				'columns' => "sid,type,price as sellprice",
				'conditions' => "sid = $sid and  type =0  and dealnum > 0",
				'order' => 'endtime DESC',
				'limit' => '1'
			)
		);
		//产品id
		$data['sid'] = $sid;
		foreach ($sellPrice as $v) {
			if ($v->sid == $sid) {
				if (!empty($v->sellprice))
					$data['sellprice'] = $v->sellprice;
			} else {
				$data['sellprice'] = '0.00000';
			}
		}
		//用户信息/查询用户余额 和金币数量
		$user = new User();
		$userInfo = $user->find(
			array(
				'conditions' => "id = $uid",
				'columns' => 'coing as gold,balance as num '
			)
		);
		foreach ($userInfo as $v) {
			$data['num'] = $v->num;
			if (empty($v->gold)) {
				$v->gold = '0.0000';
			}
			$data['gold'] = $v->gold;
		}

		$product = new Product();
		//产品手续费
		$proInfo = $product->find(
			array(
				'conditions' => "id = $sid",
				'columns' => 'poundage,id'
			)
		);
		foreach ($proInfo as $v) {
			$data['fee'] = $v->poundage;
		}
		if (!isset($data['sellprice'])) {
			$data['sellprice'] = '0.00000';
		} elseif (!isset($data['buyprice'])) {
			$data['buyprice'] = '0.00000';
		}
		if (!empty($data)) {
			return $data;
		} else {
			return false;
		}
	}
	//获取产品列表
	public function productlistAction()
	{
//		$data[] = array(
//			'rise' => 0.0003,
//			'fall' => 0.01583,
//			'low' => 0.01613,
//			'top' => 0.02011,
//			'price' => 0.01643,
//			'num' => 2734300,
//			'id' => 2
//		);
//		$this->ajaxResponse($data, '产品列表', '0');
		$sid = intval(trim($this->request->getPost('sid')));
		$productlist = new Product();
		$prolist = $productlist->find(
			array(
				'conditions'=>'tradeStatus>0',
				'order'=>'displayorder DESC'
			)
		);
		foreach ($prolist as $key => $value) {
			$arr[] = array($value->id);
		}
		$data = array();
		foreach ($arr as $v) {
			$data[] = $this->getproductlist($v[0]);
		}
		foreach ($prolist as $key => $value) {
			foreach ($data as $k => $v) {
				if ($v['marketinfo']['sid'] == $value->id) {
					$value->price = $v['marketinfo']['buyprice'];
					//如果昨日没有该产品交易信息 今日无法生成开盘价格使开盘价报错
					@$value->OpeningPrice = $v['marketinfo']['nowOpen'];
					$value->HighestPrice = $v['marketinfo']['HighestPrice'];
					$value->LowestPrice = $v['marketinfo']['LowestPrice'];
					$value->limitup = @$v['marketinfo']['riseStop'];
					$value->limitdown = @$v['marketinfo']['fallStop'];
					$value->Volume = $v['marketinfo']['successNum'];
					$v['marketinfo']['thumb'] = $value->thumb;
					$products[] = $v;
				}
			}

		}
		$this->ajaxResponse($products, '产品最新信息', '0');

	}

	public function getEntrustAction()
	{
		$productId = $this->request->get('sid');
		$id = intval(trim($this->request->get('id')));
		$entrust = trim($this->request->get('Entrust '));
		$uid = $this->uid;
		$op = $this->request->get('op');
		if ($op == 'rest') {
			$this->db->begin();
			$order = new Order();
			$item = $order->findFirst("id = $id");
            $this->clockTable('dhc_trade_order',"id = $id");
			$orderPrice = ($item->price*($item->number - $item->dealnum));
			if ($item->status == '1'){
			    $this->db->rollback();
                $this->ajaxResponse('error','该订单已撤销或者已成交','1');
			}
			$item->status = '1';
			$item->endtime = TIMESTAMP;
			//如果撤销的订单为卖的订单 撤销订单为买的订单 直接撤销不需要再进行数据返回
			$userId = $item->uid;
			$productId  = $item->sid;
			$type = $item->type;
			$userFee = $this->getRenascence($productId) * ($item->number - $item->dealnum);
			$userNum = $item->number;
			$result = $item->update();
			if (!empty($userId)){//如果撤销的订单为买的订单 对订单提交的用户进行个人金币余额返还
				$user =User::findFirst("id = $userId");
                $this->clockTable('dhc_user',"id = $userId");
				$userProduct = UserProduct::findFirst("uid = $userId and sid  =$productId");
                $this->clockTable('dhc_user_product',"uid = $userId and sid  =$productId");
				//1买 0卖
				if ($type === '0'){
					if ($userProduct) {
					    if ($userProduct->frozen <($userNum - $item->dealnum)){
					        $this->db->rollback();
                            $this->ajaxResponse('error','操作失败，交易异常','1');
                        }
						$userProduct->number += ($userNum - $item->dealnum);
						$userProduct->frozen -= ($userNum - $item->dealnum);
					}else{
						$userProduct = new UserProduct();
						$userProduct->uid = $uid;
						$userProduct->sid = $productId;
						$userProduct->number = $userNum;
					}
					$userProductResult = $userProduct->update();
					if (!empty($userProductResult)){
						$this->saveTradeLogs(["uid"=>$userId,"num"=>($userNum - $item->dealnum),"log"=>'撤销订单退回产品增加'.($userNum - $item->dealnum),"type"=>'addproduct']);
						$this->saveTradeLogs(["uid"=>$userId,"num"=>($userNum - $item->dealnum),"log"=>'撤销订单退回扣除冻结产品'.($userNum - $item->dealnum),"type"=>'dedfrozenproduct']);
					}
				}elseif ($type === '1'){
					$user->Frozen -= $orderPrice;
					$user->coing += $orderPrice;
					$userInfo = $user->update();
					if (!empty($userInfo)){
						$this->saveTradeLogs(["uid"=>$userId,"num"=>($orderPrice),"log"=>'撤销订单退回金币增加'.($orderPrice),"type"=>'addcoing']);
						$this->saveTradeLogs(["uid"=>$userId,"num"=>($orderPrice),"log"=>'撤销订单退回扣除冻结金币'.($orderPrice),"type"=>'dedfrozencoing']);
					}
				}
				if ($result&&@$userInfo||$userProductResult){//判断如果订单为卖的订单 进行判断然后提交
					$this->db->commit();
					$this->ajaxResponse('success', '撤销成功', '0');
				}else{
					$this->db->rollback();
					$this->ajaxResponse('error','撤销失败','1');
				}
			}
		}
//		$uid =intval(trim($this->request->getPost('uid')));
		$order = new Order();
		$orderlist = $order->find(
			array(
				'conditions' => "uid = $uid and sid = $productId",
				'order'		 =>	"createtime DESC",
			)
		);
		if (empty($orderlist)){$this->ajaxResponse('','暂无订单列表','1');}
		foreach ($orderlist as $value) {
			$data[] = array(
				'id' => $value->id,
				'data' => $value->createtime,
				'goods' => $value->goods,
				'type' => $value->type,
				'price' => $value->price,
				'number' => $value->number,
				'dealnum' => $value->dealnum,
				'status' => $value->status,
				'dealprice' => $value->dealprice,
				'uid' => $value->uid,
			);
		}
		if (!empty($data)){
			foreach ($data as $k => $v) {
				if ($v['status'] === '1') {
					$oldlist[] = array(
						'id' => $v['id'],
						'createtime' => $v['data'],
						'goods' => $v['goods'],
						'type' => $v['type'],
						'price' => $v['price'],
						'number' => $v['number'],
						'dealnum' => $v['dealnum'],
						'dealprice' => $v['dealprice'],
						'status' => $v['status'],
						'uid' => $v['uid'],
					);
				}elseif($v['status'] === '0'){
					$newlist[] = array(
						'id' => $v['id'],
						'data' => $v['data'],
						'goods' => $v['goods'],
						'type' => $v['type'],
						'price' => $v['price'],
						'number' => $v['number'],
						'dealnum' => $v['dealnum'],
						'dealprice' => $v['dealprice'],
						'status' => $v['status'],
						'uid' => $v['uid'],
					);
				}
			}
		}
		if (empty($oldlist)) {
			$newdata['oldlist'] = '';
		} else {
			$newdata['oldlist'] = $oldlist;
		}
		if (empty($newlist)) {
			$newdata['newlist'] = '';
		} else {
			$newdata['newlist'] = $newlist;
		}
		if (!empty($newdata)) {
			$this->ajaxResponse($newdata, '委托列表', '0');
		} else {
			$this->ajaxResponse('error', '未查询到委托', '1');
		}
	}

	//获取市场信息

	/**
	 *
	 */
	public function getMakertinfoAction()

	{

		$uid = $this->uid;
//		$this->RevokeOrderAction();

		if (empty($uid)) {
			$this->ajaxResponse('', '用户id为空', '1');
		}
		$productid = intval(trim($this->request->getPost('sid')));
		if (empty($productid)) {
			$this->ajaxResponse('', '产品id不能为空', '');
		}
		$today = new Dailydate();
		$td = strtotime(date("Y-m-d"));
		$todayinfo = $today->findFirst("sid = $productid and Date >= $td AND Volume>0");
		if ($todayinfo) {
			$data['marketinfo'] = array(
				'sid' => $todayinfo->sid,
				'nowOpen' => $todayinfo->OpeningPrice,
				'HighestPrice' => $todayinfo->HighestPrice,
				'LowestPrice' => $todayinfo->LowestPrice,
				'successNum' => $todayinfo->Volume,
			);
			$product = new Product();
			$productinfo = $product->findFirst(
				array(
					'conditions' => "status = 1 and id= $productid",
					'columns' => 'rise,fall,id,title,startprice'
				)
			);
			$dailydata = new Dailydate();
			$orderinfo = $dailydata->findFirst(
				array(
					'conditions' => "Date < $td and sid =$productid",
					'columns' => 'ClosingPrice',
					'order' => "Date DESC",
					'limit' => 1
				)
			);
			$closingprice = @$orderinfo->ClosingPrice;
			if (!$orderinfo) {
				$closingprice = $productinfo->startprice;
			}
			if ($productinfo->id == $data['marketinfo']['sid']) {
				if (USER_TYPE == 'duojin' && $productid == 1) {
					$data['marketinfo']['riseStop'] = $this->getCharge($closingprice + $productinfo->rise,'seed');
					$data['marketinfo']['fallStop'] = $this->getCharge($closingprice - $productinfo->fall,'seed');
				} else {
					$data['marketinfo']['riseStop'] = $this->getCharge($closingprice * (1 + $productinfo->rise),'rise');
					$data['marketinfo']['fallStop'] = $this->getCharge($closingprice * (1 - $productinfo->fall),'fall');
				}
//				$data['marketinfo']['riseStop'] = $closingprice * ($productinfo->rise + 1);
//				$data['marketinfo']['fallStop'] = $closingprice * (1 - $productinfo->fall);
				$data['marketinfo']['title'] = $productinfo->title;
			}
//			$order = new Order();
//			$currentprice = $order->findFirst(
//				array(
//					'conditions' => "status = 1 and sid = $productid AND dealnum>0",
//					'columns' => 'sid,price,dealprice',
//					'order' => 'endtime DESC',
//				)
//			);
			if ($todayinfo->ClosingPrice) {
				$data['marketinfo']['buyprice'] = $todayinfo->ClosingPrice;
			} else {
				$data['marketinfo']['buyprice'] = $this->getProductPrice($productid);
			}
		} else {
			$neworder = new Order();//获取订单表中的最新价格
			$price = $neworder->findFirst(
				array(
					'conditions' => "sid = $productid and status = 1 AND dealnum>0 ",
					'columns' => 'dealprice',
					'order' => 'endtime DESC',
					'limit' => '1'
				)
			);
			if ($price) {
				$data['marketinfo']['buyprice'] = $price->dealprice;
			} else {
				$data['marketinfo']['buyprice'] = $this->getProductPrice($productid);
			}
			$dailydata = new Dailydate();//获取昨日日数据中的最高最低价和数量还有关市价格
			$dailydatas = $dailydata->findFirst(
				array(
					'conditions' => "sid =$productid and Date <= $td",
					'columns' => 'HighestPrice,LowestPrice,Volume as successNum,ClosingPrice,sid',
					'order' => 'Date DESC',
					'limit' => 1
				)
			);
			if ($dailydatas) {
				$data['marketinfo']['sid'] = $productid;
				$data['marketinfo']['nowOpen'] = $dailydatas->ClosingPrice;
				$data['marketinfo']['HighestPrice'] = $dailydatas->HighestPrice;
				$data['marketinfo']['LowestPrice'] = $dailydatas->LowestPrice;
				$data['marketinfo']['successNum'] = '';
				$closingprice = $dailydatas->ClosingPrice;
				$product = new Product();
				$products = $product->findFirst(
					array(
						'conditions' => "id = $productid",
						'columns' => 'rise,fall,title,id'
					)
				);
				$data['marketinfo']['title'] = $products->title;
				if (USER_TYPE == 'duojin' && $productid == 1) {
					$data['marketinfo']['riseStop'] = $this->getCharge($closingprice + $products->rise,'seed');
					$data['marketinfo']['fallStop'] = $this->getCharge($closingprice - $products->fall,'seed');
				} else {
					$data['marketinfo']['riseStop'] = $this->getCharge($closingprice * ($products->rise + 1),'rise');
					$data['marketinfo']['fallStop'] =  $this->getCharge($closingprice * (1 - $products->fall),'fall');
				}
//					$data['marketinfo']['riseStop'] = $closingprice * ($products->rise + 1);
//					$data['marketinfo']['fallStop'] = $closingprice * (1 - $products->fall);
			} else {
				$product = new Product();
				$productprice = $product->findFirst("id = $productid");
				$data['marketinfo'] = array(
					'sid' => $productid,
					'nowOpen' => $productprice->startprice,
					'HighestPrice' => '0',
					'LowestPrice' => '0',
					'successNum' => '0',
				);
				if (USER_TYPE == 'duojin' && $productid == 1) {
					$data['marketinfo']['riseStop'] = ($productprice->startprice + $productprice->rise);
					$data['marketinfo']['fallStop'] = ($productprice->startprice - $productprice->fall);
				} else {
					$data['marketinfo']['riseStop'] = ($productprice->startprice * (1 + $productprice->rise));
					$data['marketinfo']['fallStop'] = ($productprice->startprice * (1 - $productprice->fall));
				}
//                    $order = new Order();
//                    $neworder = $order->findFirst(
//                        array(
//                            'conditions' => "status =1 AND sid= $productid AND dealnum > 0",
//                            'order' => 'endtime DESC',
//                            'limit' => '1'
//                        )
//                    );

				$data['marketinfo']['buyprice'] = $productprice->startprice;

			}

			$product = new Product();
			$products = $product->findFirst(
				array(
					'conditions' => "id = $productid",
					'columns' => 'rise,fall,title'
				)
			);
			$data['marketinfo']['title'] = $products->title;
		}
		$userproduct = new UserProduct();
		$userproducts = $userproduct->findFirst("uid = $uid and sid = $productid");
		if ($userproducts) {
			$data['marketinfo']['num'] = $userproducts->number;
		} else {
			$data['marketinfo']['num'] = '0';
		}
		$getprice = $this->getpriceAction($uid, $productid);
		$user = new User();
		$userinfo = $user->findFirst(
			array(
				'conditions' => "id = $uid",
				'columns' => 'coing'
			)
		);
		$data['marketinfo']['gold'] = $userinfo->coing;
		$data['marketinfo']['fee'] = $getprice['fee'];
		if ($data['marketinfo']['riseStop'] < 0) {
			$data['marketinfo']['riseStop'] = 0;
		}
		if ($data['marketinfo']['fallStop'] < 0) {
			$data['marketinfo']['fallStop'] = 0;
		}
		$this->ajaxResponse($data, '当日产品信息', '0');
	}

	/**
	 * 获取产品列表信息
	 */
	public function getproductlist($productId)
	{
		$today = new Dailydate();
		$td = strtotime(date("Y-m-d"));
		$yd = strtotime(date('Y-m-d', strtotime('-1 day')));
		$todayinfo = $today->findFirst("sid = $productId and Date >= $td");
		if ($todayinfo) {
			$data['marketinfo'] = array(
				'sid' => $todayinfo->sid,
				'nowOpen' => $todayinfo->OpeningPrice,
				'HighestPrice' => $todayinfo->HighestPrice,
				'LowestPrice' => $todayinfo->LowestPrice,
				'successNum' => $todayinfo->Volume,
			);
			$product = new Product();
			$productinfo = $product->find(
				array(
					'conditions' => 'status = 1',
					'columns' => 'rise,fall,id,title,startprice'
				)
			);
			$dailydata = new Dailydate();
			$orderinfo = $dailydata->findFirst(
				array(
					'conditions' => "Date < $td",
					'order'		=>"Date",
					'columns' => 'ClosingPrice',
					'limit'		=> 1
				)
			);
			if (!empty(@$orderinfo->ClosingPrice)){
				$closingprice = $orderinfo->ClosingPrice;
			}else{
				$closingprice = @$productinfo->startprice;
			}
			foreach ($productinfo as $key => $value) {
				if ($value->id == $data['marketinfo']['sid']) {
					if ($value->id == 1 && USER_TYPE == 'duojin'){
						$data['marketinfo']['riseStop'] = $closingprice + $value->rise ;
						$data['marketinfo']['fallStop'] = $closingprice - $value->fall;
					}else{
						$data['marketinfo']['riseStop'] = $closingprice * ($value->rise + 1);
						$data['marketinfo']['fallStop'] = $closingprice * (1 - $value->fall);
					}
					$data['marketinfo']['title'] = $value->title;
				}
			}
//			$order = new Order();
//			$currentprice = $order->findFirst(
//				array(
//					'conditions' => "status = 1 and sid = $productId",
//					'columns' => 'sid,price',
//					'order' => 'endtime DESC',
//				)
//			);
			$data['marketinfo']['buyprice'] = $todayinfo->ClosingPrice;
		} else {
			$dailydata = new Dailydate();//获取日数据中的最高最低价和数量还有关市价格
			$dailydatas = $dailydata->findFirst(
				array(
					'conditions' => "sid = $productId and Date <= $yd",
					'columns' => 'HighestPrice,LowestPrice,Volume as successNum,ClosingPrice',
					'order'	 =>'Date DESC',
					'limit'	 => '1'
				)
			);
			if ($dailydatas) {
				$data['marketinfo'] = array(
					'sid' => $productId,
					'nowOpen' => $dailydatas->ClosingPrice,
					'HighestPrice' => $dailydatas->HighestPrice,
					'LowestPrice' => $dailydatas->LowestPrice,
					'successNum' => $dailydatas->successNum,
				);
				$closingprice = $dailydatas->ClosingPrice;
				foreach ($dailydatas as $key => $value) {
					if (@$value->id == $data['marketinfo']['sid']) {
						$data['marketinfo']['riseStop'] = $closingprice * ($value->rise + 1);
						$data['marketinfo']['fallStop'] = $closingprice * (1 - $value->fall);
					}
				}
				$data['marketinfo']['buyprice'] = $dailydatas->ClosingPrice;
			} else {
				$product = new Product();
				$productinfo = $product->findFirst("id = $productId");
				$data['marketinfo'] = array(
					'sid' => $productId,
					'nowOpen' => $productinfo->startprice,
					'HighestPrice' => $productinfo->startprice,
					'LowestPrice' => $productinfo->startprice,
					'successNum' => '0',
				);
				$risestop = ($productinfo->rise + 1);
				$fallstop = ($productinfo->fall + 1);
				$data['marketinfo']['riseStop'] = $productinfo->startprice * $risestop;
				$data['marketinfo']['fallStop'] = $productinfo->startprice * $fallstop;
			}
			$product = new Product();
			$products = $product->findFirst(
				array(
					'conditions' => "id = $productId",
					'columns' => 'rise,fall,title'
				)
			);
			$data['marketinfo']['title'] = $products->title;

//			$neworder = new Order();//获取订单表中的最新价格
//			$price = $neworder->findFirst(
//				array(
//					'conditions' => "sid = $productId and status = 1 and dealnum > 0 and type = 0",
//					'columns' => 'price',
//					'order' => 'endtime DESC',
//					'limit' => '1'
//				)
//			);

//			if ($price) {
//			} else {
//				$data['marketinfo']['buyprice'] = '0';
//			}
		}
		if (empty($data['marketinfo']['buyprice'])){
			$data['marketinfo']['buyprice'] = $this->getstarPrice($productId);
		}
		if($productId ==1){
			$data['marketinfo']['title'] = "种子";
		}
		return $data;

	}
	//获取产品初始价格
	public function getProductPrice($productId){
		$product = Product::findFirst("id = $productId");
		if ($product){
			return $product->startprice;
		}
		return 0;
	}

	public function getProductName($sid){
		$product = Product::findFirst("id = $sid");
		$productName = $product->title;
		return $productName;
	}
	//手续费超过四位进一
	public function getCharge($charge,$type = ''){
		$charges = explode('.',$charge);
		if (!empty($charges[1])){
			if (strlen($charges[1])>=6){
				if(strlen($charges[1])>=6){
					if ($type == 'rise'){
						$charge = $charges[0].'.'.substr($charges[1],0,4);
					}elseif($type == 'fall'){
						$charge = $charges[0].'.'.substr($charges[1],0,4)+0.0001;
					}elseif($type == 'seed'){
						$charge = $charges[0].'.'.substr($charges[1],0,5)+0.00001;
					}elseif ($type == 'trade'){
						$charge = $charges[0].'.'.substr($charges[1],0,4)+0.0001;
					}
				}
				return $charge;
			}else{
				return $charge;
			}
		}else{
			return $charge;
		}
	}
	//获取产品的涨停和跌停
	public function getStopPrice($productId){
		$yd = strtotime(date("Y-m-d",time())."-1 day");
		$dailydate = Dailydate::findFirst(
			array(
				'conditions'	=> "sid = $productId and Date<=$yd",
				'order'			=>'Date DESC',
				'limit'			=>	1
			)
		);
		if ($dailydate){
			$closePrice = $dailydate ->ClosingPrice;
		}else{
			$closePrice = Product::findFirst("id = $productId")->startprice;
		}
		$product = Product::findFirst("id = $productId");
		if (USER_TYPE == 'duojin'&&$productId == 1){
			$riseStop = $closePrice + 0.0003;
			$fallStop = $closePrice - 0.0003;
		}else{
			$riseStop = $closePrice * (1+$product->rise);
			$fallStop = $closePrice * (1-$product->fall);
		}
		if ($productId >1){
			$data['riseStop'] = $this->getCharge($riseStop,'rise');
			$data['fallStop'] = $this->getCharge($fallStop,'fall');
		}else{
			$data['riseStop'] = $this->getCharge($riseStop,'seed');
			$data['fallStop'] = $this->getCharge($fallStop,'seed');
		}
		if (!empty($data)){
			return $data;
		}else{
			return 0;
		}
	}
	//获取手续费
	public function getCharges($charge){
		$charges = explode('.',$charge);
		if (!empty($charges[1])){
			if (strlen($charges[1])>=6){
				if(strlen($charges[1])>=6){
					$charge = $charges[0].'.'.substr($charges[1],0,5) + 0.00001;
				}
				return $charge;
			}else{
				return $charge;
			}
		}else{
			return $charge;
		}
	}

	public function getstarPrice($sid){
		$td = strtotime(date("Y-m-d"));
		$tadayinfo = Dailydate::findFirst("sid = $sid AND Date >= $td");
		if (!empty($tadayinfo)){
			$openPrice = $tadayinfo->OpeningPrice;
		}else {
			$product = Product::findFirst("id  = $sid");
			$openPrice = $product->startprice;
		}
		return $openPrice;
	}

//	//自动撮合交易
//	public function trade(){
//		$uid = $this->uid;
//		$sid  = $this->request->getPost('sid');
//		$orderinfo =  Order::findFirst(
//			[
//				'conditions'=>"uid = $uid AND sid  = $sid AND status = 0",
//				'order'		 =>'createtime ASC',
//				'limit'		 => '1',
//			]
//		);
//		if ($orderinfo){
//			$price = $orderinfo->price;
//			$type = $orderinfo->type;
//			$num = ($orderinfo->number - $orderinfo->dealnumber);
//			$orderList = $this->getLowPrice($sid,$price,$type);
//			$order = array();
//			if ($orderList){
//				foreach ($orderList as $key=>$value){
//					$order[] = [
//						'id' => $value->id,
//						'uid'=>$value->uid,
//						'sid' => $value->sid,
//						'number' => $value->number,
//						'price' => $value->price,
//						'dealnum' => $value->dealnum
//					];
//				}
//				for ($i = 0; $i<count($order);$i++){
//					if ($num<=0){
//
//					}
//				}
//			}
//		}
//
//	}
//	//获取符合条件的订单
//	public function getLowPrice($sid,$price,$type){
//		//1买 0卖
//		if ($type === '1'){
//			$orderinfo  = Order::find(
//				[
//					'conditions' =>"sid = {$sid} AND price <= {$price} AND status <>1 AND type = 0",
//					'columns' => 'id,sid,number,price,dealnum,uid',
//					'order' => 'price ASC'
//				]
//			);
//		}elseif ($type === '0'){
//			$orderinfo = Order::find(
//				[
//					'conditions' => " price >= $price and type = 1 and sid =$sid and status <>1",
//					'columns' => 'id,sid,number,price,dealnum,uid',
//					'order' => 'createtime ASC'
//				]
//			);
//		}
//		return $orderinfo;
//	}

	public function RevokeOrderAction(){
		//1 买 0 卖
		$BuyorderList = Order::find(
			[
				'conditions'=> 'status = 0 AND type = 1',
			]
		);
		$sellorderList = Order::find(
			[
				'conditions'=>'status = 0 AND type = 0'
			]
		);
		foreach ($BuyorderList as $key=>$value){
			$Buyorders[] =array(
				'id'=>$value->id,
				'uid'=>$value->uid,
				'sid'=>$value->sid,
				'number'=>($value->number-$value->dealnumber),
				'price'=>$value->price,
			);
		}
		if (!empty($Buyorders)){
			$this->RevokeBuy($Buyorders);
		}
		foreach ($sellorderList as $key=>$value){
			$sellorders[] = array(
				'id'=>$value->id,
				'uid'=>$value->uid,
				'sid'=>$value->sid,
				'number'=>($value->number-$value->dealnumber),
				'price'=>$value->price,

			);
		}
		if (!empty($sellorders)){
			$this->RevokeSell($sellorders);
		}
	}
	public function RevokeBuy($list){
		foreach ($list as $key=>$value){
			$this->db->begin();
			$orderPrice = ($value['price']*($value['number']));
			$orderinfo = Order::findFirst("id = {$value['id']}");
			//修改订单状态
			$orderinfo->status = 1;
			$orderinfoResult = $orderinfo->update();
			$userinfo = User::findFirst("id = {$value['uid']}");
			$userinfo->frozen -= $orderPrice;
			$userinfo->coing += $orderPrice;
			$userinfoResult = $userinfo->update();
			if ($userinfoResult&&$orderinfoResult){
				$this->db->commit();
				$this->saveTradeLogs(["uid=>{$value['uid']}","num"=>$orderPrice,"log"=>'系统退回订单增加金币'.$orderPrice]);
				$this->saveTradeLogs(["uid=>{$value['uid']}","num"=>$orderPrice,"log"=>'系统退回订单减少冻结金币'.$orderPrice]);
			}else{
				$this->db->rollback();
				var_dump($value['id']);die;
			}
		}
	}
	public function RevokeSell($list){
		foreach ($list as $key=>$value){
			$this->db->begin();
			$fnum = $value['number'];
			$orderinfo = Order::findFirst("id = {$value['id']}");
			$orderinfo->status = 1;
			$orderinfoResult = $orderinfo->update();
			$userinfo = UserProduct::findFirst("uid = {$value['uid']} AND sid = {$value['sid']}");
			if ($userinfo){
				$userinfo->number += $fnum;
				$userinfo->Frozen -= $fnum;
				$userProductResult = $userinfo->update();
			}
			if ($orderinfoResult&&$userProductResult){
				$this->db->commit();
				$this->saveTradeLogs(["uid=>{$value['uid']}","num"=>$fnum,"log"=>'系统退回订单增加果实'."{$value['sid']}-".$fnum]);
				$this->saveTradeLogs(["uid=>{$value['uid']}","num"=>$fnum,"log"=>'系统退回订单减少冻结果实'."{$value['sid']}-".$fnum]);
			}else{
				$this->db->rollback();
				var_dump($value['id']);die;
			}
		}
	}
	//检查提交的价格是否正确
	public function checkPrice($price,$sid){
		$k = explode('.',$price);
		if ($sid === 1){
			if (!empty($k[1])){
				if (strlen($k[1])>=6){
					if(strlen($k[1])>=6){
						$k = $k[0].'.'.substr($k[1],0,5);
					}
					return $k;
				}else{
					return $price;
				}
			}else{
				return $price;

			}
		}else{
			if (!empty($k[1])){
				if (strlen($k[1])>=5){
					if(strlen($k[1])>=5){
						$k = $k[0].'.'.substr($k[1],0,4);
					}
					return $k;
				}else{
					return $price;
				}
			}else{
				return $price;
			}
		}
	}
}
