<?php

namespace Dhc\Modules\Webapi\Controllers;

use Dhc\Models\Config;
use Dhc\Models\Slide;
use Dhc\Models\UserService;

class ConfigController extends ControllerBase
{
	protected function initialize() {
		$this->view->disable();
	}

	public function slideAction() {
		if($this->request->get()){
			$a = explode('/',$this->request->get('_url'));
			if($a[3] == 'slide') {
				$slide = new Slide();
				$data = $slide->find(
					array(
						'conditions' => 'status = 1',
						'columns' => 'title,address',
						'status' => '0'
					)
				);
				$this->ajaxResponse($data,'可用幻灯片','0');
			}
		}
	}

	public function infoAction() {
		echo "front-info";

	}
	public function serviceAction(){
		$service = UserService::find(
			array(
				'conditions'=>"type = 0",
				'columns'	=>'title,way,type'
			)
		);
		foreach ($service as $key=>$value){
			$data['service'][] = array(
				'title' =>$value->title,
				'way'	=>$value->way,
				'type'	=>$value->type,
			);
		}
		$services = UserService::find(
			array(
				'conditions'=>"type = 2",
				'columns'	=>'title,way,type'
			)
		);
		foreach ($services as $value){
			$data['kefu'][] = array(
				'title' =>$value->title,
				'way'	=>$value->way,
				'type'	=>$value->type,
			);
		}
		$services = UserService::find(
			array(
				'conditions'=>"type = 3",
				'columns'	=>'title,way,type'
			)
		);
		foreach ($services as $value){
			$data['rexian'][] = array(
				'title' =>$value->title,
				'way'	=>$value->way,
				'type'	=>$value->type,
			);
		}
		if (!empty($service)){
			$this->ajaxResponse($data,'联系方式','0');
		}else{
			$this->ajaxResponse('','暂无设置','1');
		}
	}

	public function configInfoAction(){
		$configinfo = Config::findFirst("key='copyright'");
		$gameinfo = Config::findFirst("key='game'");
		$withdraw = Config::findFirst("key='withdraw'");
		$images = Config::findFirst("key = 'images'");
		if ($configinfo){
			$data['info'] = unserialize($configinfo->value);
		}
		if ($gameinfo) {
			$data['gameinfo'] = unserialize($gameinfo->value);
		}
		if (!empty($withdraw)) {
			$data['withdraw'] = unserialize($withdraw->value);
		}
		if (!empty($images)){
			$data['images'] = unserialize($images->value);
		}
		if(USER_TYPE == "kk"){
			$other = Config::findFirst("key = 'other'");
			$data['other'] =!empty($other)?unserialize($other->value):"";
		}
		$data['user_type'] = USER_TYPE;
		$this->ajaxResponse($data, '公司信息', '0');
		if (empty($data)){
			$this->ajaxResponse('','尚未设置','1');
		}
	}

}

