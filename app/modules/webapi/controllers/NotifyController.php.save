<?php
namespace Dhc\Modules\Webapi\Controllers;
use Dhc\Library\PayResult;
use Dhc\Library\YpayResult;
use Dhc\Models\Recharge;
use Dhc\Models\UserConfig;

class NotifyController extends ControllerBase{
	public $inputData;
	public $p1_MerId;
	public function initDataAction(){

		if (!$this->request->isGet()){
			$this->inputData = file_get_contents('php://input');
			$data = json_decode($this->inputData,true);
			$result = $this->xml_parser1($this->inputData);
			if (!$result){
				$this->checkData($data);
			}else{
				$this->PayLog($this->inputData);
				$this->wftcheckData($this->inputData);
			}
		}else{
			$data = !empty()$this->request->get() ?;
			$this->PayLog($data);
			if (!empty($data)){
				$this->inputData = $data;
				$this->YcheckData($this->inputData);
			}
		}
	}
	public function checkData($data){
		if (!empty($data) && !empty($data['out_trade_no'])) {
			if(!empty($data) && is_array($data)){
				if($data['return_code'] == '01' && $data['result_code'] == '01'){
					$data['transaction_id'] = $data['channel_trade_no'];
					$data['out_trade_no'] = $data['terminal_trace'];
							$pay = new PayResult($data);
							$resPay = $pay->start();
						//$pay = new PayResult($data);
						//$resPay = $pay->start();
						if ($resPay) {
							$this->PayLog('支付成功！');
							exit('success');
						} else {
							$this->PayLog('支付失败！!');
						}
				}
				exit('error');
			}
			$this->PayLog($data);
	}}
	public function wftcheckData($data){
		if (!empty($data)){
			$xml = $this->xmlToArray2($data);
		}
		if (!empty($xml)&&isset($xml['result_code'])){
				if ($xml['result_code'] == 0 &&$xml['status'] == 0){
					$sign = $this->isTenpaySign($xml);
					if ($sign){
						$pay = new PayResult($xml);
						$resPay = $pay->start();
						//$pay = new PayResult($data);
						//$resPay = $pay->start();
						if ($resPay) {
							$this->PayLog('支付成功！'.$xml['total_fee']);
						} else {
							$this->PayLog('支付失败！!');
						}
					}else{
						$this->PayLog('非法参数');
					}
					exit(json_encode(array(
						'return_code' => isset($xml['return_code'])?$xml['return_code']:'02',
						'return_msg' => isset($xml['return_msg'])?$xml['return_msg']:'未收到信息'
					)));
				}else{
					$this->PayLog('失败1');
				}
		}else{
			$this->PayLog('失败');
		}
	}

	public function YcheckData($data){

		if (!empty($data['r1_Code']&&$data['r1_Code'] == 1)){
			$pay = new YpayResult($data);
			$resPay = $pay->start();
			if ($resPay){
				$this->PayLog('支付成功');
				//echo '<script>alert("支付成功");window.location.href="/web/#/userCenter/"</script>>';
			}else{
				if (isset($data['r9_BType'])&&$data['r9_BType'] == 2){
					exit('success');
				}else{
					$this->PayLog('支付失败');
					echo '<script>window.location.href="/web/#/userCenter/"</script>>';
				}
			}
		}
	}
	function xml_parser1($str){
		$xml_parser = xml_parser_create();
		if(!xml_parse($xml_parser,$str,true)){
			xml_parser_free($xml_parser);
			return false;
		}else {
			return true;
		}
	}

	/**
	 * 将xml转换成数组
	 * @param $xmlData
	 * @return array
	 */
	public function xmlToArray2($xml) {
		$xml = simplexml_load_string($xml);
		//获取xml编码
		$ret = preg_match ("/<?xml[^>]* encoding=\"(.*)\"[^>]* ?>/i", $xml, $arr);
		if($ret) {
			$encode = strtoupper ( $arr[1] );
		} else {
			$encode = "";
		}
		if($xml && $xml->children()) {
			foreach ($xml->children() as $node){
				//有子节点
				if($node->children()) {
					$k = $node->getName();
					$nodeXml = $node->asXML();
					$v = substr($nodeXml, strlen($k)+2, strlen($nodeXml)-2*strlen($k)-5);
				} else {
					$k = $node->getName();
					$v = (string)$node;
				}
				if($encode!="" && $encode != "UTF-8") {
					$k = iconv("UTF-8", $encode, $k);
					$v = iconv("UTF-8", $encode, $v);
				}
				$parameters[$k] = $v;
			}
		}
		return $parameters;
	}
	//获取签名
	private function getSign($initData){
		if ($initData){
			$signPars = "";
			ksort($initData);
			$this->PayLog($initData);
			$info = UserConfig::findFirst("payType = 'wft'");
			foreach($initData as $k => $v) {
//				if("" != $v && "sign" != $k) {
					$signPars .= $k . "=" . $v . "&";
//				}
			}
			$signPars .= 'key='.$info->access_token;
			$sign = strtoupper(md5($signPars));
			return $sign;
		} else {
			exit($initData['message']);
		}
	}

	/**
	 *是否签名,规则是:按参数名称a-z排序,遇到空值的参数不参加签名。
	 *true:是
	 *false:否
	 */
	function isTenpaySign($initData) {
		$signPars = "";
		ksort($initData);
		$info = UserConfig::findFirst("payType = 'wft'");
		foreach($initData as $k => $v) {
			if("sign" != $k && "" != $v) {
				$signPars .= $k . "=" . $v . "&";
			}
		}
		$signPars .= "key=" . $info->access_token;

		$sign = strtolower(md5($signPars));
		$tenpaySign = strtolower($initData["sign"]);
		return $sign == $tenpaySign;

	}


}



