<?php
namespace Dhc\Modules\Webapi\Controllers;

use Dhc\Component\VerifyImage;
use Dhc\Library\IDCardApi;
use Dhc\Library\Sms;
use Dhc\Models\Config;
use Dhc\Models\Dailydate;
use Dhc\Models\Orchard;
use Dhc\Models\TelMessage;
use Dhc\Models\User;
use Dhc\Models\UserLog;
use Phalcon\Mvc\Model\Message;

class AuthController extends ControllerBase
{
	protected function initialize()
	{
		$this->view->disable();
	}
	public function loginAction() {
		if ($this->request->isPost()) {
			if ($this->request->getPost('type') == 'login') {
				$imgCode = $this->request->getPost('imgCode');
				if (!empty($imgCode)){
					$this->getImgAction($imgCode);
				}else{
					$this->ajaxResponse('error','图片验证码不能为空');
				}
				$user = trim($this->request->getPost('user'));
				if (USER_TYPE != 'nong'){
					if (strlen($user) != 11) {
						$this->ajaxResponse('error', '请输入十一位手机号', '1');
					}
					$isMatched = $this->checkMobil($user);
					if (!$isMatched) {
						$this->ajaxResponse('error', '手机号未正确填写');
					}
				}
				$pass = $this->request->getPost('password');
				$userlogin = new User();
				$result1 = $userlogin->findFirst("user = '$user' and status = 1 ");
				if (!$result1) {
					$this->ajaxResponse('', $user . '用户不存在或已被禁止访问，请与管理员联系', '1');
				}
				$salt = $result1->salt;
				$password = sha1($salt . $pass);
				if ($password !== $result1->password) {
					$this->ajaxResponse('', '密码错误', '1');
				}
				if ($result1) {
					$userInfo = User::findFirst("user = '$user'");
					$info = json_encode($userInfo->toArray());
					$userlogin = new UserLog();
					$userlogin->user  = $this->request->getPost('user');
					$userlogin->ip = $this->request->getClientAddress();
					$userlogin->logintime = TIMESTAMP;
					$userlogin->uid = $userInfo->id;
					$userlogin->info = $info;
					$userlogin->save();
					$uid =$result1->id;
					$user = new User();
					$salt = $user->findFirst("id = $uid");
					$token = $this->createToken($uid,TIMESTAMP);
					$data =array(
						'uid'=>$uid,
						'lasttime'=>TIMESTAMP,
						'token'=>$token
					);
					$salt->token = $token;
					$salt->lasttime = TIMESTAMP;
					$loginResult = $salt->update();
					if ($loginResult){
						$this->session->set('uid', $result1->id);
						$this->session->set('token',$token);
						$this->session->set('logintime',TIMESTAMP);
						$this->ajaxResponse($data, '登录成功', '0');
					}
				}
			} elseif ($this->request->getPost('type') == 'register') {
				$imgCode = $this->request->getPost('imgCode');
				if (!empty($imgCode)){
					$this->getImgAction($imgCode);
				}else{
					$this->ajaxResponse('error','图片验证码不能为空','1');
				}
				$user = trim($this->request->getPost('user'));
				if (empty($user)) {
					$this->ajaxResponse('', '帐号不能为空', '1');
				}
				if (USER_TYPE != 'nong'){
					if (strlen($user) != 11) {
						$this->ajaxResponse('error', '请输入十一位手机号', '1');
					}
					$isMatched = $this->checkMobil($user);
					if (!$isMatched) {
						$this->ajaxResponse('error', '手机号未正确填写','1');
					}
				}else{
					$userResult = $this->checkUserName($user);
					if ($userResult == false){
						$this->ajaxResponse('error','注册失败，帐号中不可包含汉字','1');
					}
				}
				$password = $this->request->getPost('password');
				if (strlen($password) < 6) {
					$this->ajaxResponse('', '密码不能小于6位', '1');
				}
				if ($this->request->getPost('rePassword') != $password) {
					$this->ajaxResponse('', '两次密码不一致请重新输入', '1');
				}
				if (!empty($this->request->get('ref'))){
					$spreadId = $this->request->get('ref');
				}
				if (!empty($this->request->getPost('superior'))){
					$spreadId = $this->request->getPost('superior');
				}
				//短信发送验证码 然后继续验证
				$userRegister = new User();
				$result1 = $userRegister->findFirst("user = '$user'");
				if ($result1) {
					$this->ajaxResponse('', '用户已存在', '1');
				}
				$salt = $this->create_salt(4);
				if (USER_TYPE != 'nong'){
					$telmessage = $this->request->getPost('telCode');
					if (empty($telmessage)){$this->ajaxResponse('','短信验证码不能为空','1');}
					$telm = new TelMessage();
					$tmessage = $telm->findFirst("mobile = '$user' and code = '$telmessage'");
					if (!$tmessage) {
						$this->ajaxResponse('error', '手机验证码填写错误', '1');
					}
				}

				// @zl 添加身份证实名认证
				if(USER_TYPE == "chuangjin" || USER_TYPE == "kk") {
					$idCardApi = new IDCardApi($this->request->getPost('realname'), $this->request->getPost('idCard'));
					if (!$idCardApi->check()) {
						$this->ajaxResponse('', '姓名和身份证不匹配，请重新填写！', '1');
					}
				}

				$server = $this->create_salt(6);
				$password = sha1($salt . $password);
				$userRegister->user = $user;
				$userRegister->salt = $salt;
				$userRegister->server = $server;
				$userRegister->password = $password;
				if (USER_TYPE != 'nong'){
					$userRegister->telphone = $user;
				}
				$userRegister->createTime = TIMESTAMP;
				$userRegister->lasttime = TIMESTAMP;
				if (!empty($spreadId) || USER_TYPE == "jinlilai"){
					$spreadInfo = User::findFirst("id = $spreadId");
					if (!$spreadInfo){
						$this->ajaxResponse('','推荐导师不存在','1');
					}
					$userRegister->superior = $spreadInfo->id.'-'.$spreadInfo->superior;
				}else{
                    $userRegister->superior = 0;
                }
				$result = $userRegister->save();
				if (!$result) {
					foreach ($userRegister->getMessages() as $message) {
						$info[] = $message;
					}
					$this->ajaxResponse($info, '注册失败', '1');
				} else {
					$spreadInfo = User::findFirst("user = '$user'");
					$spreadInfo->spread = $spreadInfo->id;
					$token = $this->createToken($spreadInfo->id,TIMESTAMP);
					$spreadInfo->token = $token;
					$spreadInfo->telphone = $user;
					$registertResult = $spreadInfo->update();
					if(USER_TYPE =='taojin'){
					    $this->getRecommondGift($spreadId);
                    }
					if ($registertResult){
						$this->ajaxResponse('success', '注册成功', '0');
					}
				}
			} elseif ($this->request->getPost('type') == 'restPwd') {
				$mobile = $this->request->getPost('user');
				if (empty($mobile)) {
					$this->ajaxResponse('', '未知的操作', '1');
				}
				$user = new User();
				$checkre = $this->checkMobil($mobile);
				if (!$checkre) {
					$this->ajaxResponse('', '请输入正确的手机号', '');
				}
				$restuser = $user->findFirst("user = '$mobile'");
				if (!$restuser) {
					$this->ajaxResponse('', '用户尚未注册', '');
				}
				if (USER_TYPE != 'nong'){
					$telmessage = $this->request->getPost('telCode');
					$telm = new TelMessage();
					$tmessage = $telm->findFirst(
						array(
							'conditions' =>"mobile = $mobile and code = '$telmessage'"
						)
					);
					if (!$tmessage) {
						$this->ajaxResponse('error', '手机验证码填写错误', '1');
					}else{
						if ($this->request->getPost('password')){
							$restPwd = $this->request->getPost('password');
							$repassword = $this->request->getPost('rePassword');
							if ($restPwd != $repassword){
								$this->ajaxResponse('','两次密码不一致','1');
							}
							$salt = $restuser->salt;
							$repassword = sha1($salt . $restPwd);
							$restuser->password = $repassword;
							$restsult = $restuser->update();
							if ($restsult) {
								$this->ajaxResponse('success', '修改成功', '0');
							} else {
								$this->ajaxResponse('error', '修改失败', '');
							}
						}
						$this->ajaxResponse('success','手机验证码正确'.'0');
					}
				}else{
					if ($this->request->getPost('password')){
						$restPwd = $this->request->getPost('password');
						$repassword = $this->request->getPost('rePassword');
						if ($restPwd != $repassword){
							$this->ajaxResponse('','两次密码不一致','1');
						}
						$salt = $restuser->salt;
						$repassword = sha1($salt . $restPwd);
						$restuser->password = $repassword;
						$restsult = $restuser->update();
						if ($restsult) {
							$this->ajaxResponse('success', '修改成功', '0');
						} else {
							$this->ajaxResponse('error', '修改失败', '');
						}
					}
				}
			}else{
				$this->ajaxResponse('','未知的操作','1');
			}
		}

	}

	public function gettelAction() {
		$imgCode = $this->request->getPost('imgCode');
		if (!empty($imgCode)) {
			$this->getImgAction($imgCode);
		} else {
			$this->ajaxResponse('error', '图片验证码不能为空', '1');
		}
		$user = intval(trim($this->request->getPost('user')));
		if (empty($user)){
			$uid = $this->session->get('uid');
			$userinfo = User::findFirst("id = $uid");
			$user = $userinfo->user;
		}
		$type = $this->request->getPost('type');
		if ($type == 'register' || $this->request->getPost('telType') == 'register') {
			$userRegister = new User();
			$result1 = $userRegister->findFirst("user = $user");
			if ($result1) {
				$this->ajaxResponse('', '用户已存在', '1');
			}
		} elseif ($type == 'restPwd') {
			$userRest = new User();
			$result1 = $userRest->findFirst("user = $user");
			if (!$result1) {
				$this->ajaxResponse('', '用户不存在', '1');
			}
		}
		$mobile = $this->request->getPost('user');
		if (empty($mobile)){
			$uid = $this->session->get('uid');
			$userinfo = User::findFirst("id = $uid");
			$mobile = $userinfo->telphone;
			if (empty($mobile)){
				$this->ajaxResponse('error','短信发送失败,请绑定手机号');
			}
		}
		$content = "";
		$salt = $this->create_salt(4);
		if (USER_TYPE == 'duojin'){
			$config = $this->getOpenMessage();
			if ($config == false){
				$this->ajaxResponse('error','短信发送失败，请联系管理员','1');
			}
		}else{
			$configs = Config::findFirst("key= 'message'");
			if (empty($configs)){
				$this->ajaxResponse('error','短信发送失败，请联系管理员','1');
			}
			$config = unserialize($configs->value);
		}
		$telType = $this->request->getPost('telType');
		if ($config['type'] == 'message'){
			if (!empty($config['sign'])){
				$sign = $config['sign'];
			}else{
				$sign = '';
			}
			if ($telType){
				if(in_array($telType, ['register', 'restPhone', 'give', 'restPwd', 'freePay', 'withdraw','payPassword'])){
					if (USER_TYPE == 'huangjin'){
						$content  ="尊敬的黄金农场用户您好，您的验证码是".$salt.'，请不要向他人泄露验证码，如有问题请致电************【'.$sign.'】';
					}else{
						$content ='您的验证码是：' .$salt. '，请不要把短信验证码泄露给他人，如非本人操作可不用理会!【'.$sign.'】';
					}
				}else{
					$this->ajaxResponse('error', '接口参数有误', '1');
				}
			}
			$telMessageTime = TelMessage::findFirst(['conditions' => "mobile = $user", 'columns' => "sendTime"]);
			if ($telMessageTime) {
				if ($telMessageTime->sendTime + 60 > TIMESTAMP){
					$this->ajaxResponse('', '请勿频繁请求，稍后再试', '1');
				}
			}
			$message = new MessageController();
			$mess = $message->sendAction($mobile, $content);
		}elseif ($config['type'] == 'jh'){
			$mess = Sms::send($mobile,['code'=>$salt],$config);
		}
		if ($mess === true) {
			$telMessage = new TelMessage();
			$telMessage->mobile = $mobile;
			$telMessage->sendTime = TIMESTAMP;
			$telMessage->code = $salt;
			$telMessage->info = $content;
			$telMessage->status = 1;
			$telMessage->save();
			$data[] = array('info' => 'success');
			$this->ajaxResponse($data, '发送成功', '0');
		} else {
			$data[] = array('info' => $mess);
			$this->ajaxResponse($data, '发送失败', '1');
		}
	}


	public function exitAction(){
		$result = $this->session->destroy();
		if ($result){
			$this->ajaxResponse('','退出成功','0');
		}
	}
	public function checkUserName($str){
		if (preg_match("/([\x81-\xfe][\x40-\xfe])/", $str, $match)) {
			return false;
		} else {
			return true;
		}
	}
	//验证图片验证码
//	public function checkImg(){
//		$imgCode = $this->request->getPost('imgCode');
//		if(empty($imgCode)){
//			$this->ajaxResponse('error','验证码为空','1');
//		}
//		$result =  $check->checkVerify($imgCode);
//		if ($result == false){
//			$this->ajaxResponse('error','验证码错误','1');
//		}else{
//			$this->ajaxResponse('success','验证码正确','0');
//		}
//	}

}
