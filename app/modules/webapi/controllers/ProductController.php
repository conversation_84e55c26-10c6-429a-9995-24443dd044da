<?php
namespace Dhc\Modules\Webapi\Controllers;

use Dhc\Models\Dailydate;
use Dhc\Models\Order;
use Dhc\Models\Product;

class ProductController extends ControllerBase
{
	//产品列表
	public function listAction()
	{
		$product = new Product();
		$productlist= $product->find(
			array(
				'columns' =>'id,thumb'
			)
		);
		$order = new Order();
		$data = [];
		foreach ($productlist as $key=>$value){
			$data[] =array(
				'sid'	=>	$value->id,
				'thumb'	=>	$value->thumb
			);
		}
		foreach ( $data as $key=>$value) {
			//mt 最近成交时间
			$sid = $value['sid'];
			$num = $order::find(
				array(
					'columns'=>'sum(number) as num ,sid,goods,price,max(endtime) as mt',
					'conditions'=>"sid = $sid ",
					'group'	 =>'sid'
				)
			);
			foreach ($num as $k=>$v){
				if($value['sid'] == $v->sid){
					$arr[] = [
						'sid'=>$value['sid'],
						'num'=>$v->num,
					];
				}
			}
		}
		var_dump($arr);
		//数据包含产品id 图片 名字
	}
	//提交订单
	//测试添加虚假数据
	public function adddateAction()
	{
		$arr = file_get_contents('data.json');
		$arr = json_decode($arr);
		$i = 0;
		foreach ($arr as $key=>$value){
			foreach ($value as $k=>$v){
				$dailydate = new Dailydate();
				$dailydate->Date = $v->Date;
				$dailydate->sid = 2;
				$dailydate->OpeningPrice = $v->OpeningPrice;
				$dailydate->ClosingPrice = $v->ClosingPrice;
				$dailydate->HighestPrice = $v->	HighestPrice;
				$dailydate->LowestPrice  = $v->LowestPrice;
				$dailydate->Volume		 =	$v->Volume;
				$result = $dailydate->save();
				$i++;
				if(!$result){
					foreach ($dailydate->getMessages() as $message){
						echo $message.'<br>';
					}
					var_dump($i);
					die;
				}
			}
		}
		die;

	}

}
