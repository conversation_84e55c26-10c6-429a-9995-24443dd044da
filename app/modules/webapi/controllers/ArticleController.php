<?php
namespace Dhc\Modules\Webapi\Controllers;
use function Dhc\Func\Common\object2array;
use Phalcon\Paginator\Adapter\Model as PaginatorModel;
use Dhc\Models\Article;
use Dhc\Models\ArticleRead;

class ArticleController extends ControllerBase
{
		public function listAction()
		{   $cid = intval($this->request->get('cid'));
			$title = $this->request->get('type');
			if(empty($cid)){
				$this->ajaxResponse('', "文章ID不存在", '1');
			}
			if($this->request->get()){
				$article = new Article();
				if($this->request->get('page')){
					$p = $this->request->get('page');
				}else{
					$p = 1;
				}
				if($this->request->get('num')){
					$num  = $this->request->get('num');
				}else{
					$num  = 5;
				}
				if ($this->request->get('ord')){
					$ord = $this->request->get('ord');
				}else {
					$ord = " DESC ";
				}
				if($cid){
					$articlelist= $article->find(
						array(
							'conditions' => "cid = $cid",
							'column'=>'id,title,description,thumb,`from`,updateTime,rewardproduct,type',
							'order'	=> ' updateTime '.$ord
						)
					);
				}else{
					$articlelist = $article->find(
						array(
							'column'=>'id,title,description,thumb,`from`,updateTime,rewardproduct,type',
							'order'=>' updateTime '.$ord
						)
					);
				}

				$paginator = new PaginatorModel(
						array(
							"data" =>$articlelist,
							"limit"=>$num,
							"page" =>$p
						)
				);
				$page = $paginator->getPaginate();
					if(!empty($page->items)){
						if(!empty($this->uid)){
							$items = object2array($page->items);
							foreach ($items as $k=>&$v){
								$read = new ArticleRead();
								$readInfo = $read->findFirst("uid='{$this->uid}' AND aid='{$v['id']}'");
								if(!empty($readInfo) && $readInfo->status == 1){
									$v['readStatus'] = 1;
								}else{
									$v['readStatus'] = 0;
								}
							}
							$page->items =$items;
						}
						$data['total_page'] = $page->total_pages;
						$data['list'] = $page->items;
						$this->ajaxResponse($data,'文章列表','0');
					} else {
						$this->ajaxResponse('null','暂无文章列表','1');
					}
				}

		}
	    public  function detialAction(){
	    	if($this->request->get('id')){
	    		$id = $this->request->get('id');
				$article = new Article();
				$data= $article->find(
					array(
						'conditions' => "id = $id",
						'column'=>'id,title,content,thumb,`from`,updatetime',
					)
				);
				if(!empty($data)){
					$article = Article::findFirst("id = $id");
					$article->readTimes += 1;
					$article->update();
					if(USER_TYPE == "kk" && !empty($this->uid)){
						$this->updateArticleRead($id,$article);
					}
					$this->ajaxResponse($data,'文章详情','0');
				}else{
					$this->ajaxResponse('null','暂无数据','1');
				}
			}
		}
		function updateArticleRead($id,$article){
	    	$info = json_encode(array("title"=>$article->title,"content"=>$article->content),JSON_UNESCAPED_UNICODE);
	    	$articleRead = new ArticleRead();
	    	$one = $articleRead->findFirst("uid='{$this->uid}' AND aid='{$id}'");
	    	if(empty($one)){
			    $articleRead->uid = $this->uid;
			    $articleRead->aid = $id;
			    $articleRead->info = $info;
			    $articleRead->readtime = TIMESTAMP;
			    $articleRead->createtime = TIMESTAMP;
			    $articleRead->status = 1;
			    $articleRead->save();
		    }else{
				$one->info = $info;
				$one->readtime = TIMESTAMP;
			    $one->status = 1;
			    $one->update();
		    }
		    return true;
		}

}
