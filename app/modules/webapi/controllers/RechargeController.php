<?php
namespace Dhc\Modules\Webapi\Controllers;
use Dhc\Library\Native;
use Dhc\Library\fuiouPay;
use Dhc\Library\Payment;
use Dhc\Library\WxH5Pay;
use Dhc\Library\WftNative;
use Dhc\Library\QHNative;
use Dhc\Library\TKNative;
use Dhc\Library\WPNative;
use Dhc\Library\yeepay\Pay;
use Dhc\Library\AliNative;
use Dhc\Models\Recharge;
use Dhc\Models\User;
use Dhc\Models\UserConfig;

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017/4/21
 * Time: 9:22
 */
class RechargeController extends ControllerBase{
/**
 * @pram uid 用户ID
 * @pram total_fee 支付金额
 * @pram payType 支付类型
*/
	public function payUrlAction(){
		$uid = $this->uid;
		if (empty($uid )){
			$this->ajaxResponse('error','支付发起失败，支付用户错误','1');
		}else{
			$user = User::findFirst("id = $uid");
			if (!$user){$this->ajaxResponse('error','支付用户错误','1');}
		}
		$money = $this->request->get('number');
		if (!empty($money)){
			if ($money % 10 == 0){
				$this->ajaxResponse('error','充值数量不能为10的整数倍','1');
			}elseif ($money > 20000){
				$this->ajaxResponse('error','单笔充值不能超过20000金币','1');
			}elseif ($money === '0'){
				$this->ajaxResponse('error','单笔充值不能为0','1');
			}elseif ($money <10){
			    $this->ajaxResponse('error','单笔充值金额错误','1');
            }
		}
		$payType = $this->request->get('payType');
		if (empty($payType)){$this->ajaxResponse('error','未选择支付类型','1');}
		if ($payType=='wechet'){
			$type = '020';
		}elseif ($payType=='qq'){
			$type='060';
		}elseif ($payType=='alipay') {
			$type = '020';
		}elseif($payType == "WeChat") {
			$type = '012';
		}else{
			$this->ajaxResponse('error','支付类型错误','1');
		}

		$recharge = new Recharge();//020 微信  060 qq钱包  020 支付宝
		$recharge->uid = $uid;
		$orderNumber = $this->createOrderNumber($uid,'CZ');
		$recharge->orderNumber = $orderNumber;
		$recharge->number =$money;
		$recharge->payType = $payType;
		$recharge->createTime = TIMESTAMP;
		$result  = $recharge->save();
		if (!$result){
			foreach ($recharge->getMessages() as $message){
				echo  $message;
			}die;
		}
		$orderInfo = array(
			'uid'	=>$uid,
			'payType'=>$payType,
			'total_fee'=>$money,
			'createTime'=>TIMESTAMP,
			'orderNumber'=>$orderNumber
		);
		if ($result){
			// 读取支付信息
			$payChannel = UserConfig::findFirst([
				"status = 1",
				'order' => 'id ASC'
			]);
			if (empty($payChannel)) {
				$this->ajaxResponse('error', '支付通道暂未开通，暂时无法充值', '1');
			}
			$this->merchant_no = $payChannel->merchant_no;
			$this->terminal_id = $payChannel->terminal_id;
			$this->access_token = $payChannel->access_token;

			$data  = array(
				'pay_ver'=>'100',
				'pay_type'=>$payType,
				'service_id'=>'011',
				'merchant_no'=>$this->merchant_no,//商户号数据库获取	858400203000022
				'terminal_id'=>$this->terminal_id,//终端号 数据库获取 例如 123
				'terminal_trace'=>$orderNumber,//终端流水号
				'terminal_time'=>date("YmdHis",TIMESTAMP),
				'operator_id'=>'01',//操作员id
				'total_fee'	=>$money * 100,//$money*100,//$money$money*100
				'order_body'	=>'金币充值',
				'notify_url'	=>'http://'.$_SERVER['HTTP_HOST'].'/wapi/Notify/initData',
				'attach'		=>'1',
				'access_token'=>$this->access_token,
			);

			if ($payChannel->payType == 'fuyou') {
				if ($type == "012") {
					$pay = new fuiouPay($data, $uid);
					$re = $pay->pay();
					if ($re["code"] == 1) {
						$this->ajaxResponse('', $re["info"], '1');
					} else {
						$this->ajaxResponse($re["info"], '用户支付信息', '0');
					}
				} else {
					$alipayConfig = UserConfig::findFirst("payType = 'alipay' AND status = 1");
					$wxh5Config = UserConfig::findFirst("payType = 'wxh5' AND status = 1");
					if(!empty($alipayConfig) && $payType == "alipay"){
						$payChannel->payType = "alipay";
						$data["type"] = $payType;
						$data["token"] = $this->request->get('token');
						$pay = new AliNative();
						$qr_code = $pay->run($data);
					}elseif(!empty($wxh5Config) && $payType == "wxh5"){
                        $payChannel->payType = "wxh5";
                        $data["type"] = $payType;
                        $data["token"] = $this->request->get('token');
                        $pay = new WxH5Pay();
                        $qr_code = $pay->run($data);
                    }else{
						$pay = new Native($data);
						$qr_code = $pay->pay();
					}
				}
			} else if($payChannel->payType == 'zny'){
                $data["total_fee"] = $data["total_fee"] / 100;
                $data["type"] = $payType;
                $pay = new \Dhc\Library\zny\Pay($data);
                $qr_code = $pay->run();
            } else if ($payChannel->payType == 'wft') {
				$pay = new WftNative($data);
				$qr_code = $pay->pay();
			} else if ($payChannel->payType == 'YB') {
				$pay = new Pay($data);
				$qr_code = $pay->run();
			} else if($payChannel->payType == 'QH'){
				$data["type"] = $payType;
				$data["token"] = $this->request->get('token');
				$pay = new QHNative();
				$qr_code = $pay->run($data);
			}else if($payChannel->payType == 'TK'){
				$data["type"] = $payType;
				$data["token"] = $this->request->get('token');
				$pay = new TKNative();
				$qr_code = $pay->run($data);
			}else if($payChannel->payType == 'WP') {
				$data["type"] = $payType;
				$data["token"] = $this->request->get('token');
				$pay = new WPNative();
				$qr_code = $pay->run($data);
			}else if($payChannel->payType == "alipay"){
				$data["type"] = $payType;
				$data["token"] = $this->request->get('token');
				$pay = new AliNative();
				$qr_code = $pay->run($data);
			}else if($type=="020" && $payChannel->payType == "wxh5"){
                $data["type"] = $payType;
                $data["token"] = $this->request->get('token');
                $pay = new WxH5Pay();
                $qr_code = $pay->run($data);
            }
			if (!empty($qr_code)){
				$orderInfo['qr_code'] = $qr_code;
				$orderInfo['payType'] = $payChannel->payType;
				$this->ajaxResponse($orderInfo,'用户支付信息','0');
			}else{
				$this->ajaxResponse('','支付参数错误，请联系管理员1','1');
			}
		}else{
			$this->ajaxResponse('error','订单提交失败','1');
		}
		$this->view->setVar('payType',$payType);

	}


	public function rechargeListAction(){
			$uid  =$this->uid;
		if (!empty($uid)){
			$userRechager = Recharge::find(
				array(
					'conditions'=>"uid =$uid AND payStatus = 1 ",
					'order'		 =>'createTime DESC',
					'limit'		=>'10'
				)
			);
			if (!empty($userRechager)){
				$this->ajaxResponse($userRechager,'用户成功充值记录','0');
			}else{
				$this->ajaxResponse('','该用户暂无相关充值记录','1');
			}
		}
	}

	public function erweima($data)
	{
		require_once APP_PATH . '/common/library/phpqrcode/phpqrcode.php';
		$errorLevel = "L";
//定义生成图片宽度和高度;默认为3
		$size = "20";
//定义生成内容
//调用QRcode类的静态方法png生成二维码图片//
		$k = rand(1,999999999);
		$time = date("YmdHis",TIMESTAMP);
		if (!is_dir(WEB_PATH . '/code')) {
			mkdir(WEB_PATH . '/code');
		}
		if (!file_exists(WEB_PATH . "/code/$time.$this->uid.png")) {
			\QRcode::png($data['qr_code'], WEB_PATH . "/code/$time.$this->uid.png", $errorLevel, $size);
		}
		$data['qr_code'] = 'http://' . $_SERVER['HTTP_HOST']."/code/$time.$this->uid.png";
		$this->ajaxResponse($data,'用户支付信息','0');
	}


}


