<?php
/**
 * Created by PhpStorm.
 * User: XingBin
 * Date: 2017/7/26
 * Time: 15:49
 */
namespace Dhc\Modules\Webapi\Controllers;

use Dhc\Library\YBNative;
use Dhc\Models\Recharge;

class YrechargeController extends ControllerBase{
	public function PayAction(){
		$price = $this->request->getPost('price');
		$type  = $this->request->getPost('payType');
		$list = ['wechet'=>'weixin','alipay'=>'alipay','qqpay'];
		if (!in_array($list[$type],$list)){
			$this->ajaxResponse('error','充值失败，支付类型错误','1');
		}
		if ($price<= 0 ){
			$this->ajaxResponse('error','充值失败,支付金额错误','1');
		}
		if (!empty($price)){
			if ($price % 10 == 0){
				$this->ajaxResponse('error','充值数量不能为10的整数倍','1');
			}elseif ($price > 20000){
				$this->ajaxResponse('error','单笔充值不能超过20000金币','1');
			}elseif ($price <10){
				$this->ajaxResponse('error','单笔充值金额错误','1');
			}
		}
		$data['p3_Amt'] = $price;
		$data = [
			'p2_Order'=>$this->createOrderNumber($this->uid,'CZ'),
			'p3_Amt'  => $price,
			'p5_Pid'  => 'GoldRecharge',
			'p6_Pcat' =>'gold',
			'p7_Pdesc'=>'gold',
			'p8_Url'  =>'http://'.$_SERVER['HTTP_HOST'].'/wapi/Notify/initData',
			'pa_MP'  =>'goldRecharge',
			'pd_FrpId'  =>$list[$type],
		];
		$recharge = new Recharge();//020 微信  060 qq钱包  020 支付宝
		$recharge->uid = $this->uid;
		$orderNumber = $data['p2_Order'];
		$recharge->orderNumber = $orderNumber;
		$recharge->number =$price;
		$recharge->payType = $type;
		$recharge->createTime = TIMESTAMP;
		$result  = $recharge->save();
		if (!empty($result)){
			$info = new YBNative($data);
			$result = $info->payRequest();
			if (empty($result) || $result == 'null'){
				$this->ajaxResponse('error','操作失败，请联系管理员','1');
			}
			$orderInfo = array(
				'uid'			=>$this->uid,
				'payType'		=>$type,
				'total_fee'		=>$price,
				'createTime'	=>TIMESTAMP,
				'orderNumber'	=>$orderNumber,
				'qr_code'		=>$result ? $result : ''
			);
//			if (!empty($orderInfo['qr_code'])){
//				$orderInfo['qr_code'] = $this->erweima($result);
//			}
			$this->ajaxResponse($orderInfo,'请求参数返回数据',0);
		}else{
			$this->ajaxResponse('','操作失败，用户订单生成失败','1');
		}
	}
	public function erweima($data)
	{
		require_once APP_PATH . '/common/library/phpqrcode/phpqrcode.php';
		$errorLevel = "L";
//定义生成图片宽度和高度;默认为3
		$size = "20";
//定义生成内容
//调用QRcode类的静态方法png生成二维码图片//
		$k = rand(1,999999999);
		$time = date("YmdHis",TIMESTAMP);
		if (!is_dir(WEB_PATH . '/code')) {
			mkdir(WEB_PATH . '/code');
		}
		if (!file_exists(WEB_PATH . "/code/$time.$this->uid.png")) {
			\QRcode::png($data['qr_code'], WEB_PATH . "/code/$time.$this->uid.png", $errorLevel, $size);
		}
		$data['qr_code'] = 'http://' . $_SERVER['HTTP_HOST']."/code/$time.$this->uid.png";
		return $data['qr_code'];
	}


}
