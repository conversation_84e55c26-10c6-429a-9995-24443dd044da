<?php
namespace Dhc\Modules\Webapi\Controllers;
use Dhc\Library\PayResult;
use Dhc\Library\yeepay\YeePayMPay;
use Dhc\Library\YpayResult;
use Dhc\Models\Recharge;
use Dhc\Models\UserConfig;

class NotifyController extends ControllerBase{
	public $inputData;
	public $p1_MerId;
	public function initDataAction(){

		if (!$this->request->isGet()){
			$this->inputData = file_get_contents('php://input');
			$data = json_decode($this->inputData,true);
			$result = $this->xml_parser1($this->inputData);
			if (!$result){
				$this->checkData($data);
			}else{
				$this->PayLog($this->inputData);
				$this->wftcheckData($this->inputData);
			}
		}else{
			$data = !empty($this->request->get()) ? $this->request->get() : $this->request->getPost();
			$this->PayLog($data);
			if (!empty($data)){
				$this->inputData = $data;
				$this->YcheckData($this->inputData);
			}
		}
	}
	public function qhAction(){
		$memberid = $this->request->getPost('memberid', 'string', '')?$this->request->getPost('memberid', 'string', ''):$this->request->get('memberid', 'string', '');
		$orderid = $this->request->getPost('orderid', 'string', '')?$this->request->getPost('orderid', 'string', ''):$this->request->get('orderid', 'string', '');
		$amount = $this->request->getPost('amount', 'string', '')?$this->request->getPost('amount', 'string', ''):$this->request->get('amount', 'string', '');
		$datetime = $this->request->getPost('datetime', 'string', '')?$this->request->getPost('datetime', 'string', ''):$this->request->get('datetime', 'string', '');
		$returncode = $this->request->getPost('returncode', 'string', '')?$this->request->getPost('returncode', 'string', ''):$this->request->get('returncode', 'string', '');
		$sign = $this->request->getPost('sign', 'string', '')?$this->request->getPost('sign', 'string', ''):$this->request->get('sign', 'string', '');
		$transaction_id = $this->request->getPost('transaction_id', 'string', '')?$this->request->getPost('transaction_id', 'string', ''):$this->request->get('transaction_id', 'string', '');
		if (empty($memberid) || empty($orderid) || empty($amount) || empty($datetime) || empty($returncode) || empty($sign) || empty($transaction_id)) {
			echo "参数不正确！";
			exit;
		}
		$userConfig = UserConfig::findFirst("payType = 'QH' AND status = 1");
		if (empty($userConfig->merchant_no) || empty($userConfig->access_token) || $userConfig->merchant_no != $memberid) {
			echo '支付参数没有配置';
			exit;
		}
		$data = array(
			"memberid"=>$memberid,
			"orderid"=>$orderid,
			"amount"=>$amount,
			"datetime"=>$datetime,
			"returncode"=>$returncode,
			"transaction_id"=>$transaction_id,
		);
		ksort($data);
		reset($data);
		$md5str = "";
		foreach ($data as $key => $val) {
			$md5str = $md5str . $key . "=" . $val . "&";
		}
		$data["sign"] = $sign;
		$signStr = strtoupper(md5($md5str . "key=" . $userConfig->access_token));
		$this->PayLog($data,'支付信息！!');
		if ($sign != $signStr || $returncode != "00") {
			$this->PayLog($signStr,'支付失败！!');
			exit("fail");
		}
		$data = [];
		$data['transaction_id'] = $transaction_id;
		$data['out_trade_no'] = $orderid;
		$pay = new PayResult($data);
		$resPay = $pay->start();
		$this->PayLog($resPay,'支付状态！!');
		if ($resPay) {
			echo 'ok';
			exit;
		} else {
			echo 'fail';
			exit;
		}
	}
	public function tkAction(){
		$orderid = $this->request->get('orderid');
		$ovalue = $this->request->get('ovalue');
		$sysorderid = $this->request->get('sysorderid');
		$opstate = $this->request->get('opstate');
		$sign = $this->request->get('sign');
		$systime = $this->request->get('systime', 'string', '');
		if (empty($orderid) || empty($ovalue) || empty($sysorderid) || empty($sign)) {
			echo "参数不正确！";
			exit;
		}
		$userConfig = UserConfig::findFirst("payType = 'TK' AND status = 1");
		if (empty($userConfig->merchant_no) || empty($userConfig->access_token)) {
			echo '支付参数没有配置';
			exit;
		}
		$data = array(
			"orderid"=>$orderid,
			"opstate"=>$opstate,
			"ovalue"=>$ovalue,
			"sign" => $sign,
		);
		$str = "orderid=".$orderid."&opstate=".$opstate."&ovalue=".$ovalue.$userConfig->access_token;
		$signStr = md5($str);
		$this->PayLog($data,'支付信息！!');
		if ($sign != $signStr || $opstate != "0") {
			$this->PayLog($signStr,'支付失败！!');
			exit("fail");
		}
		$data = [];
		$data['transaction_id'] = $sysorderid;
		$data['out_trade_no'] = $orderid;
		$pay = new PayResult($data);
		$resPay = $pay->start();
		$this->PayLog($resPay,'支付状态！!');
		if ($resPay) {
			echo 'SUCCESS';
			exit;
		} else {
			echo 'fail';
			exit;
		}
	}
	public function wpAction(){
		$this->PayLog(1,'支付失败！!');
		$cpparam = $this->request->getPost('cpparam')?$this->request->getPost('cpparam'):$this->request->get('cpparam');//商户参数/商户订单号
		$orderNo = $this->request->getPost('orderNo')?$this->request->getPost('orderNo'):$this->request->get('orderNo');
		$price = $this->request->getPost('price')?$this->request->getPost('price'):$this->request->get('price');
		$status = $this->request->getPost('status')?$this->request->getPost('status'):$this->request->get('status');
		$sign = $this->request->getPost('sign')?$this->request->getPost('sign'):$this->request->get('sign');
		$synType = $this->request->getPost('synType')?$this->request->getPost('synType'):$this->request->get('synType');
		$time = $this->request->getPost('time')?$this->request->getPost('time'):$this->request->get('time');
		if (empty($cpparam) || empty($orderNo) || empty($price) || empty($status) || empty($sign) || empty($synType) || empty($time)) {
			echo "参数不正确！";
			exit;
		}
		$userConfig = UserConfig::findFirst("payType = 'WP' AND status = 1");
		if (empty($userConfig->terminal_id) || empty($userConfig->access_token)) {
			echo '支付参数没有配置';
			exit;
		}
		$data = array(
			"cpparam" => $cpparam,
			"orderNo" => $orderNo,
			"price" => $price,
			"status" => $status,
			"synType" => $synType,
			"time" => $time
		);
		$str = "cpparam=".$cpparam."&orderNo=".$orderNo."&price=".$price."&status=".$status."&synType=".$synType."&time=".$time.$userConfig->access_token;
		$signStr = strtoupper(md5($str));
		$this->PayLog($data,'支付信息！!');
		if ($sign != $signStr || $status != "success") {
			$this->PayLog($signStr,'支付失败！!');
			exit("fail");
		}
		$data = [];
		$data['transaction_id'] = $orderNo;
		$data['out_trade_no'] = $cpparam;
		$pay = new PayResult($data);
		$resPay = $pay->start();
		$this->PayLog($resPay,'支付状态！!');
		if ($resPay) {
			echo 'success';
			exit;
		} else {
			echo 'fail';
			exit;
		}
	}
	public function ybAction()
	{
		$request = $this->request;
		$data = $request->get('data', 'string', '');
		$encryptkey = $request->get('encryptkey', 'string', '');
		if (empty($data) || empty($encryptkey)) {
			echo "参数不正确！";
			exit;
		}

		$userConfig = UserConfig::findFirst("payType = 'YB' AND status = 1");
		if (empty($userConfig->merchant_no) || empty($userConfig->other)) {
			echo '支付参数没有配置';
			exit;
		}
		$userConfig->other = json_decode($userConfig->other, true);
		// 商户编号
		$merchantaccount = $userConfig->merchant_no;
		// 商户私钥
		$merchantPrivateKey = $userConfig->other['merchantPrivateKey'];
		// 商户公钥
		$merchantPublicKey = $userConfig->other['merchantPublicKey'];
		// 易宝公钥
		$yeepayPublicKey = $userConfig->other['yeepayPublicKey'];

		$yeepay = new YeePayMPay($merchantaccount, $merchantPublicKey, $merchantPrivateKey, $yeepayPublicKey);
		$result = $yeepay->callback($data, $encryptkey);

		$data = [];
		$data['transaction_id'] = $result['yborderid'];
		$data['out_trade_no'] = $result['orderid'];
		$pay = new PayResult($data);
		$resPay = $pay->start();
		if ($resPay) {
			echo '支付成功';
			exit;
		} else {
			echo '支付失败';
			exit;
		}
	}

	/*
	 * 支付宝回调
	 */
	public function alipayAction(){
		$request = $this->request->getPost();
		$this->PayLog($request,'支付宝支付参数！!');
		if (empty($request)) {
			echo "参数不正确！";
			exit;
		}

		$userConfig = UserConfig::findFirst("payType = 'alipay' AND status = 1");
		if (empty($userConfig->merchant_no) || empty($userConfig->other)) {
			echo '支付参数没有配置';
			exit;
		}
		$userConfig->other = json_decode($userConfig->other, true);
		// 商户公钥
		$merchantPublicKey = $userConfig->other['merchantPublicKey'];
		$sign = !empty($request["sign"])?$request["sign"]:"";
		ksort($request);
		$str = '';
		foreach ($request as $k => $v) {
			if (empty($v) || $k == "sign" || $k == "sign_type" || $k == "type" || "@" == substr($v, 0, 1)) {
				continue;
			} else {
				$str .= "&" . $k . "=" . urldecode($v);
			}
		}
		unset ($k, $v);
		$str = trim($str, '&');
		$res = $this->rsaSignCheck($merchantPublicKey,$str,$sign);
		if($res == false){
			exit("签名验证失败");
		}
		if(empty($request["trade_status"]) || !@in_array($request["trade_status"],array("TRADE_SUCCESS","TRADE_FINISHED"))){
			exit("接口返回业务结果为结果");
		}

		$data = [];
		$data['transaction_id'] = $request['trade_no'];
		$data['out_trade_no'] = $request['out_trade_no'];
		$pay = new PayResult($data);
		$resPay = $pay->start();
		if ($resPay) {
			exit("success");
		} else {
			exit("fail");
		}
	}
	/*
	 * 支付宝处理跳转
	 */
	public function alipayreturnAction(){
		$request = $this->request->get();
		$this->PayLog($request,'支付宝通知支付参数！!');
		if (empty($request) || empty($request['out_trade_no']) || empty($request["return_token"])) {
			echo "参数不正确！";
			exit;
		}

		$userConfig = UserConfig::findFirst("payType = 'alipay' AND status = 1");
		if (empty($userConfig->merchant_no) || empty($userConfig->other)) {
			echo '支付参数没有配置';
			exit;
		}
		$recharge = Recharge::findFirst("orderNumber = '{$request['out_trade_no']}'");
		if(empty($recharge)){
			exit("订单记录不存在");
		}
		$locationUrl = 'http://' . $_SERVER['HTTP_HOST']."/web/?token=".$request["return_token"]."#/userCenter/";
		if ($recharge->uid) {
			header("Location:".$locationUrl);
			exit();
		} else {
			exit("fail");
		}
	}
	/**
	 * RSA 验签
	 * @param $data
	 * @param $sign
	 * @return bool
	 */
	private function rsaSignCheck($pubKey,$data, $sign) {
		$pubKey = "-----BEGIN PUBLIC KEY-----\n" . wordwrap($pubKey, 64, "\n", true) . "\n-----END PUBLIC KEY-----";
		$res = openssl_get_publickey($pubKey);
		$result = openssl_verify($data, base64_decode($sign), $res);
		openssl_free_key($res);
		return (bool) $result;
	}


	public function checkData($data){
		if (!empty($data) && !empty($data['out_trade_no'])) {
			if(!empty($data) && is_array($data)){
				if($data['return_code'] == '01' && $data['result_code'] == '01'){
					$data['transaction_id'] = $data['channel_trade_no'];
					$data['out_trade_no'] = $data['terminal_trace'];
							$pay = new PayResult($data);
							$resPay = $pay->start();
						//$pay = new PayResult($data);
						//$resPay = $pay->start();
						if ($resPay) {
							$this->PayLog('支付成功！');
							exit('success');
						} else {
							$this->PayLog('支付失败！!');
						}
				}
				exit('error');
			}
			$this->PayLog($data);
	}}
	public function wftcheckData($data){
		if (!empty($data)){
			$xml = $this->xmlToArray2($data);
		}
		if (!empty($xml)&&isset($xml['result_code'])){
				if ($xml['result_code'] == 0 &&$xml['status'] == 0){
					$sign = $this->isTenpaySign($xml);
					if ($sign){
						$pay = new PayResult($xml);
						$resPay = $pay->start();
						//$pay = new PayResult($data);
						//$resPay = $pay->start();
						if ($resPay) {
							$this->PayLog('支付成功！'.$xml['total_fee']);
						} else {
							$this->PayLog('支付失败！!');
						}
					}else{
						$this->PayLog('非法参数');
					}
					exit(json_encode(array(
						'return_code' => isset($xml['return_code'])?$xml['return_code']:'02',
						'return_msg' => isset($xml['return_msg'])?$xml['return_msg']:'未收到信息'
					)));
				}else{
					$this->PayLog('失败1');
				}
		}else{
			$this->PayLog('失败');
		}
	}

	public function YcheckData($data){

		if (!empty($data['r1_Code']&&$data['r1_Code'] == 1)){
			$pay = new YpayResult($data);
			$resPay = $pay->start();
			if ($resPay){
				$this->PayLog('支付成功');
				//echo '<script>alert("支付成功");window.location.href="/web/#/userCenter/"</script>>';
			}else{
				if (isset($data['r9_BType'])&&$data['r9_BType'] == 2){
					echo 'success';
				}else{
					$this->PayLog('支付失败');
					echo '<script>window.location.href="/web/#/userCenter/"</script>>';
				}
			}
		}
	}
	function xml_parser1($str){
		$xml_parser = xml_parser_create();
		if(!xml_parse($xml_parser,$str,true)){
			xml_parser_free($xml_parser);
			return false;
		}else {
			return true;
		}
	}

	/**
	 * 将xml转换成数组
	 * @param $xmlData
	 * @return array
	 */
	public function xmlToArray2($xml) {
		$xml = simplexml_load_string($xml);
		//获取xml编码
		$ret = preg_match ("/<?xml[^>]* encoding=\"(.*)\"[^>]* ?>/i", $xml, $arr);
		if($ret) {
			$encode = strtoupper ( $arr[1] );
		} else {
			$encode = "";
		}
		if($xml && $xml->children()) {
			foreach ($xml->children() as $node){
				//有子节点
				if($node->children()) {
					$k = $node->getName();
					$nodeXml = $node->asXML();
					$v = substr($nodeXml, strlen($k)+2, strlen($nodeXml)-2*strlen($k)-5);
				} else {
					$k = $node->getName();
					$v = (string)$node;
				}
				if($encode!="" && $encode != "UTF-8") {
					$k = iconv("UTF-8", $encode, $k);
					$v = iconv("UTF-8", $encode, $v);
				}
				$parameters[$k] = $v;
			}
		}
		return $parameters;
	}
	//获取签名
	private function getSign($initData){
		if ($initData){
			$signPars = "";
			ksort($initData);
			$this->PayLog($initData);
			$info = UserConfig::findFirst("payType = 'wft'");
			foreach($initData as $k => $v) {
//				if("" != $v && "sign" != $k) {
					$signPars .= $k . "=" . $v . "&";
//				}
			}
			$signPars .= 'key='.$info->access_token;
			$sign = strtoupper(md5($signPars));
			return $sign;
		} else {
			exit($initData['message']);
		}
	}

	/**
	 *是否签名,规则是:按参数名称a-z排序,遇到空值的参数不参加签名。
	 *true:是
	 *false:否
	 */
	function isTenpaySign($initData) {
		$signPars = "";
		ksort($initData);
		$info = UserConfig::findFirst("payType = 'wft'");
		foreach($initData as $k => $v) {
			if("sign" != $k && "" != $v) {
				$signPars .= $k . "=" . $v . "&";
			}
		}
		$signPars .= "key=" . $info->access_token;

		$sign = strtolower(md5($signPars));
		$tenpaySign = strtolower($initData["sign"]);
		return $sign == $tenpaySign;

	}


    /*
     * 智能云支付回调通知
     */
    public function znyAction(){
        $request = $this->request->get();
        $this->PayLog($request,'智能云支付通知支付参数！!');
        if (empty($request) || empty($request['orderid'])) {
            echo "参数不正确！";
            exit;
        }

        $userConfig = UserConfig::findFirst("payType = 'zny'");
        if (empty($userConfig->merchant_no) || empty($userConfig->access_token)) {
            echo '支付参数没有配置';
            exit;
        }

        $p_id = $request["ordno"];
        $orderid = $request["orderid"];
        $price = $request["price"];
        $realprice = $request["realprice"];
        $orderuid = $request["orderuid"];
        $key = $request["key"];
        $check = md5($orderid . $orderuid . $p_id . $price . $realprice . $userConfig->access_token);
        if($key != $check){
            echo '签名错误';
            exit;
        }

        $data['out_trade_no'] = $request['orderid'];
        $pay = new PayResult($data);
        $resPay = $pay->start();
        if ($resPay) {
            $this->PayLog('支付成功！');
            exit('success');
        } else {
            $this->PayLog('支付失败！!');
        }
    }

    /**
     * 微信H5回调
     */
    public function wxh5Action()
    {
        $xml = file_get_contents('php://input');
        $data = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
        $this->PayLog($data,'微信H5支付通知支付参数!');
        if (empty($data) || empty($data['out_trade_no']))
        {
            $result = [
                'return_code' => 'FAIL',
                'return_msg' => '支付信息为空或信息不正确'
            ];
            echo $this->data_to_xml($result);
            exit;
        }
        if ($data['result_code'] != 'SUCCESS' || $data['return_code'] != 'SUCCESS')
        {
            $result = array(
                'return_code' => 'FAIL',
                'return_msg' => empty($data['return_msg']) ? $data['err_code_des'] : $data['return_msg']
            );
            echo $this->data_to_xml($result);
            exit;
        }
        $userConfig = UserConfig::findFirst("payType = 'wxh5'  AND status = 1");
        if(empty($userConfig->merchant_no) || empty($userConfig->access_token))
        {
            $result = [
                'return_code' => 'FAIL',
                'return_msg' => '支付配置信息错误'
            ];
            echo $this->data_to_xml($result);
            exit;
        }
        if($this->checkSign($userConfig->access_token,$data) != $data['sign'])
        {
            $result = [
                'return_code' => 'FAIL',
                'return_msg' => '签名失败'
            ];
            echo $this->data_to_xml($result);
            exit;
        }
        $pay = new PayResult($data);
        $resPay = $pay->start();
        if ($resPay) {
            $this->PayLog('支付成功！');
            $result = [
                'return_code' => 'SUCCESS',
                'return_msg' => ''
            ];
            echo $this->data_to_xml($result);
            exit;
        } else {
            $result = [
                'return_code' => 'FAIL',
                'return_msg' => '更新订单失败'
            ];
            echo $this->data_to_xml($result);
            exit;
        }
    }

}